"""
URL configuration for zuumm project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.1/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.http import JsonResponse
from accounts.admin_auth.views import CustomAdminLoginView
from django.contrib.auth import views as auth_views
from django.conf import settings
from django.template.loader import get_template

urlpatterns = [
    # Health check endpoint
    path("health/", lambda request: JsonResponse({"status": "ok", "message": "Service is healthy"}), name="health_check"),
    
    # Override admin login
    path('admin/login/', CustomAdminLoginView.as_view(), name='admin:login'),

    # Password reset URLs with proper configuration
    path(
        "admin/password_reset/",
        auth_views.PasswordResetView.as_view(),
        name="admin_password_reset",
    ),
    path(
        "admin/password_reset/done/",
        auth_views.PasswordResetDoneView.as_view(
            template_name='admin/password_reset_done.html',
        ),
        name="password_reset_done",
    ),
    path(
        "reset/<uidb64>/<token>/",
        auth_views.PasswordResetConfirmView.as_view(
            template_name='admin/password_reset_confirm.html',
            success_url='/reset/done/',
        ),
        name="password_reset_confirm",
    ),
    path(
        "reset/done/",
        auth_views.PasswordResetCompleteView.as_view(
            template_name='admin/password_reset_complete.html',
        ),
        name="password_reset_complete",
    ),

    # Admin URLs
    path('admin/', admin.site.urls),

    # API URLs
    path("api/", include("zuumm.api_urls")),
]
