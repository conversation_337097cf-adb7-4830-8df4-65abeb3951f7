from django.db.models import TextChoices

class UserTypeChoices(TextChoices):
    SUPER_ADMIN = "SA", "Super Admin"
    PARTNER_ADMIN = "PA", "Partner Admin"
    PARTNER_USER = "PU", "Partner User"
    ZUUMM_USER = "ZU", "Zuumm User"
    REFERRED_PARTNER = "RP", "Referred Partner"

    @classmethod
    def normal_users(cls):
        return [cls.PARTNER_USER.value, cls.ZUUMM_USER.value]

    @classmethod
    def admin_types(cls):
        return [cls.SUPER_ADMIN.value, cls.PARTNER_ADMIN.value, cls.REFERRED_PARTNER.value]

    @classmethod
    def partner_admin_types(cls):
        return [cls.PARTNER_ADMIN.value, cls.REFERRED_PARTNER.value]

# class PartnerTypeChoices(TextChoices):
#     # Zuumm Partner -> <PERSON><PERSON>umm's own
#     ZUUMM = "ZUUMM", "Zuumm"
#     # Super Partner -> Will have their own partner and own packages
#     SUPER = "SUP", "Super"
#     # Sub Partner -> Not have their own partner and use zuumm packages
#     SUB = "SUB", "Sub"

class PartnerTypeChoices(TextChoices):
    # Zuumm Partner -> Zuumm's own
    ZUUMM = "ZUUMM", "Zuumm"
    # Super Partner -> Will have their own partner and own packages
    PARTNER = "PARTNER", "Partner"


class OTPSourceChoices(TextChoices):
    PARTNER_REGISTRATION = "PARTNER_REGISTRATION", "Partner Registration"
    LOGIN_SIGNUP = "LOGIN_SIGNUP", "Login/Signup"


class ProviderChoices(TextChoices):
    """Social login provider choices"""
    GOOGLE = "GOOGLE", "Google"
    # Future providers can be added here
    # FACEBOOK = "FACEBOOK", "Facebook"
    # APPLE = "APPLE", "Apple"
    # LINKEDIN = "LINKEDIN", "LinkedIn"


class AffiliateStatusChoices(TextChoices):
    PENDING = "Pending"
    APPROVED = "Approved"
    REJECTED = "Rejected"


class TagChoices(TextChoices):
    SMART_TRAVEL = "SMART_TRAVEL", "Smart Travel"
    DESTINATIONS = "DESTINATIONS", "Destinations"
    TRIP_TYPES = "TRIP_TYPES", "Trip Types"
    TRAVEL_TOOLS = "TRAVEL_TOOLS", "Travel Tools"
    STAY_AND_EXPERIENCE = "STAY_AND_EXPERIENCE", "Stay and Experience"
    TRAVEL_BYTES = "TRAVEL_BYTES", "Travel Bytes"
    VOICES = "VOICES", "Voices"
    

class BlogTypeChoices(TextChoices):
    POST = "POST", "Post"
    PODCAST = "PODCAST", "Podcast"


class EntityTypeChoices(TextChoices):
    TRAVEL_AGENT = "TA", "Travel Agent/Tour Operators"  # Travel Agent/Tour operators
    BANK_PLATFORM = "BP", "Banks & Financial Institutions"  # Banks & financial Institutions
    RETAIL_PLATFORM = "RP", "Retail Chains and Loyalty Platforms"  # Retail chains and loyalty platforms
    INSURANCE_COMPANY = "IC", "Insurance Companies"  # Insurance companies
    EDUCATIONAL_INSTITUTION = "EI", "Educational Institutions"  # Educational Institutions
    RELIGUOUS_AND_SPIRITUAL_PLATFORM = "RSP", "Religious & Spiritual Organizations"  # Religious and spiritual Organizations
    HR_EMPLOYEE_BENEFIT_PORTAL = "HEBP", "HR/Employee Benefit Portal"  # HR/Employee benefit portal
    ECOMMERCE_PLATFORM = "ECP", "E-commerce Platforms"  # E-commerce platforms
    FREELANCER_GIG_WORKER = "FG", "Freelancer & Gig-workers"  # Freelancers & Gig-workers
    SMALL_BUSINESS_LOCAL_SHOP = "SBL", "Small Businesses & Local Shops"  # Small Businesses & local shops
    WELLNESS_CENTER_CLINIC = "WCC", "Wellness Centres & Clinics"  # Wellness Centres & clinics
    TRANSPORT_OPERATIONS = "TO", "Transport Operations (Rail, Bus, Cab)"  # Transport operators (Rail, Bus, Cab)
    OTHERS = "OT", "Others"  # Other business types not listed above


class PreferenceChoices(TextChoices):
    OWN_WEBSITE_WHITELABEL = "OW", "Own Website Whitelabel"
    INTEGRATE_WITH_MOBILE_APP = "IA", "Integrate with Mobile App"
    INTEGRATE_WITH_EXISTING_WEBSITE = "IW", "Integrate with Existing Website"
    REFERRAL_LINK = "RL", "Referral Link"


class MonthlyTransactionVolumeChoices(TextChoices):
    LESS_THAN_1L = "<1L", "< 1 L"
    BETWEEN_1L_TO_5L = "1-5L", "1-5L"
    BETWEEN_5L_TO_20L = "5-20L", "5-20L"
    GREATER_THAN_20L = ">20L", "> 20L"


class PackageTypeChoices(TextChoices):
    ZUUMM_PACKAGE = "ZP", "Zuumm Package"
    OWN_PACKAGE = "OP", "Own Package"
