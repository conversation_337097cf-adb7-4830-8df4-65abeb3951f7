"""
User management models for the accounts app.
"""
from django.contrib.auth.models import AbstractUser, BaseUserManager
from django_softdelete.models import SoftDeleteManager
from django.db import models
from django.contrib.gis.db import models as locationmodels
from django.utils.translation import gettext_lazy as _
from django.contrib.admin.models import LogEntry
from django.contrib.contenttypes.fields import GenericRelation
from django.core.validators import MinValueValidator, MaxValueValidator
from base.models import BaseModel
from base.storage_utils import (
    get_logo_path, 
    get_writer_profile_picture_path, 
    get_blog_banner_image_path,
    get_blog_audio_file_path,
)

from accounts.choices import (
    UserTypeChoices, 
    PartnerTypeChoices, 
    OTPSourceChoices,
    EntityTypeChoices, 
    PreferenceChoices, 
    MonthlyTransactionVolumeChoices,
    PackageTypeChoices,
    BlogTypeChoices,
    TagChoices,
    ProviderChoices,
    AffiliateStatusChoices,
)
from ckeditor.fields import RichTextField


class Address(BaseModel):
    """Address Model"""
    title = models.CharField(max_length=100, null=True, blank=True)
    address = models.TextField(null=True, blank=True)
    house_no = models.CharField(max_length=100, null=True, blank=True)
    city = models.CharField(max_length=100, null=True, blank=True)
    state = models.CharField(max_length=100, null=True, blank=True)
    country = models.CharField(max_length=100, default='India')
    pincode = models.CharField(max_length=6, null=True, blank=True)
    location = locationmodels.PointField(srid=4326, null=True)

    def __str__(self):
        return f"{self.title or 'Untitled'} - {self.city}, {self.country}"


class Partner(BaseModel):
    """
    Partner model for multi-partner support.
    Each partner represents a partner/organization.
    """

    entity_name = models.CharField(max_length=255, unique=True)
    entity_type = models.CharField(
        max_length=255, 
        choices=EntityTypeChoices.choices,
        default=EntityTypeChoices.OTHERS.value,
    )
    partner_type = models.CharField(
        max_length=25,
        choices=PartnerTypeChoices.choices,
        default=PartnerTypeChoices.PARTNER.value,
    )
    preference = models.CharField(
        max_length=255, 
        choices=PreferenceChoices.choices,
        default=PreferenceChoices.OWN_WEBSITE_WHITELABEL.value,
    )
    organization_url = models.CharField(max_length=255, null=True, blank=True)

    subdomain = models.CharField(max_length=255, unique=True, null=True, blank=True)
    vanity_domain = models.CharField(max_length=255, unique=True, null=True, blank=True)

    logo = models.FileField(
        upload_to=get_logo_path, 
        max_length=500,
        help_text="Partner logo image", 
        null=True, 
        blank=True
    )
    primary_theme = models.CharField(max_length=50, help_text="Partner primary theme color")
    secondary_theme = models.CharField(max_length=50, help_text="Partner secondary theme color", null=True, blank=True)

    address = models.ForeignKey(Address, on_delete=models.SET_NULL, null=True, blank=True)

    gst_number = models.CharField(max_length=15, null=True, blank=True)
    company_registration_number = models.CharField(max_length=255, null=True, blank=True)
    monthly_transaction_volume = models.CharField(
        max_length=255, 
        choices=MonthlyTransactionVolumeChoices.choices,
        default=MonthlyTransactionVolumeChoices.LESS_THAN_1L.value,
    )

    package_type = models.CharField(
        max_length=255,
        choices=PackageTypeChoices.choices,
        default=PackageTypeChoices.ZUUMM_PACKAGE.value,
    )
    facebook_media_link = models.CharField(max_length=255, null=True, blank=True)
    instagram_media_link = models.CharField(max_length=255, null=True, blank=True)
    twitter_media_link = models.CharField(max_length=255, null=True, blank=True)
    referral_code = models.CharField(max_length=255, null=True, blank=True)
    update_notification_enabled = models.BooleanField(default=False)

    log_entries = GenericRelation(LogEntry)

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "Partner"
        verbose_name_plural = "Partners"

    def __str__(self):
        return f"{self.entity_name}"


class OTP(BaseModel):
    partner = models.ForeignKey(Partner, related_name="otps", on_delete=models.CASCADE, null=True, blank=True)
    email = models.EmailField(max_length=255, null=True, blank=True)
    phone_number = models.CharField(max_length=20, null=True, blank=True)
    source = models.CharField(
        max_length=255, 
        choices=OTPSourceChoices.choices, 
        default=OTPSourceChoices.PARTNER_REGISTRATION.value
    )
    code = models.PositiveIntegerField(
        validators=[MinValueValidator(0), MaxValueValidator(999999)]
    )


class UserManager(BaseUserManager, SoftDeleteManager):
    """Custom user manager for the User model."""
    
    def _create_user(self, email, password=None, partner=None, **extra_fields):
        """
        Create and save a user with the given email, partner, and password.
        """
        if not email:
            raise ValueError('Users must have an email address')
        
        # Normalize email to lowercase
        email = self.normalize_email(email)
        
        # Remove username from extra_fields if present, as we will derive it from external_id
        extra_fields.pop('username', None)

        # Check if a user with this email exists for this partner
        if partner and self.filter(email=email, partner=partner).exists():
            raise ValueError(f'User with email {email} already exists for this partner')
        
        # Create the user instance. external_id will be auto-generated by BaseModel.
        user = self.model(email=email, partner=partner, **extra_fields)
        
        # Set the username to the string representation of external_id.
        # This ensures username is unique and tied to the BaseModel's external_id.
        user.username = str(user.external_id)
        
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_user(self, email, password=None, partner=None, **extra_fields):
        """Create and save a regular user."""
        user_type = extra_fields.get('user_type')
        if user_type and user_type == UserTypeChoices.PARTNER_ADMIN.value:
            extra_fields.setdefault('is_staff', True)
            extra_fields.setdefault('is_superuser', False)
        else:
            extra_fields.setdefault('is_staff', False)
            extra_fields.setdefault('is_superuser', False)

        return self._create_user(email, password, partner, **extra_fields)

    def create_superuser(self, email, password=None, **extra_fields):
        """Create and save a superuser."""
        partner_id = extra_fields.pop('partner', None)
        is_primary_superadmin = extra_fields.pop('is_primary_superadmin', False)
        if partner_id:
            try:
                partner = Partner.objects.get(id=partner_id)
            except Partner.DoesNotExist:
                raise ValueError(f'Partner with id {partner_id} does not exist')
        else:
            partner = Partner.objects.filter(partner_type=PartnerTypeChoices.ZUUMM.value).first()

        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        extra_fields.setdefault('is_email_verified', True)
        extra_fields.setdefault('user_type', UserTypeChoices.SUPER_ADMIN.value)
        extra_fields.setdefault('is_primary_superadmin', is_primary_superadmin)

        if extra_fields.get('is_staff') is not True:
            raise ValueError('Superuser must have is_staff=True.')
        if extra_fields.get('is_superuser') is not True:
            raise ValueError('Superuser must have is_superuser=True.')

        return self._create_user(email, password, partner, **extra_fields)


class User(BaseModel, AbstractUser):
    """
    Custom user model that supports multi-partner and role-based access control.
    """

    partner = models.ForeignKey(
        Partner,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='users',
    )
    email = models.EmailField(
        max_length=255,
        db_index=True
    )
    phone_number_country_code = models.CharField(
        max_length=10,
        null=True,
        blank=True,
        default='+91',
        verbose_name='Country code'
    )
    phone_number = models.CharField(
        max_length=20,
        null=True,
        blank=True,
        db_index=True
    )
    full_name = models.CharField(
        max_length=255,
        null=True,
        blank=True
    )

    # This is a flag to check if the superadmin user is the primary superadmin
    is_primary_superadmin = models.BooleanField(default=False)
    
    user_type = models.CharField(
        max_length=20,
        choices=UserTypeChoices.choices,
        default=UserTypeChoices.ZUUMM_USER.value
    )
    is_email_verified = models.BooleanField(default=False)
    is_social_user = models.BooleanField(default=False)
    is_affiliate = models.BooleanField(default=False)

    objects = UserManager()

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['partner']

    class Meta:
        unique_together = ('partner', 'email', 'user_type')  # Email is unique per partner
        ordering = ['-created_at']

    def __str__(self):
        partner_name = self.partner.entity_name if self.partner else 'No Partner'
        return f"{self.email} - ({self.full_name})"

    @property
    def token(self):
        """Generate JWT tokens for the user"""
        from accounts.api.v1.token_serializers import ZuummTokenObtainPairSerializer

        token = ZuummTokenObtainPairSerializer.get_token(self)

        return {
            'refresh': str(token),
            'access': str(token.access_token),
        }
    
    def invalidate_all_tokens(self):
        """Invalidate all JWT tokens for this user"""
        try:
            from rest_framework_simplejwt.token_blacklist.models import OutstandingToken
            # Get all outstanding tokens for this user and blacklist them
            tokens = OutstandingToken.objects.filter(user=self)
            for token in tokens:
                try:
                    # Blacklist the token
                    token.blacklist()
                except Exception:
                    # Token might already be blacklisted, continue
                    pass
        except ImportError:
            # Token blacklist not available
            pass


class SuperAdminUser(User):
    class Meta:
        proxy = True
        verbose_name = "Super Admin"
        verbose_name_plural = "Super Admins"


class PartnerAdminUser(User):
    class Meta:
        proxy = True
        verbose_name = "Partner Admin"
        verbose_name_plural = "Partner Admins"


class UserSocialAccount(BaseModel):
    """
    Social Account Connect
    """
    provider = models.CharField(max_length=10, choices=ProviderChoices.choices)
    user = models.OneToOneField(
        User, related_name="social_account", on_delete=models.CASCADE
    )
    token = models.TextField(null=True, blank=True)
    uid = models.TextField(null=True, blank=True)
    meta_data = models.JSONField(default=dict)

    class Meta:
        unique_together = ('provider', 'uid')
        verbose_name = "User Social Account"
        verbose_name_plural = "User Social Accounts"

    def __str__(self):
        return f"{self.user.email} - {self.provider}"


class Affiliate(BaseModel):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='affiliate')
    status = models.CharField(
        max_length=255, 
        choices=AffiliateStatusChoices.choices, 
        default=AffiliateStatusChoices.PENDING.value
    )
    affiliate_code = models.UUIDField(unique=True, null=True, blank=True)
    requested_at = models.DateTimeField(null=True, blank=True)
    approved_at = models.DateTimeField(null=True, blank=True)
    rejected_at = models.DateTimeField(null=True, blank=True)
    rejection_reason = models.CharField(max_length=255, null=True, blank=True)

    users_referred_count = models.PositiveIntegerField(default=0)

    class Meta:
        verbose_name = "Affiliate User"
        verbose_name_plural = "Affiliate Users"

    def __str__(self):
        return f"{self.user.email}"

    def update_referred_count(self):
        """Update the referred users count"""
        self.users_referred_count = self.referred_users.count()
        self.save(update_fields=['users_referred_count'])


class AffiliateReferralUser(BaseModel):
    affiliate = models.ForeignKey(Affiliate, on_delete=models.CASCADE, related_name='referred_users')
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='referred_user')

    class Meta:
        verbose_name = "Affiliate Referral"
        verbose_name_plural = "Affiliate Referrals"

    def __str__(self):
        return f"{self.user.email} - Referred by {self.affiliate.user.email}"

    def save(self, *args, **kwargs):
        """Override save to update affiliate's referred count"""
        is_new = self.pk is None
        super().save(*args, **kwargs)
        if is_new:
            self.affiliate.update_referred_count()

    def delete(self, *args, **kwargs):
        """Override delete to update affiliate's referred count"""
        affiliate = self.affiliate
        super().delete(*args, **kwargs)
        affiliate.update_referred_count()


class Tag(BaseModel):
    name = models.CharField(max_length=255, unique=True, choices=TagChoices.choices)
    slug = models.TextField(null=True, blank=True)

    class Meta:
        verbose_name = "Tag"
        verbose_name_plural = "Tags"
    
    def __str__(self):
        return self.name


class Writer(BaseModel):
    name = models.CharField(max_length=255)
    profile_picture = models.FileField(upload_to=get_writer_profile_picture_path)
    bio = models.TextField()
    email = models.EmailField(max_length=255, null=True, blank=True)

    class Meta:
        verbose_name = "Writer"
        verbose_name_plural = "Writers"
    
    def __str__(self):
        return self.name


class Blog(BaseModel):
    title = models.CharField(max_length=255)
    content = RichTextField()
    banner = models.FileField(upload_to=get_blog_banner_image_path, max_length=500)
    read_time = models.IntegerField(null=True, blank=True)
    type = models.CharField(max_length=255, choices=BlogTypeChoices.choices, default=BlogTypeChoices.POST.value)
    published_at = models.DateTimeField(null=True, blank=True)
    writer = models.ForeignKey(Writer, on_delete=models.CASCADE, related_name='blogs')
    tags = models.ManyToManyField(Tag, through='BlogTag', related_name='blogs', blank=True)
    audio_file = models.FileField(upload_to=get_blog_audio_file_path, null=True, blank=True, max_length=500)

    # Auto-generation control fields
    auto_generate_content = models.BooleanField(
        default=False,
        verbose_name='Auto-generate Content',
        help_text='Whether content was auto-generated by OpenAI'
    )
    auto_generate_slug = models.BooleanField(
        default=False,
        verbose_name='Auto-generate Slug', 
        help_text='Whether slug was auto-generated by OpenAI'
    )
    auto_generate_audio_file = models.BooleanField(
        default=False,
        verbose_name='Auto-generate Audio File',
        help_text='Whether audio file was auto-generated by OpenAI'
    )

    slug = models.TextField(unique=True)
    seo_title = models.CharField(max_length=255, null=True, blank=True)
    meta_description = models.TextField(null=True, blank=True)
    meta_keywords = models.TextField(null=True, blank=True)
    canonical_url = models.CharField(max_length=255, null=True, blank=True)
    featured_image_alt_text = models.CharField(max_length=255, null=True, blank=True)
    robots_directive = models.TextField(null=True, blank=True)
    focus_keyword = models.CharField(max_length=255, null=True, blank=True)
    meta_information = models.TextField(null=True, blank=True)

    class Meta:
        verbose_name = "Blog/Podcast"
        verbose_name_plural = "Blogs/Podcasts"

    def __str__(self):
        return self.title


class BlogTag(BaseModel):
    """
    Through model for Blog-Tag M2M relationship
    """
    blog = models.ForeignKey(Blog, on_delete=models.CASCADE, related_name='blog_tags')
    tag = models.ForeignKey(Tag, on_delete=models.CASCADE, related_name='blog_tags')
    
    def delete(self, *args, **kwargs):
        """Override delete to use hard_delete instead of soft delete"""
        self.hard_delete()
    
    def save(self, *args, **kwargs):
        """Override save to handle soft delete conflicts"""
        if not self.pk:  # Only for new instances
            # Check for existing soft-deleted records with same blog-tag pair
            existing_soft_deleted = BlogTag.global_objects.filter(
                blog=self.blog,
                tag=self.tag,
                deleted_at__isnull=False
            )
            
            # Hard delete any existing soft-deleted records
            if existing_soft_deleted.exists():
                existing_soft_deleted._raw_delete(existing_soft_deleted.db)
        
        super().save(*args, **kwargs)
    
    class Meta:
        unique_together = ('blog', 'tag')
        verbose_name = 'Blog Tag'
        verbose_name_plural = 'Blog Tags'
        
    def __str__(self):
        return f"{self.blog.title} - {self.tag.name}" 