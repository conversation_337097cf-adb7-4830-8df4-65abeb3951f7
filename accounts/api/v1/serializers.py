from rest_framework import serializers
from django.contrib.auth import get_user_model
from django.conf import settings
from accounts.models import Partner, Address, OTP, Blog, Writer, Tag, UserSocialAccount, Affiliate
from accounts.utils.code_utils import handle_affiliate_referral
from django.utils import timezone
from random import randint
from datetime import timedelta
from base.static import Constants
from accounts.choices import (
    UserTypeChoices, 
    PreferenceChoices, 
    EntityTypeChoices,
    MonthlyTransactionVolumeChoices,
    PackageTypeChoices,
    OTPSourceChoices,
    PartnerTypeChoices,
    ProviderChoices,
    AffiliateStatusChoices,
)
from accounts.utils.helper_methods import get_user_data, user_profile_complete_status, get_masked_email
from google.auth.transport import requests
from google.oauth2 import id_token as google_id_token
from django.contrib.gis.geos import Point
import uuid
import re

from base.email_utils import send_mail_task, EmailContentManager
from base.sms_utils import send_sms_task

User = get_user_model()


class AddressSerializer(serializers.ModelSerializer):
    # Make city and country required according to user story
    city = serializers.CharField(max_length=100, required=True)
    country = serializers.CharField(max_length=100, required=True)
    
    class Meta:
        model = Address
        fields = ('title', 'address', 'house_no', 'city', 'state', 'country', 'pincode', 'location')

    def to_internal_value(self, data):
        location = data.get('location')
        if location and isinstance(location, list) and len(location) >= 2:
            try:
                point = Point(location[0], location[1], srid=4326)
                data = data.copy()
                data['location'] = point
            except Exception as e:
                raise serializers.ValidationError({'location': f'Invalid coordinates: {str(e)}'})

        return super().to_internal_value(data)

    def to_representation(self, instance):
        representation = super().to_representation(instance)

        if instance.location:
            representation['location'] = [
                instance.location.x,
                instance.location.y
            ]

        return representation


class PartnerAdminSerializer(serializers.Serializer):
    full_name = serializers.CharField(max_length=255, required=True)
    email = serializers.EmailField(required=True)
    phone_number_country_code = serializers.CharField(max_length=10, default='+91')
    phone_number = serializers.CharField(max_length=20, required=True)

    def validate_email(self, value):
        # Check for existing users with this email and user_type combination
        existing_user = User.objects.filter(
            email=value, 
            user_type__in=UserTypeChoices.partner_admin_types()
        ).first()
        
        if existing_user:
            raise serializers.ValidationError(
                f"This email is already registered as a partner admin for '{existing_user.partner.entity_name}'."
            )
        return value

    def validate_phone_number(self, value):
        # Basic phone number validation - just check if it's not empty
        if not value:
            raise serializers.ValidationError("Phone number is required.")
        
        # Since we're handling multiple country phone numbers and verifying via OTP,
        # we don't need strict digit count or regex validation
        return value


class PartnerRegistrationSerializer(serializers.ModelSerializer):
    """Partner Registration Serializer"""
    address = AddressSerializer(required=True)
    partner_admin = PartnerAdminSerializer(required=True)
    
    # Required fields according to user story
    entity_name = serializers.CharField(max_length=255, required=True)
    entity_type = serializers.ChoiceField(choices=EntityTypeChoices.choices, required=True)
    
    # Choice fields with proper validation
    preference = serializers.ChoiceField(choices=PreferenceChoices.choices, required=True)
    monthly_transaction_volume = serializers.ChoiceField(choices=MonthlyTransactionVolumeChoices.choices, required=True)
    package_type = serializers.ChoiceField(choices=PackageTypeChoices.choices, required=True)
    
    # Conditional required fields based on usage preference
    logo = serializers.CharField(required=False, write_only=True)
    primary_theme = serializers.CharField(max_length=50, required=False)
    secondary_theme = serializers.CharField(max_length=50, required=False, allow_blank=True)
    subdomain = serializers.CharField(max_length=255, required=False)
    vanity_domain = serializers.CharField(max_length=255, required=False, allow_blank=True)
    
    # Optional fields
    gst_number = serializers.CharField(max_length=15, required=False, allow_blank=True)
    company_registration_number = serializers.CharField(max_length=255, required=False, allow_blank=True)
    referral_code = serializers.CharField(max_length=255, required=False, allow_blank=True)
    update_notification_enabled = serializers.BooleanField(default=False)

    class Meta:
        model = Partner
        fields = (
            'entity_name', 'entity_type', 'subdomain', 'vanity_domain', 
            'logo', 'primary_theme', 'secondary_theme', 'address', 
            'partner_admin', 'gst_number', 'company_registration_number',
            'monthly_transaction_volume', 'package_type', 'referral_code',
            'update_notification_enabled', 'preference', 'organization_url',
            'facebook_media_link', 'instagram_media_link', 'twitter_media_link',
        )

    def validate_subdomain(self, value):
        if not value:
            return value
            
        # Convert subdomain to lowercase
        value = value.lower()
        
        # Validate subdomain format: no spaces, only lowercase, numbers, and hyphens
        if not re.match(r'^[a-z0-9-]+$', value):
            raise serializers.ValidationError(
                "Subdomain can only contain lowercase letters, numbers, and hyphens. No spaces or special characters allowed."
            )
        
        if Partner.objects.filter(subdomain=value).exists():
            raise serializers.ValidationError("This domain is already registered for another partner.")
        return value

    def validate_entity_name(self, value):
        """Validate that entity name is unique"""
        if Partner.objects.filter(entity_name__iexact=value).exists():
            raise serializers.ValidationError("A partner with this entity name already exists.")
        return value

    def validate(self, data):
        """Cross-field validation based on usage preferences"""
        preference = data.get('preference')
        entity_type = data.get('entity_type')
        package_type = data.get('package_type')
        
        # Validate package_type based on entity_type
        # "Sell your own package" (OWN_PACKAGE) is only allowed for Travel Agent/Tour Operator
        if package_type == PackageTypeChoices.OWN_PACKAGE.value:
            if entity_type != EntityTypeChoices.TRAVEL_AGENT.value:
                raise serializers.ValidationError({
                    'package_type': 'Own Package option is only available for Travel Agent & Tour Operators.'
                })
        
        # Subdomain is now mandatory for all preferences except REFERRAL_LINK
        if preference != PreferenceChoices.REFERRAL_LINK.value:
            if not data.get('subdomain'):
                raise serializers.ValidationError({'subdomain': 'Subdomain is required for this preference type.'})
        
        # Check if usage preference requires certain fields
        if preference in [PreferenceChoices.OWN_WEBSITE_WHITELABEL.value, 
                               PreferenceChoices.INTEGRATE_WITH_MOBILE_APP.value,
                               PreferenceChoices.INTEGRATE_WITH_EXISTING_WEBSITE.value]:
            
            # Logo and primary theme are required for these preferences
            if not data.get('logo'):
                raise serializers.ValidationError({'logo': 'Logo is required for this usage preference.'})
            if not data.get('primary_theme'):
                raise serializers.ValidationError({'primary_theme': 'Primary partner color is required for this usage preference.'})

        return data

    def create(self, validated_data):
        # Extract nested data
        partner_admin_data = validated_data.pop('partner_admin')
        partner_address_data = validated_data.pop('address', None)
        preference = validated_data.get('preference')

        # Double-check for race conditions during creation
        entity_name = validated_data.get('entity_name')
        if Partner.objects.filter(entity_name__iexact=entity_name).exists():
            raise serializers.ValidationError({'entity_name': 'A partner with this entity name already exists.'})
        
        admin_email = partner_admin_data.get('email')
        if User.objects.filter(email=admin_email, user_type__in=UserTypeChoices.partner_admin_types()).exists():
            raise serializers.ValidationError({'partner_admin': {'email': 'This email is already registered as a partner admin.'}})

        # Create address
        address_id = None
        if partner_address_data:
            address_serializer = AddressSerializer(data=partner_address_data)
            address_serializer.is_valid(raise_exception=True)
            address_id = address_serializer.save().id

        validated_data['address_id'] = address_id
        
        # Create Partner using super
        partner = super().create(validated_data)

        # Generate random password
        admin_password = str(uuid.uuid4())[:8]

        # Determine user type based on preference
        if preference == PreferenceChoices.REFERRAL_LINK.value:
            user_type = UserTypeChoices.REFERRED_PARTNER.value
            is_staff = False  # No admin access for referred partners
        else:
            user_type = UserTypeChoices.PARTNER_ADMIN.value
            is_staff = True  # Admin access for regular partners

        # Create partner admin/referred partner user
        admin_user = User.objects.create_user(
            email=partner_admin_data['email'],
            password=admin_password,
            full_name=partner_admin_data['full_name'],
            phone_number_country_code=partner_admin_data.get('phone_number_country_code', '+91'),
            phone_number=partner_admin_data['phone_number'],
            partner=partner,
            user_type=user_type,
            is_staff=is_staff,
            is_email_verified=True  # Email should be verified for admin accounts created via partner onboarding
        )

        # Store for email context
        self.context.update({
            'admin_password': admin_password,
            'admin_user': admin_user,
            'is_referral_partner': preference == PreferenceChoices.REFERRAL_LINK.value
        })

        return partner


class OTPSerializer(serializers.ModelSerializer):
    """Generate OTP Serializer"""
    source = serializers.ChoiceField(choices=OTPSourceChoices.choices, required=True)
    email = serializers.EmailField(required=False)
    phone_number = serializers.CharField(required=False)
    step = serializers.IntegerField(required=False)

    class Meta:
        model = OTP
        fields = (
            "source",
            "email",
            "phone_number",
            "step",
        )

    def validate(self, attrs):
        """Validations before generating a new OTP"""
        email = attrs.get("email", None)
        phone_number = attrs.get("phone_number", None)
        source = attrs.get("source", None)
        step = attrs.get("step", None)

        otp_object = None
        otp_already_exists = False
        partner = self.context.get('partner')

        if not email and not phone_number:
            raise serializers.ValidationError({"email": "Email or phone number is required"})
        
        if email and phone_number:
            raise serializers.ValidationError({"email": "Only one of email or phone number is required"})
        
        if email:
            if step == 2:
                users = User.objects.filter(email=email, partner=partner)
                if users.exists():
                    raise serializers.ValidationError({"email": "A user with this email already exists"})

            otp = OTP.objects.filter(email=email, source=source, partner=partner)
        else:
            if step == 2:
                users = User.objects.filter(
                    phone_number=phone_number[-10:], 
                    partner=partner
                )
                if users.exists():
                    raise serializers.ValidationError({"phone_number": "A user with this phone number already exists"})

            otp = OTP.objects.filter(phone_number=phone_number, source=source, partner=partner)

        if otp.exists():
            otp_object = otp.first()
            otp_already_exists = True

        if source == OTPSourceChoices.PARTNER_REGISTRATION.value:
            if email:
                if User.objects.filter(email=email, user_type__in=UserTypeChoices.partner_admin_types()).exists():
                    raise serializers.ValidationError({"email": "A partner admin with this email already exists"})
            else:
                if User.objects.filter(phone_number=phone_number, user_type__in=UserTypeChoices.partner_admin_types()).exists():
                    raise serializers.ValidationError({"phone_number": "A partner admin with this phone number already exists"})

        # For LOGIN_SIGNUP source, check if user exists and is active
        elif source == OTPSourceChoices.LOGIN_SIGNUP.value:
            if email:
                existing_user = User.objects.filter(
                    email=email, 
                    user_type__in=UserTypeChoices.normal_users(),
                    partner=partner
                ).first()
                if existing_user and not existing_user.is_active:
                    raise serializers.ValidationError({"email": "Your account has been deactivated. Please contact support to login."})
            else:
                existing_user = User.objects.filter(
                    phone_number=phone_number[-10:], 
                    user_type__in=UserTypeChoices.normal_users(),
                    partner=partner
                ).first()
                if existing_user and not existing_user.is_active:
                    raise serializers.ValidationError({"phone_number": "Your account has been deactivated. Please contact support to login."})

        # If a regenerate API got hit before 1 minute - Raise Error
        if otp_already_exists and otp_object.created_at > timezone.now() - timedelta(minutes=Constants.OTP_WAIT_MINUTES) :
            raise serializers.ValidationError({"email": f"Please wait {Constants.OTP_WAIT_MINUTES} minutes before generating a new OTP"})

        if otp_already_exists:
            for otp_object in otp:
                otp_object.hard_delete()

        return attrs

    def create(self, validated_data):
        # Generate & save OTP related fields
        otp_code = f"{randint(0, 999999):06}"  # Pad OTP with leading zeros if necessary

        email = validated_data.get("email", None)
        phone_number = validated_data.get("phone_number", None)
        source = validated_data.get("source", None)
        # Get partner from context
        partner = self.context.get('partner')

        is_login = False
        is_signup = False

        context = {'otp': otp_code}
        if source == OTPSourceChoices.PARTNER_REGISTRATION.value:
            content_type = "partner_registration_otp"
        else:
            content_type = source

        if email:
            otp_objects = OTP.objects.filter(email=email, source=source, partner=partner)
            for otp_object in otp_objects:
                otp_object.hard_delete()

            otp = OTP.objects.create(
                email=email,
                source=source,
                code=otp_code,
                partner=partner
            )
            
            # Send OTP via email
            subject, body = EmailContentManager.get_email_content(content_type, context)
            send_mail_task(subject, body, [email])

        else:
            nested_content_type = None
            if source == OTPSourceChoices.LOGIN_SIGNUP.value:
                user_phone_number = phone_number[-10:]
                users = User.objects.filter(phone_number=user_phone_number, partner=partner)
                if users.exists():
                    is_signup = True
                    nested_content_type = 'SIGNUP_OTP'
                else:
                    is_login = True
                    nested_content_type = 'LOGIN_OTP'

            otp_objects = OTP.objects.filter(phone_number=phone_number, source=source, partner=partner)
            for otp_object in otp_objects:
                otp_object.hard_delete()

            otp = OTP.objects.create(
                phone_number=phone_number,
                source=source,
                code=otp_code,
                partner=partner
            )
            
            # Send OTP via SMS
            success, result = send_sms_task(phone_number, content_type, context, nested_content_type)

        return otp


class VerifyOTPSerializer(serializers.ModelSerializer):
    """Serializer to Verify OTP using validate method"""

    otp = serializers.IntegerField(required=True, max_value=999999, min_value=0)
    email = serializers.EmailField(required=False)
    phone_number = serializers.CharField(required=False)
    source = serializers.ChoiceField(choices=OTPSourceChoices.choices, required=True)

    class Meta:
        model = OTP
        fields = (
            "otp",
            "email",
            "phone_number",
            "source",
        )

    def validate(self, attrs):
        email = attrs.get("email", None)
        phone_number = attrs.get("phone_number", None)
        otp_code = attrs.get("otp", None)
        source = attrs.get("source", None)
        # Get partner from context
        partner = self.context.get('partner')

        # Check if the OTP is for signup or login
        is_signup = False
        is_login = False

        # VALID OTP BOOLEAN
        is_otp_valid = False 

        if not email and not phone_number:
            raise serializers.ValidationError({"email": "Email or phone number is required"})

        if email and phone_number:
            raise serializers.ValidationError({"email": "Only one of email or phone number is required"})

        if email:
            otp_objects = OTP.objects.filter(email=email, source=source, partner=partner)
            if not otp_objects.exists():
                raise serializers.ValidationError({"email": "OTP does not exist"})

        else:
            otp_objects = OTP.objects.filter(phone_number=phone_number, source=source, partner=partner)
            if not otp_objects.exists():
                raise serializers.ValidationError({"phone_number": "OTP does not exist"})

        if source == OTPSourceChoices.LOGIN_SIGNUP.value:
            if email:
                users = User.objects.filter(
                    email=email, 
                    user_type__in=UserTypeChoices.normal_users(), 
                    partner=partner
                )
                if users.exists():
                    # Check if user is active
                    user = users.first()
                    if not user.is_active:
                        raise serializers.ValidationError({"email": "Your account has been deactivated. Please contact support."})
                    is_login = True
                else:
                    is_signup = True

            else:
                users = User.objects.filter(
                    phone_number=phone_number[-10:], 
                    user_type__in=UserTypeChoices.normal_users(),
                    partner=partner
                )
                if users.exists():
                    # Check if user is active
                    user = users.first()
                    if not user.is_active:
                        raise serializers.ValidationError({"phone_number": "Your account has been deactivated. Please contact support."})
                    is_login = True
                else:
                    is_signup = True

        otp_object = otp_objects.first()
        if otp_object.created_at < timezone.now() - timedelta(minutes=Constants.OTP_EXPIRATION_MINUTES):
            otp_object.hard_delete()
            raise serializers.ValidationError({"otp": "OTP expired"})

        env = settings.ENV
        if env in ['dev', 'uat']:
            if otp_code == 123456 or otp_code == otp_object.code:
                is_otp_valid = True
                # otp_object.hard_delete()
                # return {"is_valid": True}
        else:
            if otp_code == otp_object.code:
                is_otp_valid = True
                # otp_object.hard_delete()
                # return {"is_valid": True}
        
        if is_otp_valid:
            if source == OTPSourceChoices.PARTNER_REGISTRATION.value:
                otp_object.hard_delete()
                return {"is_valid": True}
            elif  source == OTPSourceChoices.LOGIN_SIGNUP.value:
                if is_signup:
                    return {"is_valid": True}
                elif is_login:
                    user = users.first()
                    otp_object.hard_delete()
                    
                    # Update last_login since this is a login flow
                    user.last_login = timezone.now()
                    user.save(update_fields=['last_login'])

                    token = user.token

                    return {
                        "is_valid": True,
                        "user": get_user_data(user),
                        "user_token": token
                    }

        return {"is_valid": False}


class TagSerializer(serializers.ModelSerializer):
    class Meta:
        model = Tag
        fields = ['external_id', 'name', 'slug']


class WriterSerializer(serializers.ModelSerializer):
    class Meta:
        model = Writer
        fields = ['external_id', 'name', 'profile_picture', 'bio',]


class BlogPostSerializer(serializers.ModelSerializer):
    writer = WriterSerializer(read_only=True)
    tags = TagSerializer(many=True, read_only=True)
    seo_title = serializers.SerializerMethodField()
    
    class Meta:
        model = Blog
        fields = [
            # Main fields
            'external_id',
            'title',
            'banner',
            'read_time',
            'published_at',
            'writer',
            'tags',

            # SEO fields
            'slug',
            'seo_title',
            'meta_description',
            'meta_keywords',
            'canonical_url',
            'featured_image_alt_text',
            'robots_directive',
            'focus_keyword',
            'meta_information',

            # Timestamps
            'created_at',
        ]
    
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation['banner'] = instance.banner.name
        return representation
    
    def get_seo_title(self, obj):
        if obj.seo_title:
            return obj.seo_title
        else:
            return obj.title


class BlogPodcastSerializer(serializers.ModelSerializer):
    """Serializer specifically for podcast blogs"""
    writer = WriterSerializer(read_only=True)
    tags = TagSerializer(many=True, read_only=True)
    seo_title = serializers.SerializerMethodField()
    
    class Meta:
        model = Blog
        fields = [
            # Main fields
            'external_id',
            'title',
            'banner',
            'read_time',
            'published_at',
            'writer',
            'tags',
            'audio_file',

            # SEO fields
            'slug',
            'seo_title',
            'meta_description',
            'meta_keywords',
            'canonical_url',
            'featured_image_alt_text',
            'robots_directive',
            'focus_keyword',
            'meta_information',

            # Timestamps
            'created_at',
        ]
    
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation['banner'] = instance.banner.name
        if instance.audio_file:
            representation['audio_file'] = instance.audio_file.url
        return representation
    
    def get_seo_title(self, obj):
        if obj.seo_title:
            return obj.seo_title
        else:
            return obj.title


class BlogDescriptionSerializer(serializers.ModelSerializer):
    """Serializer for blog description/detail view"""
    writer = WriterSerializer(read_only=True)
    tags = TagSerializer(many=True, read_only=True)
    
    class Meta:
        model = Blog
        fields = [
            # Main fields
            'external_id',
            'title',
            'content',
            'banner',
            'read_time',
            'type',
            'published_at',
            'writer',
            'tags',
            # 'audio_file',

            # SEO fields
            'slug',
            'seo_title',
            'meta_description',
            'meta_keywords',
            'canonical_url',
            'featured_image_alt_text',
            'robots_directive',
            'focus_keyword',
            'meta_information',

            # Timestamps
            'created_at',
        ]
    
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation['banner'] = instance.banner.name
        # representation['audio_file'] = instance.audio_file.url
        return representation


class UserSignupSerializer(serializers.Serializer):
    """User Signup Serializer with OTP validation"""
    name = serializers.CharField(max_length=255, required=True)
    phone_number = serializers.DictField(required=True)
    email = serializers.DictField(required=True)
    affiliate = serializers.CharField(required=False)

    def validate_name(self, value):
        """Validate name is not empty"""
        if not value.strip():
            raise serializers.ValidationError("Name cannot be empty")
        return value.strip()

    def validate_phone_number(self, value):
        """Validate phone number structure and OTP"""
        # Check required fields
        phone_number_country_code = value.get('phone_number_country_code')
        phone_number = value.get('phone_number')
        phone_number_otp = value.get('phone_number_otp')

        if not phone_number_country_code:
            raise serializers.ValidationError("phone_number_country_code is required")
        if not phone_number:
            raise serializers.ValidationError("phone_number is required")
        if phone_number_otp is None:
            raise serializers.ValidationError("phone_number_otp is required")

        # Validate OTP format
        try:
            phone_number_otp = int(phone_number_otp)
            if not (0 <= phone_number_otp <= 999999):
                raise serializers.ValidationError("phone_number_otp must be between 0 and 999999")
        except (ValueError, TypeError):
            raise serializers.ValidationError("phone_number_otp must be a valid number")

        # Store validated OTP for later use
        value['phone_number_otp'] = phone_number_otp
        return value

    def validate_email(self, value):
        """Validate email structure and OTP"""
        # Check required fields
        email = value.get('email')
        email_otp = value.get('email_otp')

        if not email:
            raise serializers.ValidationError("email is required")
        if email_otp is None:
            raise serializers.ValidationError("email_otp is required")

        # Validate email format
        try:
            serializers.EmailField().run_validation(email)
        except serializers.ValidationError:
            raise serializers.ValidationError("Invalid email format")

        # Validate OTP format
        try:
            email_otp = int(email_otp)
            if not (0 <= email_otp <= 999999):
                raise serializers.ValidationError("email_otp must be between 0 and 999999")
        except (ValueError, TypeError):
            raise serializers.ValidationError("email_otp must be a valid number")

        # Store validated OTP for later use
        value['email_otp'] = email_otp
        return value

    def validate(self, attrs):
        """Cross-field validation and OTP verification"""
        partner = self.context.get('partner')
        
        name = attrs.get('name')
        phone_data = attrs.get('phone_number')
        email_data = attrs.get('email')

        phone_number = phone_data.get('phone_number')
        phone_number_country_code = phone_data.get('phone_number_country_code')

        original_phone_number = phone_number_country_code + phone_number
        phone_number_otp = phone_data.get('phone_number_otp')
        
        email = email_data.get('email')
        email_otp = email_data.get('email_otp')

        # Check if user already exists with this email for this partner
        existing_user_email = User.objects.filter(
            email=email,
            partner=partner,
        ).first()
        
        if existing_user_email:
            raise serializers.ValidationError({"email": {"email": "User with this email already exists"}})

        # Check if user already exists with this phone number for this partner
        existing_user_phone = User.objects.filter(
            phone_number_country_code=phone_number_country_code,
            phone_number=phone_number,
            partner=partner,
        ).first()
        
        if existing_user_phone:
            raise serializers.ValidationError({"phone_number": {"phone_number": "User with this phone number already exists"}})

        # Verify Email OTP
        email_otp_objects = OTP.objects.filter(
            email=email,
            source=OTPSourceChoices.LOGIN_SIGNUP.value,
            partner=partner
        )
        if not email_otp_objects.exists():
            raise serializers.ValidationError({"email": {"email_otp": "Email OTP does not exist or has expired"}})

        email_otp_object = email_otp_objects.first()
        if email_otp_object.created_at < timezone.now() - timedelta(minutes=Constants.OTP_EXPIRATION_MINUTES):
            email_otp_object.hard_delete()
            raise serializers.ValidationError({"email": {"email_otp": "Email OTP has expired"}})

        # Verify Phone OTP
        phone_otp_objects = OTP.objects.filter(
            phone_number=original_phone_number,
            source=OTPSourceChoices.LOGIN_SIGNUP.value,
            partner=partner
        )
        if not phone_otp_objects.exists():
            raise serializers.ValidationError({"phone_number": {"phone_number_otp": "Phone number OTP does not exist or has expired"}})

        phone_otp_object = phone_otp_objects.first()
        if phone_otp_object.created_at < timezone.now() - timedelta(minutes=Constants.OTP_EXPIRATION_MINUTES):
            phone_otp_object.hard_delete()
            raise serializers.ValidationError({"phone_number": {"phone_number_otp": "Phone number OTP has expired"}})

        # Validate OTPs
        env = settings.ENV
        email_otp_valid = False
        phone_otp_valid = False

        if env in ['dev', 'uat']:
            if email_otp == 123456 or email_otp == email_otp_object.code:
                email_otp_valid = True
            if phone_number_otp == 123456 or phone_number_otp == phone_otp_object.code:
                phone_otp_valid = True
        else:
            if email_otp == email_otp_object.code:
                email_otp_valid = True
            if phone_number_otp == phone_otp_object.code:
                phone_otp_valid = True

        if not email_otp_valid:
            raise serializers.ValidationError({"email": {"email_otp": "Invalid email OTP"}})
        
        if not phone_otp_valid:
            raise serializers.ValidationError({"phone_number": {"phone_number_otp": "Invalid phone number OTP"}})

        # Store OTP objects for cleanup after successful creation
        attrs['_email_otp_object'] = email_otp_object
        attrs['_phone_otp_object'] = phone_otp_object

        return attrs

    def create(self, validated_data):
        """Create user after successful OTP validation"""
        partner = self.context.get('partner')
        
        name = validated_data.get('name')
        phone_data = validated_data.get('phone_number')
        email_data = validated_data.get('email')

        phone_number = phone_data.get('phone_number')
        phone_number_country_code = phone_data.get('phone_number_country_code')
        email = email_data.get('email')

        # Determine user type based on partner
        if partner.partner_type == PartnerTypeChoices.ZUUMM.value:
            user_type = UserTypeChoices.ZUUMM_USER.value
        else:
            user_type = UserTypeChoices.PARTNER_USER.value

        # Create user
        user = User.objects.create_user(
            email=email,
            full_name=name,
            phone_number=phone_number,
            phone_number_country_code=phone_number_country_code,
            partner=partner,
            user_type=user_type,
            is_email_verified=True,  # Email is verified via OTP
        )
        
        # Set last_login since this is essentially a first login
        user.last_login = timezone.now()
        user.save(update_fields=['last_login'])


        affiliate = validated_data.get('affiliate')
        if affiliate:
            handle_affiliate_referral(user, affiliate)

        email_context = {
            'user_name': user.full_name or user.email,
            'explore_url': f"{settings.FE_BASE_URL}explore",
            'affiliate_url': f"{settings.FE_AFFILIATE_SECTION_URL}",
        }
        subject, body = EmailContentManager.get_email_content('user_signup', email_context)
        send_mail_task(subject, body, [user.email])

        # Clean up OTPs after successful user creation
        email_otp_object = validated_data.get('_email_otp_object')
        phone_otp_object = validated_data.get('_phone_otp_object')
        
        if email_otp_object:
            email_otp_object.hard_delete()
        if phone_otp_object:
            phone_otp_object.hard_delete()

        return user


class SocialLoginSerializer(serializers.Serializer):
    """
    Social Login Serializer for handling social authentication
    Supports Google OAuth and can be extended for other providers
    """
    provider = serializers.ChoiceField(
        choices=ProviderChoices.choices,
        help_text="Social login provider (e.g., GOOGLE)"
    )
    id_token = serializers.CharField(
        max_length=2000,
        help_text="ID token from the social provider"
    )
    affiliate = serializers.CharField(required=False)
    
    def validate(self, attrs):
        """Validate social login token and extract user data"""
        provider = attrs.get('provider')
        id_token = attrs.get('id_token')
        partner = self.context.get('partner')

        # Validate based on provider
        if provider == ProviderChoices.GOOGLE.value:
            user_data = self._validate_google_token(id_token)
        else:
            raise serializers.ValidationError(f'Provider {provider} is not supported yet')
        
        # Get or create user
        user, is_login, is_signup = self._get_or_create_user(user_data, provider, partner)
        
        attrs['user'] = user
        attrs['is_login'] = is_login
        attrs['is_signup'] = is_signup
        attrs['user_data'] = user_data
        attrs['provider'] = provider
        attrs['uid'] = user_data['uid']
        attrs['token'] = user_data['token']
        attrs['meta_data'] = user_data['raw_data']
        
        return attrs
    
    def _validate_google_token(self, id_token):
        """Validate Google ID token and extract user information"""
        try:
            # Verify the token with Google
            idinfo = google_id_token.verify_oauth2_token(
                id_token, 
                requests.Request(),
                audience=settings.GOOGLE_CLIENT_ID
            )
            
            # Verify the issuer
            if idinfo['iss'] not in ['accounts.google.com', 'https://accounts.google.com']:
                raise serializers.ValidationError('Invalid token issuer')
            
            # Extract user data
            user_data = {
                'uid': idinfo.get('sub'),
                'email': idinfo.get('email'),
                'name': idinfo.get('name', None),
                'first_name': idinfo.get('given_name', ''),
                'last_name': idinfo.get('family_name', ''),
                'is_email_verified': idinfo.get('email_verified', True),
                'locale': idinfo.get('locale', ''),
                'token': id_token,
                'raw_data': idinfo
            }
            
            if not user_data['uid'] or not user_data['email']:
                raise serializers.ValidationError('Missing required user data from Google')
            
            return user_data
            
        except Exception as e:
            raise serializers.ValidationError(f'Invalid Google ID token: {str(e)}')
    
    def _get_or_create_user(self, user_data, provider, partner):
        """Get or create user from social login data"""
        uid = user_data['uid']
        email = user_data['email']
        
        # Fix full_name concatenation to prevent TypeError
        full_name = user_data.get('name')
        if not full_name:
            first_name = user_data.get('first_name', '').strip()
            last_name = user_data.get('last_name', '').strip()
            full_name = f"{first_name} {last_name}".strip() or None

        is_email_verified = True
        is_signup = False
        is_login = False
        
        # Determine user type based on partner
        if partner.partner_type == PartnerTypeChoices.ZUUMM.value:
            user_type = UserTypeChoices.ZUUMM_USER.value
        else:
            user_type = UserTypeChoices.PARTNER_USER.value
        
        user = User.objects.filter(email=email, partner=partner).first()

        if user:
            if user.user_type in UserTypeChoices.admin_types():
                raise serializers.ValidationError('You are not authorized to login with this email.')

            is_login = True
            if not user.is_active:
                raise serializers.ValidationError('Your account has been deactivated. Please contact support.')
            if user.is_social_user:
                try:
                    social_account = user.social_account
                    if social_account.provider != provider:
                        raise serializers.ValidationError('You are already a social user with a different provider. Please login with the same provider.')
                    
                    # Update social account token and metadata during login
                    social_account.token = user_data['token']
                    social_account.meta_data = user_data['raw_data']
                    social_account.save(update_fields=['token', 'meta_data'])
                    
                except UserSocialAccount.DoesNotExist:
                    # This shouldn't happen if is_social_user is True, but handle it
                    raise serializers.ValidationError('Social account data is missing. Please contact support.')
            # else:
            #     raise serializers.ValidationError('You are already a registered user. Please login directly.')

        else:
            is_signup = True
            user = User.objects.create_user(
                email=email,
                full_name=full_name,
                partner=partner,
                user_type=user_type,
                is_social_user=True,
                is_email_verified=is_email_verified
            )

        user.last_login = timezone.now()
        user.is_social_user = True
        user.save(update_fields=['last_login', 'is_social_user'])

        return user, is_login, is_signup

    def create(self, validated_data):
        """Return the user data with tokens"""
        user = validated_data['user']
        is_login = validated_data['is_login']
        is_signup = validated_data['is_signup']
        
        social_account = UserSocialAccount.objects.filter(user=user, provider=validated_data['provider']).first()

        if is_signup or not social_account:
            social_account = UserSocialAccount.objects.create(
                user=user,
                provider=validated_data['provider'],
                uid=validated_data['uid'],
                token=validated_data['token'],
                meta_data=validated_data['meta_data']
            )
        else:
            social_account  = user.social_account

        affiliate = validated_data.get('affiliate')
        if is_signup:
            email_context = {
                'user_name': user.full_name or user.email,
                'explore_url': f"{settings.FE_BASE_URL}explore",
                'affiliate_url': f"{settings.FE_AFFILIATE_SECTION_URL}",
            }
            subject, body = EmailContentManager.get_email_content('user_signup', email_context)
            send_mail_task(subject, body, [user.email])

            if affiliate:
                handle_affiliate_referral(user, affiliate)

        # Generate JWT tokens
        token = user.token

        return {
            'user_data': get_user_data(user),
            'token': token,
            'provider': social_account.provider,
            'is_login': is_login,
            'is_signup': is_signup,
        }


class UserProfileUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for updating user profile information
    Allows partial updates of full_name, email, phone_number_country_code, and phone_number
    """
    
    class Meta:
        model = User
        fields = ('full_name', 'email', 'phone_number_country_code', 'phone_number')
        extra_kwargs = {
            'full_name': {'required': False},
            'email': {'required': False},
            'phone_number_country_code': {'required': False},
            'phone_number': {'required': False},
        }
    
    def validate_email(self, value):
        """Validate email format and uniqueness for the partner"""
        if value:
            # Get current user and partner from context
            user = self.instance
            partner = self.context.get('partner')
            
            # Check if another user with same email exists for this partner (excluding current user)
            existing_user = User.objects.filter(
                email=value,
                partner=partner
            ).exclude(id=user.id).first()
            if existing_user:
                raise serializers.ValidationError("User with this email already exists.")
    
        return value
    
    def validate_phone_number(self, value):
        """Validate phone number format"""
        if value:
            partner = self.context.get('partner')

            # Remove any spaces or special characters
            value = value.strip()
            if not value:
                raise serializers.ValidationError("Phone number cannot be empty")
            
            # Check if another user with same phone number exists for this partner (excluding current user)
            existing_user = User.objects.filter(
                phone_number=value,
                partner=partner
            ).exclude(id=self.instance.id).first()
            if existing_user:
                raise serializers.ValidationError("User with this phone number already exists.")

        return value
    
    def validate_phone_number_country_code(self, value):
        """Validate phone number country code format"""
        if value:
            value = value.strip()
            if not value.startswith('+'):
                raise serializers.ValidationError("Country code must start with '+'")
        
        return value
    
    def validate_full_name(self, value):
        """Validate full name is not empty"""
        if value:
            value = value.strip()
            if not value:
                raise serializers.ValidationError("Full name cannot be empty")
        
        return value
    
    def update(self, instance, validated_data):
        """Update user profile with validated data"""
        # Update only the fields that are provided in the request
        for field_name, field_value in validated_data.items():
            setattr(instance, field_name, field_value)
        
        instance.save()
        return instance


class UserProfileSerializer(serializers.ModelSerializer):
    """
    Serializer for retrieving user profile information
    Returns user data in the format used by get_user_data helper function
    """
    phone_number = serializers.SerializerMethodField()
    is_profile_completed = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = [
            'external_id',
            'full_name', 
            'email',
            'phone_number',
            'user_type',
            'is_email_verified',
            'is_social_user',
            'created_at',
            'last_login',
            'is_profile_completed'
        ]
        read_only_fields = fields
    
    def get_phone_number(self, obj):
        """Return phone number as nested object"""
        return {
            'phone_number_country_code': obj.phone_number_country_code,
            'phone_number': obj.phone_number,
        }
    
    def get_is_profile_completed(self, obj):
        """Check if user profile is completed"""
        return user_profile_complete_status(obj)


class AffiliateProfileSerializer(serializers.ModelSerializer):
    """Serializer for affiliate profile"""
    is_affiliate = serializers.SerializerMethodField()
    user = serializers.SerializerMethodField()
    status = serializers.SerializerMethodField()
    affiliate_code = serializers.SerializerMethodField()
    affiliate_url = serializers.SerializerMethodField()
    rejection_reason = serializers.SerializerMethodField()
    users_referred_count = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = ['external_id', 'is_affiliate', 'user', 'status', 'affiliate_code', 'affiliate_url', 'rejection_reason', 'users_referred_count']
        read_only_fields = fields  # Mark all fields as read-only since this is for GET only

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.user = self.context.get('request').user
        self.affiliate_object = None
        try:
            self.affiliate_object = self.user.affiliate
        except Exception as e:
            print(e)
            pass

    def get_is_affiliate(self, obj):
        """Return if user is affiliate"""
        return self.user.is_affiliate

    def get_user(self, obj):
        """Return user data"""
        user_object = {
            'external_id': str(obj.external_id),
            'full_name': obj.full_name,
            'email': obj.email,
        }
        return user_object

    def get_status(self, obj):
        """Return affiliate status"""
        if self.affiliate_object:
            return self.affiliate_object.status
        return None

    def get_rejection_reason(self, obj):
        """Return affiliate rejection reason"""
        if self.affiliate_object and self.affiliate_object.status == AffiliateStatusChoices.REJECTED.value and self.affiliate_object.rejection_reason:
            return self.affiliate_object.rejection_reason
        return None

    def get_users_referred_count(self, obj):
        """Return affiliate users referred count"""
        if self.affiliate_object:
            return self.affiliate_object.users_referred_count
        return 0

    def get_affiliate_code(self, obj):
        """Return affiliate code"""
        if self.affiliate_object:
            return self.affiliate_object.affiliate_code
        return None

    def get_affiliate_url(self, obj):
        """Return affiliate url"""
        if self.affiliate_object and self.affiliate_object.status == AffiliateStatusChoices.APPROVED.value:        
            return f"{settings.FE_AFFILIATE_BASE_URL}{self.affiliate_object.affiliate_code}"
        return None


class AffiliateRequestSerializer(serializers.ModelSerializer):
    """Serializer for affiliate request"""

    class Meta:
        model = Affiliate
        fields = ['external_id', 'status', 'requested_at']
        read_only_fields = ['external_id', 'status', 'requested_at']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.user = self.context.get('request').user
        try:
            self.affiliate_object = self.user.affiliate
        except Affiliate.DoesNotExist:
            self.affiliate_object = None

    def validate(self, attrs):
        """Validate affiliate request"""
        if self.user.is_affiliate:
            raise serializers.ValidationError("You are already an affiliate.")
        
        if self.affiliate_object:
            if self.affiliate_object.status == AffiliateStatusChoices.APPROVED.value:
                raise serializers.ValidationError("You are already an affiliate.")
            elif self.affiliate_object.status == AffiliateStatusChoices.PENDING.value:
                raise serializers.ValidationError("Your affiliate request is pending with Admin.")
        
        return attrs
    
    def create(self, validated_data):
        """Create affiliate request"""
        if not self.affiliate_object:
            # Create new affiliate request
            affiliate = Affiliate.objects.create(
                user=self.user,
                status=AffiliateStatusChoices.PENDING.value,
                requested_at=timezone.now()
            )
            return affiliate
        else:
            # Update existing affiliate request
            self.affiliate_object.status = AffiliateStatusChoices.PENDING.value
            self.affiliate_object.rejection_reason = None
            self.affiliate_object.requested_at = timezone.now()
            self.affiliate_object.save(update_fields=['status', 'rejection_reason', 'requested_at'])
            return self.affiliate_object


class AffiliateReferralUsersSerializer(serializers.ModelSerializer):
    """Serializer for affiliate referral users"""
    email = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = ['external_id', 'email', 'full_name', 'phone_number', 'is_active', 'created_at']

    def get_email(self, obj):
        """Return user email"""
        return get_masked_email(obj.email)
