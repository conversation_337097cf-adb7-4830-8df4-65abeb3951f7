from django.urls import path
from accounts.api.v1 import views 


urlpatterns = [
    path(
        'register/partner/', 
        views.PartnerRegistrationView.as_view(), 
        name='partner-registration'
    ),

    # Presigned URL API for Public Assets
    path(
        'presigned-url/<str:file_type>/<str:file>/', 
        views.PresignedURLView.as_view(), 
        name='presigned-url'
    ),

    # Presigned URL API for Encrypted Assets
    path(
        'encrypted-presigned-url/<str:file_type>/<str:file>/', 
        views.EncryptedPresignedURLView.as_view(), 
        name='encrypted-presigned-url'
    ),

    # OTP API's
    path(
        'otp/',
        views.OTPViewSet.as_view(
            {
                'post': 'send_otp',
                'get': 'verify_otp',
            }
        ),
        name='otp-send-verify'
    ),

    # Signup API
    path(
        'signup/',
        views.SignupView.as_view(),
        name='user-signup'
    ),

    # Social Login API
    path(
        'social-login/',
        views.SocialLoginView.as_view(),
        name='social-login'
    ),

    # Profile API
    path(
        'profile/',
        views.UserProfileViewSet.as_view({
            'get': 'retrieve',
            'patch': 'partial_update'
        }),
        name='user-profile'
    ),

    path(
        'logout/', 
        views.LogoutView.as_view(), 
        name='logout'
    ),

    # Blog API's
    path(
        'blog-posts/', 
        views.BlogPostView.as_view(), 
        name='blog-posts'
    ),
    path(
        'blog-podcasts/', 
        views.BlogPodcastView.as_view(), 
        name='blog-podcasts'
    ),
    path(
        'blog-description/<str:slug>/', 
        views.BlogDescriptionView.as_view(), 
        name='blog-description'
    ),

    # Affiliate API's
    path(
        'affiliate-profile/',
        views.AffiliateProfileView.as_view(
            {
                'get': 'fetch_profile',
            }
        ),
        name='affiliate-profile'
    ),
    path(
        'affiliate-request/',
        views.AffiliateRequestView.as_view(
            {
                'post': 'request_affiliate',
            }
        ),
        name='affiliate-request'
    ),
    path(
        'affiliate-referral-users/',
        views.AffiliateReferralUsersView.as_view(
            {
                'get': 'fetch_referral_users',
            }
        ),
        name='affiliate-referral-users'
    ),
    
]
