export interface DropdownOption {
  idx?: number;
  value: string;
  title?: string;
  serverUrl?: string;
  label?: string;
}

export interface DropdownProps {
  options: DropdownOption[];
  onChange: (option: DropdownOption) => void;
  ariaLabel?: string;
  className?: string;
  placeholder?: string;
  value?: string;
  dense?: boolean;
  fullWidth?: boolean;
  variant?: 'dark' | 'light';
}

export interface ArrowIconProps {
  className?: string;
  variant?: 'light' | 'dark';
  style?: React.CSSProperties;
}
