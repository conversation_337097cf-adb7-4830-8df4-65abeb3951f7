// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Models Schema should correct resolve double $ref if no need sibling 1`] = `
{
  "refsStack": [
    "#/components/schemas/Parent",
  ],
  "resolved": {
    "properties": {
      "test": {
        "type": "string",
      },
    },
    "type": "object",
  },
}
`;

exports[`Models Schema should hoist oneOfs when mergin allOf 1`] = `
{
  "oneOf": [
    {
      "allOf": [
        {
          "properties": {
            "id": {
              "description": "The user's ID",
              "type": "integer",
            },
          },
        },
        {
          "properties": {
            "username": {
              "description": "The user's name",
              "type": "string",
            },
          },
        },
        {
          "properties": {
            "extra": {
              "type": "string",
            },
          },
        },
        {
          "oneOf": [
            {
              "properties": {
                "password": {
                  "description": "The user's password",
                  "type": "string",
                },
              },
            },
            {
              "properties": {
                "mobile": {
                  "description": "The user's mobile",
                  "type": "string",
                },
              },
            },
          ],
        },
      ],
      "x-refsStack": undefined,
    },
    {
      "allOf": [
        {
          "properties": {
            "id": {
              "description": "The user's ID",
              "type": "integer",
            },
          },
        },
        {
          "properties": {
            "email": {
              "description": "The user's email",
              "type": "string",
            },
          },
        },
        {
          "properties": {
            "extra": {
              "type": "string",
            },
          },
        },
        {
          "oneOf": [
            {
              "properties": {
                "password": {
                  "description": "The user's password",
                  "type": "string",
                },
              },
            },
            {
              "properties": {
                "mobile": {
                  "description": "The user's mobile",
                  "type": "string",
                },
              },
            },
          ],
        },
      ],
      "x-refsStack": undefined,
    },
    {
      "allOf": [
        {
          "properties": {
            "id": {
              "description": "The user's ID",
              "type": "integer",
            },
          },
        },
        {
          "properties": {
            "id": {
              "description": "The user's ID",
              "format": "uuid",
              "type": "string",
            },
          },
        },
        {
          "properties": {
            "extra": {
              "type": "string",
            },
          },
        },
        {
          "oneOf": [
            {
              "properties": {
                "password": {
                  "description": "The user's password",
                  "type": "string",
                },
              },
            },
            {
              "properties": {
                "mobile": {
                  "description": "The user's mobile",
                  "type": "string",
                },
              },
            },
          ],
        },
      ],
      "x-refsStack": undefined,
    },
  ],
}
`;

exports[`Models Schema should override description from $ref of the referenced component, when sibling description exists  1`] = `
{
  "refsStack": [
    "#/components/schemas/Test",
  ],
  "resolved": {
    "description": "Overriden description",
    "type": "object",
  },
}
`;
