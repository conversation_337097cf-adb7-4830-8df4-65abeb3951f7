// Jest <PERSON> v1, https://goo.gl/fbAQLP

exports[`Models Schema schemaDefinition should resolve field with conditional operators 1`] = `
{
  "allOf": undefined,
  "default": undefined,
  "description": undefined,
  "items": {
    "allOf": undefined,
    "description": undefined,
    "format": "url",
    "readOnly": undefined,
    "title": undefined,
    "type": "string",
    "writeOnly": undefined,
    "x-circular-ref": undefined,
    "x-parentRefs": [],
  },
  "maxItems": 20,
  "minItems": 1,
  "readOnly": undefined,
  "title": "isString",
  "type": "string",
  "writeOnly": undefined,
  "x-circular-ref": undefined,
  "x-displayName": "isString",
  "x-parentRefs": [],
}
`;

exports[`Models Schema schemaDefinition should resolve field with conditional operators 2`] = `
{
  "allOf": undefined,
  "default": undefined,
  "description": undefined,
  "items": {
    "allOf": undefined,
    "description": undefined,
    "format": "url",
    "readOnly": undefined,
    "title": undefined,
    "type": "string",
    "writeOnly": undefined,
    "x-circular-ref": undefined,
    "x-parentRefs": [],
  },
  "maxItems": 10,
  "minItems": 1,
  "pattern": "\\d+",
  "readOnly": undefined,
  "title": "notString",
  "type": [
    "string",
    "integer",
    "null",
  ],
  "writeOnly": undefined,
  "x-circular-ref": undefined,
  "x-displayName": "notString",
  "x-parentRefs": [],
}
`;

exports[`Models Schema schemaDefinition should resolve schema with conditional operators 1`] = `
{
  "allOf": undefined,
  "description": undefined,
  "maxItems": 2,
  "properties": {
    "test": {
      "allOf": undefined,
      "description": "The list of URL to a cute photos featuring pet",
      "enum": [
        10,
      ],
      "items": {
        "allOf": undefined,
        "description": undefined,
        "format": "url",
        "readOnly": undefined,
        "title": undefined,
        "type": "string",
        "writeOnly": undefined,
        "x-circular-ref": undefined,
        "x-parentRefs": [],
      },
      "maxItems": 20,
      "minItems": 1,
      "readOnly": undefined,
      "title": undefined,
      "type": [
        "string",
        "integer",
        "null",
      ],
      "writeOnly": undefined,
      "x-circular-ref": undefined,
      "x-parentRefs": [],
      "x-refsStack": [
        "/oneOf/0",
      ],
    },
  },
  "readOnly": undefined,
  "title": "=== 10",
  "type": "object",
  "writeOnly": undefined,
  "x-circular-ref": undefined,
  "x-parentRefs": [],
}
`;

exports[`Models Schema schemaDefinition should resolve schema with conditional operators 2`] = `
{
  "allOf": undefined,
  "description": undefined,
  "maxItems": 20,
  "properties": {
    "test": {
      "description": "The list of URL to a cute photos featuring pet",
      "items": {
        "format": "url",
        "type": "string",
      },
      "maxItems": 20,
      "minItems": 1,
      "type": [
        "string",
        "integer",
        "null",
      ],
      "x-refsStack": [
        "/oneOf/1",
      ],
    },
  },
  "readOnly": undefined,
  "title": "case 2",
  "type": "object",
  "writeOnly": undefined,
  "x-circular-ref": undefined,
  "x-parentRefs": [],
}
`;
