{"openapi": "3.0.0", "info": {"version": "1.0", "title": "Foo"}, "components": {"parameters": {"testParam": {"in": "path", "name": "test_name", "schema": {"type": "string"}}, "serializationParam": {"in": "query", "name": "serialization_test_name", "schema": {"type": "array"}, "style": "form", "explode": true}, "queryParamWithNoStyle": {"in": "query", "name": "serialization_test_name", "schema": {"type": "array"}}, "pathParamWithNoStyle": {"in": "path", "name": "serialization_test_name", "schema": {"type": "array"}}, "cookieParamWithNoStyle": {"in": "cookie", "name": "serialization_test_name", "schema": {"type": "array"}}}, "headers": {"testHeader": {"description": "The response content language", "schema": {"type": "string"}}}}}