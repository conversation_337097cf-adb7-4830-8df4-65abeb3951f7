{"openapi": "3.0.0", "info": {"version": "1.0", "title": "Foo"}, "components": {"schemas": {"Case1": {"allOf": [{"title": "Bar"}, {"title": "Baz"}]}, "Case2": {"properties": {"a": {"allOf": [{"title": "Bar"}, {"title": "Baz"}]}}}, "Case3": {"schemas": {"Foo": {"title": "Foo", "allOf": [{"title": "Bar"}, {"title": "Baz"}]}}}, "Case4": {"allOf": [{"title": "Foo"}, {"$ref": "#/components/schemas/Ref"}]}, "Ref": {"oneOf": [{"title": "Bar"}, {"title": "Baz"}]}}}}