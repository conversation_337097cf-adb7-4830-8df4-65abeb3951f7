{"openapi": "3.0.0", "info": {"version": "1.0", "title": "Foo"}, "components": {"schemas": {"Test": {"type": "object", "properties": {"any": {"anyOf": [{"$ref": "#/components/schemas/Foo"}, {"$ref": "#/components/schemas/Bar"}]}, "one": {"oneOf": [{"$ref": "#/components/schemas/Foo"}, {"$ref": "#/components/schemas/Bar"}]}, "all": {"allOf": [{"$ref": "#/components/schemas/Foo"}, {"$ref": "#/components/schemas/Bar"}]}}}, "Foo": {"type": "object", "properties": {"foo": {"type": "string"}}}, "Bar": {"type": "object", "properties": {"bar": {"type": "string"}}}, "WithArray": {"oneOf": [{"type": "array", "items": {"oneOf": [{"type": "string"}, {"type": "number"}]}}, {"type": "string"}]}}}}