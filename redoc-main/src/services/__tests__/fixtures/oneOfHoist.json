{"openapi": "3.0.0", "info": {"version": "1.0", "title": "Foo"}, "components": {"schemas": {"test": {"allOf": [{"properties": {"id": {"description": "The user's ID", "type": "integer"}}, "oneOf": [{"properties": {"username": {"description": "The user's name", "type": "string"}}}, {"properties": {"email": {"description": "The user's email", "type": "string"}}}, {"properties": {"id": {"description": "The user's ID", "type": "string", "format": "uuid"}}}]}, {"properties": {"extra": {"type": "string"}}}, {"oneOf": [{"properties": {"password": {"description": "The user's password", "type": "string"}}}, {"properties": {"mobile": {"description": "The user's mobile", "type": "string"}}}]}]}}}}