{"openapi": "3.0.0", "servers": [], "info": {"title": "Broken Redoc Discriminator", "version": ""}, "paths": {"/foo": {"get": {"responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/FooTopLevel"}}}}}}}}, "components": {"schemas": {"JsonApiResource": {"type": "object", "description": "A related resource.", "required": ["type"], "discriminator": {"propertyName": "type"}, "properties": {"type": {"type": "string", "description": "The type of object this resource represents."}}}, "FooTopLevel": {"type": "object", "required": ["data"], "properties": {"data": {"$ref": "#/components/schemas/Foo"}}}, "Foo": {"allOf": [{"type": "object"}, {"$ref": "#/components/schemas/JsonApiResource"}]}}}}