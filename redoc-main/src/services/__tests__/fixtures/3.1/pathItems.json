{"openapi": "3.1.0", "info": {"version": "1.0.0", "title": "Swagger Petstore"}, "webhooks": {"myWebhook": {"$ref": "#/components/pathItems/catsWebhook", "description": "Overriding description", "summary": "Overriding summary"}}, "components": {"pathItems": {"catsWebhook": {"put": {"summary": "Get a cat details after update", "description": "Get a cat details after update", "operationId": "updatedCat", "tags": ["pet"], "requestBody": {"description": "Information about cat in the system", "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Pet"}}}}, "responses": {"200": {"description": "update Cat details"}}}, "post": {"summary": "Create new cat", "description": "Info about new cat", "operationId": "createdCat", "tags": ["pet"], "requestBody": {"description": "Information about cat in the system", "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Pet"}}}}, "responses": {"200": {"description": "create Cat details"}}}}}}}