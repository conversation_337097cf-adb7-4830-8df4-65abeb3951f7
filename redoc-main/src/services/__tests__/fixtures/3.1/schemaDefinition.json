{"openapi": "3.1.0", "info": {"title": "Schema definition double $ref", "version": "1.0.0"}, "servers": [{"url": "example.com"}], "tags": [{"name": "test", "x-displayName": "The test Model", "description": "<SchemaDefinition schemaRef=\"#/components/schemas/Parent\" />\n"}], "paths": {"/newPath": {"post": {"requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Child"}}}}, "responses": {"200": {"description": "all ok"}}}}}, "components": {"schemas": {"Parent": {"$ref": "#/components/schemas/Child"}, "Child": {"type": "object", "properties": {"test": {"type": "string"}}}}}}