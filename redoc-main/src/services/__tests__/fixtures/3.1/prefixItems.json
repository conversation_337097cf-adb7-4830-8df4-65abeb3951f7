{"openapi": "3.1.0", "info": {"title": "Schema definition with prefixItems", "version": "1.0.0"}, "servers": [{"url": "example.com"}], "components": {"schemas": {"Case1": {"type": "array", "minItems": 1, "maxItems": 10, "prefixItems": [{"type": "string", "minLength": 0, "maxLength": 10}, {"type": "number", "minimum": 0, "maximum": 10}, {"$ref": "#/components/schemas/Cat"}], "items": false}, "Case2": {"type": "array", "minItems": 1, "maxItems": 10, "prefixItems": [{"type": "string", "minLength": 0, "maxLength": 10}, {"type": "number", "minimum": 0, "maximum": 10}, {"$ref": "#/components/schemas/Cat"}], "items": true}, "Case3": {"type": "array", "minItems": 1, "maxItems": 10, "prefixItems": [{"type": "string", "minLength": 0, "maxLength": 10}, {"type": "number", "minimum": 0, "maximum": 10}, {"$ref": "#/components/schemas/Cat"}], "items": {"$ref": "#/components/schemas/Dog"}}, "Case4": {"type": "array", "minItems": 1, "maxItems": 10, "prefixItems": [{"type": "string", "minLength": 0, "maxLength": 10}, {"type": "number", "minimum": 0, "maximum": 10}, {"$ref": "#/components/schemas/Cat"}], "items": {"type": "object", "properties": {"firstItem": {"type": "string"}}}}, "Case5": {"type": "array", "minItems": 1, "maxItems": 10, "prefixItems": [{"type": "string", "minLength": 0, "maxLength": 10}, {"type": "number", "minimum": 0, "maximum": 10}, {"$ref": "#/components/schemas/Cat"}], "items": {"type": "array", "items": [{"type": "string", "minLength": 0}]}}, "Case6": {"type": "array", "minItems": 1, "items": {"$ref": "#/components/schemas/Tag"}}, "Case7": {"type": "object", "properties": {"array_field": {"$ref": "#/components/schemas/AnyArray"}}}, "Cat": {"type": "object", "properties": {"color": {"type": "string"}}}, "Dog": {"type": "object", "properties": {"size": {"type": "string"}}}, "Tag": {"type": "object", "properties": {"id": {"description": "Tag ID", "allOf": [{"$ref": "#/components/schemas/Id"}]}, "name": {"description": "tag name", "type": "string", "minLength": 1}}}, "Id": {"type": "integer", "format": "int64", "readOnly": true}, "AnyArray": {"type": "array", "items": true}}}}