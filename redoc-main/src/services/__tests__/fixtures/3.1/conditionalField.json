{"openapi": "3.1.0", "info": {"title": "Schema definition field with conditional operators", "version": "1.0.0"}, "components": {"schemas": {"Test": {"type": "object", "properties": {"test": {"type": ["string", "integer", "null"], "minItems": 1, "maxItems": 20, "items": {"type": "string", "format": "url"}, "if": {"x-displayName": "isString", "type": "string"}, "then": {"type": "string", "minItems": 1, "maxItems": 20}, "else": {"x-displayName": "notString", "minItems": 1, "maxItems": 10, "pattern": "\\d+"}}}}}}}