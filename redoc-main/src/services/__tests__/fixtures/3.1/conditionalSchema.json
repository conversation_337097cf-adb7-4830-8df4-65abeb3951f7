{"openapi": "3.1.0", "info": {"title": "Schema definition with conditional operators", "version": "1.0.0"}, "components": {"schemas": {"Test": {"type": "object", "properties": {"test": {"description": "The list of URL to a cute photos featuring pet", "type": ["string", "integer", "null"], "minItems": 1, "maxItems": 20, "items": {"type": "string", "format": "url"}}}, "if": {"title": "=== 10", "properties": {"test": {"enum": [10]}}}, "then": {"maxItems": 2}, "else": {"maxItems": 20}}}}}