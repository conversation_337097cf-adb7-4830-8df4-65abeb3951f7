{"openapi": "3.1.0", "info": {"title": "Schema definition with unevaluatedProperties", "version": "1.0.0"}, "servers": [{"url": "example.com"}], "components": {"schemas": {"Patterns": {"type": "object", "patternProperties": {"^S_\\w+\\.[1-9]{2,4}$": {"type": "string"}, "^O_\\w+\\.[1-9]{2,4}$": {"type": "object", "properties": {"x-nestedProperty": {"type": "string"}}}}, "properties": {"nestedObjectProp": {"type": "object", "patternProperties": {".*": {"type": "integer"}}}, "nestedArrayProp": {"type": "array", "items": {"patternProperties": {".*": {"type": "string"}}}}}}}}}