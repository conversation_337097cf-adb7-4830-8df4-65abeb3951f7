{"openapi": "3.1.0", "info": {"title": "Schema definition with unevaluatedProperties", "version": "1.0.0"}, "servers": [{"url": "example.com"}], "components": {"schemas": {"Test": {"type": "object", "unevaluatedProperties": true, "properties": {"$ref": "#/components/schemas/Cat"}}, "Test2": {"type": "object", "unevaluatedProperties": true, "anyOf": [{"$ref": "#/components/schemas/Cat"}, {"$ref": "#/components/schemas/Dog"}]}, "Test3": {"type": "object", "unevaluatedProperties": {"type": "boolean"}, "properties": {"$ref": "#/components/schemas/Cat"}}, "Cat": {"type": "object", "properties": {"color": {"type": "string"}}}, "Dog": {"type": "object", "properties": {"size": {"type": "string"}}}}}}