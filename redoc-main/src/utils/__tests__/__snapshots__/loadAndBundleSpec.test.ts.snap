// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`#loadAndBundleSpec should load And Bundle Spec demo/openapi.yaml 1`] = `
{
  "components": {
    "examples": {
      "Order": {
        "value": {
          "complete": false,
          "quantity": 1,
          "shipDate": "2018-10-19T16:46:45Z",
          "status": "placed",
        },
      },
    },
    "requestBodies": {
      "Pet": {
        "content": {
          "application/json": {
            "schema": {
              "allOf": [
                {
                  "description": "My Pet",
                  "title": "<PERSON><PERSON>",
                },
                {
                  "$ref": "#/components/schemas/Pet",
                },
              ],
            },
          },
          "application/xml": {
            "schema": {
              "properties": {
                "name": {
                  "default": [],
                  "description": "hooray",
                  "type": "string",
                },
              },
              "type": "object",
            },
          },
        },
        "description": "Pet object that needs to be added to the store",
        "required": true,
      },
      "UserArray": {
        "content": {
          "application/json": {
            "schema": {
              "items": {
                "$ref": "#/components/schemas/User",
              },
              "type": "array",
            },
          },
        },
        "description": "List of user object",
        "required": true,
      },
    },
    "schemas": {
      "ApiResponse": {
        "properties": {
          "code": {
            "format": "int32",
            "type": "integer",
          },
          "message": {
            "type": "string",
          },
          "type": {
            "type": "string",
          },
        },
        "type": "object",
      },
      "Cat": {
        "allOf": [
          {
            "$ref": "#/components/schemas/Pet",
          },
          {
            "properties": {
              "huntingSkill": {
                "default": "lazy",
                "description": "The measured skill for hunting",
                "enum": [
                  "clueless",
                  "lazy",
                  "adventurous",
                  "aggressive",
                ],
                "example": "adventurous",
                "type": "string",
              },
            },
            "required": [
              "huntingSkill",
            ],
            "type": "object",
          },
        ],
        "description": "A representation of a cat",
        "x-tags": [
          "pet",
        ],
      },
      "Category": {
        "properties": {
          "id": {
            "allOf": [
              {
                "$ref": "#/components/schemas/Id",
              },
            ],
            "description": "Category ID",
          },
          "name": {
            "description": "Category name",
            "minLength": 1,
            "type": "string",
          },
          "sub": {
            "description": "Test Sub Category",
            "properties": {
              "prop1": {
                "description": "Dumb Property",
                "type": "string",
              },
            },
            "type": "object",
          },
        },
        "type": "object",
        "xml": {
          "name": "Category",
        },
      },
      "Dog": {
        "allOf": [
          {
            "$ref": "#/components/schemas/Pet",
          },
          {
            "properties": {
              "packSize": {
                "default": 1,
                "description": "The size of the pack the dog is from",
                "format": "int32",
                "minimum": 1,
                "type": "integer",
              },
            },
            "required": [
              "packSize",
            ],
            "type": "object",
          },
        ],
        "description": "A representation of a dog",
      },
      "HoneyBee": {
        "allOf": [
          {
            "$ref": "#/components/schemas/Pet",
          },
          {
            "properties": {
              "honeyPerDay": {
                "description": "Average amount of honey produced per day in ounces",
                "example": 3.14,
                "multipleOf": 0.01,
                "type": "number",
              },
            },
            "required": [
              "honeyPerDay",
            ],
            "type": "object",
          },
        ],
        "description": "A representation of a honey bee",
      },
      "Id": {
        "format": "int64",
        "readOnly": true,
        "type": "integer",
      },
      "Order": {
        "properties": {
          "complete": {
            "default": false,
            "description": "Indicates whenever order was completed or not",
            "readOnly": true,
            "type": "boolean",
          },
          "id": {
            "allOf": [
              {
                "$ref": "#/components/schemas/Id",
              },
            ],
            "description": "Order ID",
          },
          "petId": {
            "allOf": [
              {
                "$ref": "#/components/schemas/Id",
              },
            ],
            "description": "Pet ID",
          },
          "quantity": {
            "default": 1,
            "format": "int32",
            "minimum": 1,
            "type": "integer",
          },
          "requestId": {
            "description": "Unique Request Id",
            "type": "string",
            "writeOnly": true,
          },
          "shipDate": {
            "description": "Estimated ship date",
            "format": "date-time",
            "type": "string",
          },
          "status": {
            "description": "Order Status",
            "enum": [
              "placed",
              "approved",
              "delivered",
            ],
            "type": "string",
          },
        },
        "type": "object",
        "xml": {
          "name": "Order",
        },
      },
      "Pet": {
        "discriminator": {
          "mapping": {
            "bee": "#/components/schemas/HoneyBee",
            "cat": "#/components/schemas/Cat",
            "dog": "#/components/schemas/Dog",
          },
          "propertyName": "petType",
        },
        "properties": {
          "category": {
            "allOf": [
              {
                "$ref": "#/components/schemas/Category",
              },
            ],
            "description": "Categories this pet belongs to",
          },
          "friend": {
            "allOf": [
              {
                "$ref": "#/components/schemas/Pet",
              },
            ],
          },
          "id": {
            "allOf": [
              {
                "$ref": "#/components/schemas/Id",
              },
            ],
            "description": "Pet ID",
            "externalDocs": {
              "description": "Find more info here",
              "url": "https://example.com",
            },
          },
          "name": {
            "description": "The name given to a pet",
            "example": "Guru",
            "type": "string",
          },
          "petType": {
            "description": "Type of a pet",
            "type": "string",
          },
          "photoUrls": {
            "default": [],
            "description": "The list of URL to a cute photos featuring pet",
            "items": {
              "format": "url",
              "type": "string",
            },
            "maxItems": 20,
            "type": "array",
            "xml": {
              "name": "photoUrl",
              "wrapped": true,
            },
          },
          "status": {
            "description": "Pet status in the store",
            "enum": [
              "available",
              "pending",
              "sold",
            ],
            "type": "string",
            "x-enumDescriptions": {
              "available": "Available status",
              "pending": "Pending status",
              "sold": "Sold status",
            },
          },
          "tags": {
            "description": "Tags attached to the pet",
            "items": {
              "$ref": "#/components/schemas/Tag",
            },
            "minItems": 1,
            "type": "array",
            "xml": {
              "name": "tag",
              "wrapped": true,
            },
          },
        },
        "required": [
          "name",
          "photoUrls",
        ],
        "type": "object",
        "xml": {
          "name": "Pet",
        },
      },
      "Tag": {
        "properties": {
          "id": {
            "allOf": [
              {
                "$ref": "#/components/schemas/Id",
              },
            ],
            "description": "Tag ID",
          },
          "name": {
            "description": "Tag name",
            "minLength": 1,
            "type": "string",
          },
        },
        "type": "object",
        "xml": {
          "name": "Tag",
        },
      },
      "User": {
        "properties": {
          "addresses": {
            "additionalItems": {
              "type": "string",
            },
            "items": [
              {
                "properties": {
                  "city": {
                    "minLength": 0,
                    "type": "string",
                  },
                  "country": {
                    "minLength": 0,
                    "type": "string",
                  },
                  "street": {
                    "description": "includes build/apartment number",
                    "minLength": 0,
                    "type": "string",
                  },
                },
                "type": "object",
              },
              {
                "type": "number",
              },
            ],
            "maxLength": 10,
            "minItems": 0,
            "type": "array",
          },
          "email": {
            "description": "User email address",
            "example": "<EMAIL>",
            "format": "email",
            "type": "string",
          },
          "firstName": {
            "description": "User first name",
            "example": "John",
            "minLength": 1,
            "type": "string",
          },
          "id": {
            "$ref": "#/components/schemas/Id",
          },
          "lastName": {
            "description": "User last name",
            "example": "Smith",
            "minLength": 1,
            "type": "string",
          },
          "password": {
            "description": "User password, MUST contain a mix of upper and lower case letters, as well as digits",
            "example": "drowssaP123",
            "format": "password",
            "minLength": 8,
            "pattern": "/(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])/",
            "type": "string",
          },
          "pet": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/Pet",
              },
              {
                "$ref": "#/components/schemas/Tag",
              },
            ],
          },
          "phone": {
            "description": "User phone number in international format",
            "example": "******-555-0192",
            "pattern": "/^\\+(?:[0-9]-?){6,14}[0-9]$/",
            "type": "string",
          },
          "userStatus": {
            "description": "User status",
            "format": "int32",
            "type": "integer",
          },
          "username": {
            "description": "User supplied username",
            "example": "John78",
            "minLength": 4,
            "type": "string",
          },
        },
        "type": "object",
        "xml": {
          "name": "User",
        },
      },
    },
    "securitySchemes": {
      "api_key": {
        "description": "For this sample, you can use the api key \`special-key\` to test the authorization filters.
",
        "in": "header",
        "name": "api_key",
        "type": "apiKey",
      },
      "petstore_auth": {
        "description": "Get access to data while protecting your account credentials.
OAuth2 is also a safer and more secure way to give you access.
",
        "flows": {
          "implicit": {
            "authorizationUrl": "http://petstore.swagger.io/api/oauth/dialog",
            "scopes": {
              "read:pets": "read your pets",
              "write:pets": "modify pets in your account",
            },
          },
        },
        "type": "oauth2",
      },
    },
  },
  "externalDocs": {
    "description": "Find out how to create Github repo for your OpenAPI spec.",
    "url": "https://github.com/Rebilly/generator-openapi-repo",
  },
  "info": {
    "contact": {
      "email": "<EMAIL>",
      "name": "API Support",
      "url": "https://github.com/Redocly/redoc",
    },
    "description": "This is a sample server Petstore server.
You can find out more about Swagger at
[http://swagger.io](http://swagger.io) or on [irc.freenode.net, #swagger](http://swagger.io/irc/).
For this sample, you can use the api key \`special-key\` to test the authorization filters.

# Introduction
This API is documented in **OpenAPI format** and is based on
[Petstore sample](http://petstore.swagger.io/) provided by [swagger.io](http://swagger.io) team.
It was **extended** to illustrate features of [generator-openapi-repo](https://github.com/Rebilly/generator-openapi-repo)
tool and [ReDoc](https://github.com/Redocly/redoc) documentation. In addition to standard
OpenAPI syntax we use a few [vendor extensions](https://github.com/Redocly/redoc/blob/main/docs/redoc-vendor-extensions.md).

# OpenAPI Specification
This API is documented in **OpenAPI format** and is based on
[Petstore sample](http://petstore.swagger.io/) provided by [swagger.io](http://swagger.io) team.
It was **extended** to illustrate features of [generator-openapi-repo](https://github.com/Rebilly/generator-openapi-repo)
tool and [ReDoc](https://github.com/Redocly/redoc) documentation. In addition to standard
OpenAPI syntax we use a few [vendor extensions](https://github.com/Redocly/redoc/blob/main/docs/redoc-vendor-extensions.md).

# Cross-Origin Resource Sharing
This API features Cross-Origin Resource Sharing (CORS) implemented in compliance with  [W3C spec](https://www.w3.org/TR/cors/).
And that allows cross-domain communication from the browser.
All responses have a wildcard same-origin which makes them completely public and accessible to everyone, including any code on any site.

# Authentication

Petstore offers two forms of authentication:
  - API Key
  - OAuth2
OAuth2 - an open protocol to allow secure authorization in a simple
and standard method from web, mobile and desktop applications.

<!-- ReDoc-Inject: <security-definitions> -->
",
    "license": {
      "name": "Apache 2.0",
      "url": "http://www.apache.org/licenses/LICENSE-2.0.html",
    },
    "termsOfService": "http://swagger.io/terms/",
    "title": "Swagger Petstore",
    "version": "1.0.0",
    "x-logo": {
      "altText": "Petstore logo",
      "url": "https://redocly.github.io/redoc/petstore-logo.png",
    },
  },
  "openapi": "3.0.0",
  "paths": {
    "/pet": {
      "parameters": [
        {
          "description": "The language you prefer for messages. Supported values are en-AU, en-CA, en-GB, en-US",
          "example": "en-US",
          "in": "header",
          "name": "Accept-Language",
          "required": false,
          "schema": {
            "default": "en-AU",
            "type": "string",
          },
        },
        {
          "description": "Some cookie",
          "in": "cookie",
          "name": "cookieParam",
          "required": true,
          "schema": {
            "format": "int64",
            "type": "integer",
          },
        },
      ],
      "post": {
        "description": "Add new pet to the store inventory.",
        "operationId": "addPet",
        "requestBody": {
          "$ref": "#/components/requestBodies/Pet",
        },
        "responses": {
          "405": {
            "description": "Invalid input",
          },
        },
        "security": [
          {
            "petstore_auth": [
              "write:pets",
              "read:pets",
            ],
          },
        ],
        "summary": "Add a new pet to the store",
        "tags": [
          "pet",
        ],
        "x-badges": [
          {
            "color": "purple",
            "name": "Beta",
            "position": "before",
          },
        ],
        "x-codeSamples": [
          {
            "lang": "C#",
            "source": "PetStore.v1.Pet pet = new PetStore.v1.Pet();
pet.setApiKey("your api key");
pet.petType = PetStore.v1.Pet.TYPE_DOG;
pet.name = "Rex";
// set other fields
PetStoreResponse response = pet.create();
if (response.statusCode == HttpStatusCode.Created)
{
  // Successfully created
}
else
{
  // Something wrong -- check response for errors
  Console.WriteLine(response.getRawResponse());
}
",
          },
          {
            "lang": "PHP",
            "source": "$form = new \\PetStore\\Entities\\Pet();
$form->setPetType("Dog");
$form->setName("Rex");
// set other fields
try {
    $pet = $client->pets()->create($form);
} catch (UnprocessableEntityException $e) {
    var_dump($e->getErrors());
}
",
          },
        ],
      },
      "put": {
        "description": "",
        "operationId": "updatePet",
        "requestBody": {
          "$ref": "#/components/requestBodies/Pet",
        },
        "responses": {
          "400": {
            "description": "Invalid ID supplied",
          },
          "404": {
            "description": "Pet not found",
          },
          "405": {
            "description": "Validation exception",
          },
        },
        "security": [
          {
            "petstore_auth": [
              "write:pets",
              "read:pets",
            ],
          },
        ],
        "summary": "Update an existing pet",
        "tags": [
          "pet",
        ],
        "x-badges": [
          {
            "color": "purple",
            "name": "Alpha",
          },
        ],
        "x-codeSamples": [
          {
            "lang": "PHP",
            "source": "$form = new \\PetStore\\Entities\\Pet();
$form->setPetId(1);
$form->setPetType("Dog");
$form->setName("Rex");
// set other fields
try {
    $pet = $client->pets()->update($form);
} catch (UnprocessableEntityException $e) {
    var_dump($e->getErrors());
}
",
          },
        ],
      },
    },
    "/pet/findByStatus": {
      "get": {
        "description": "Multiple status values can be provided with comma separated strings",
        "operationId": "findPetsByStatus",
        "parameters": [
          {
            "description": "Status values that need to be considered for filter",
            "in": "query",
            "name": "status",
            "required": true,
            "schema": {
              "items": {
                "default": "available",
                "enum": [
                  "available",
                  "pending",
                  "sold",
                ],
                "type": "string",
              },
              "maxItems": 3,
              "minItems": 1,
              "type": "array",
            },
            "style": "form",
          },
        ],
        "responses": {
          "200": {
            "content": {
              "application/json": {
                "schema": {
                  "items": {
                    "$ref": "#/components/schemas/Pet",
                  },
                  "type": "array",
                },
              },
              "application/xml": {
                "schema": {
                  "items": {
                    "$ref": "#/components/schemas/Pet",
                  },
                  "type": "array",
                },
              },
            },
            "description": "successful operation",
          },
          "400": {
            "description": "Invalid status value",
          },
        },
        "security": [
          {
            "petstore_auth": [
              "write:pets",
              "read:pets",
            ],
          },
        ],
        "summary": "Finds Pets by status",
        "tags": [
          "pet",
        ],
      },
    },
    "/pet/findByTags": {
      "get": {
        "deprecated": true,
        "description": "Multiple tags can be provided with comma separated strings. Use tag1, tag2, tag3 for testing.",
        "operationId": "findPetsByTags",
        "parameters": [
          {
            "description": "Tags to filter by",
            "in": "query",
            "name": "tags",
            "required": true,
            "schema": {
              "items": {
                "type": "string",
              },
              "type": "array",
            },
            "style": "form",
          },
        ],
        "responses": {
          "200": {
            "content": {
              "application/json": {
                "schema": {
                  "items": {
                    "$ref": "#/components/schemas/Pet",
                  },
                  "type": "array",
                },
              },
              "application/xml": {
                "schema": {
                  "items": {
                    "$ref": "#/components/schemas/Pet",
                    "maxItems": 111,
                  },
                  "maxItems": 999,
                  "type": "array",
                },
              },
            },
            "description": "successful operation",
          },
          "400": {
            "description": "Invalid tag value",
          },
        },
        "security": [
          {
            "petstore_auth": [
              "write:pets",
              "read:pets",
            ],
          },
        ],
        "summary": "Finds Pets by tags",
        "tags": [
          "pet",
        ],
      },
    },
    "/pet/{petId}": {
      "delete": {
        "description": "",
        "operationId": "deletePet",
        "parameters": [
          {
            "example": "Bearer <TOKEN>",
            "in": "header",
            "name": "api_key",
            "required": false,
            "schema": {
              "type": "string",
            },
          },
          {
            "description": "Pet id to delete",
            "in": "path",
            "name": "petId",
            "required": true,
            "schema": {
              "format": "int64",
              "type": "integer",
            },
          },
        ],
        "responses": {
          "400": {
            "description": "Invalid pet value",
          },
        },
        "security": [
          {
            "petstore_auth": [
              "write:pets",
              "read:pets",
            ],
          },
        ],
        "summary": "Deletes a pet",
        "tags": [
          "pet",
        ],
      },
      "get": {
        "description": "Returns a single pet",
        "operationId": "getPetById",
        "parameters": [
          {
            "deprecated": true,
            "description": "ID of pet to return",
            "in": "path",
            "name": "petId",
            "required": true,
            "schema": {
              "format": "int64",
              "type": "integer",
            },
          },
        ],
        "responses": {
          "200": {
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/Pet",
                },
              },
              "application/xml": {
                "schema": {
                  "$ref": "#/components/schemas/Pet",
                },
              },
            },
            "description": "successful operation",
          },
          "400": {
            "description": "Invalid ID supplied",
          },
          "404": {
            "description": "Pet not found",
          },
        },
        "security": [
          {
            "api_key": [],
          },
        ],
        "summary": "Find pet by ID",
        "tags": [
          "pet",
        ],
        "x-badges": [
          {
            "name": "Gamma",
          },
        ],
      },
      "post": {
        "description": "",
        "operationId": "updatePetWithForm",
        "parameters": [
          {
            "description": "ID of pet that needs to be updated",
            "in": "path",
            "name": "petId",
            "required": true,
            "schema": {
              "format": "int64",
              "type": "integer",
            },
          },
        ],
        "requestBody": {
          "content": {
            "application/x-www-form-urlencoded": {
              "schema": {
                "properties": {
                  "name": {
                    "description": "Updated name of the pet",
                    "type": "string",
                  },
                  "status": {
                    "description": "Updated status of the pet",
                    "type": "string",
                  },
                },
                "type": "object",
              },
            },
          },
        },
        "responses": {
          "405": {
            "description": "Invalid input",
          },
        },
        "security": [
          {
            "petstore_auth": [
              "write:pets",
              "read:pets",
            ],
          },
        ],
        "summary": "Updates a pet in the store with form data",
        "tags": [
          "pet",
        ],
      },
    },
    "/pet/{petId}/uploadImage": {
      "post": {
        "description": "",
        "operationId": "uploadFile",
        "parameters": [
          {
            "description": "ID of pet to update",
            "in": "path",
            "name": "petId",
            "required": true,
            "schema": {
              "format": "int64",
              "type": "integer",
            },
          },
        ],
        "requestBody": {
          "content": {
            "application/octet-stream": {
              "schema": {
                "format": "binary",
                "type": "string",
              },
            },
          },
        },
        "responses": {
          "200": {
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ApiResponse",
                },
              },
            },
            "description": "successful operation",
          },
        },
        "security": [
          {
            "petstore_auth": [
              "write:pets",
              "read:pets",
            ],
          },
        ],
        "summary": "uploads an image",
        "tags": [
          "pet",
        ],
      },
    },
    "/store/inventory": {
      "get": {
        "description": "Returns a map of status codes to quantities",
        "operationId": "getInventory",
        "responses": {
          "200": {
            "content": {
              "application/json": {
                "schema": {
                  "additionalProperties": {
                    "format": "int32",
                    "type": "integer",
                  },
                  "minProperties": 2,
                  "type": "object",
                },
              },
            },
            "description": "successful operation",
          },
        },
        "security": [
          {
            "api_key": [],
          },
        ],
        "summary": "Returns pet inventories by status",
        "tags": [
          "store",
        ],
      },
    },
    "/store/order": {
      "post": {
        "description": "",
        "operationId": "placeOrder",
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/Order",
              },
            },
          },
          "description": "order placed for purchasing the pet",
          "required": true,
        },
        "responses": {
          "200": {
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/Order",
                },
              },
              "application/xml": {
                "schema": {
                  "$ref": "#/components/schemas/Order",
                },
              },
            },
            "description": "successful operation",
          },
          "400": {
            "content": {
              "application/json": {
                "example": {
                  "message": "Invalid Order",
                  "status": 400,
                },
              },
            },
            "description": "Invalid Order",
          },
        },
        "summary": "Place an order for a pet",
        "tags": [
          "store",
        ],
      },
    },
    "/store/order/{orderId}": {
      "delete": {
        "description": "For valid response try integer IDs with value < 1000. Anything above 1000 or nonintegers will generate API errors",
        "operationId": "deleteOrder",
        "parameters": [
          {
            "description": "ID of the order that needs to be deleted",
            "in": "path",
            "name": "orderId",
            "required": true,
            "schema": {
              "minimum": 1,
              "type": "string",
            },
          },
        ],
        "responses": {
          "400": {
            "description": "Invalid ID supplied",
          },
          "404": {
            "description": "Order not found",
          },
        },
        "summary": "Delete purchase order by ID",
        "tags": [
          "store",
        ],
      },
      "get": {
        "description": "For valid response try integer IDs with value <= 5 or > 10. Other values will generated exceptions",
        "operationId": "getOrderById",
        "parameters": [
          {
            "description": "ID of pet that needs to be fetched",
            "in": "path",
            "name": "orderId",
            "required": true,
            "schema": {
              "format": "int64",
              "maximum": 5,
              "minimum": 1,
              "type": "integer",
            },
          },
        ],
        "responses": {
          "200": {
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/Order",
                },
              },
              "application/xml": {
                "schema": {
                  "$ref": "#/components/schemas/Order",
                },
              },
            },
            "description": "successful operation",
          },
          "400": {
            "description": "Invalid ID supplied",
          },
          "404": {
            "description": "Order not found",
          },
        },
        "summary": "Find purchase order by ID",
        "tags": [
          "store",
        ],
      },
    },
    "/store/subscribe": {
      "post": {
        "callbacks": {
          "orderDelivered": {
            "http://notificationServer.com?url={$request.body#/callbackUrl}&event={$request.body#/eventName}": {
              "post": {
                "deprecated": true,
                "description": "A callback triggered every time an Order is delivered to the recipient",
                "requestBody": {
                  "content": {
                    "application/json": {
                      "schema": {
                        "properties": {
                          "orderId": {
                            "example": "123",
                            "type": "string",
                          },
                          "timestamp": {
                            "example": "2018-10-19T16:46:45Z",
                            "format": "date-time",
                            "type": "string",
                          },
                        },
                        "type": "object",
                      },
                    },
                  },
                },
                "responses": {
                  "200": {
                    "description": "Callback successfully processed and no retries will be performed",
                  },
                },
                "summary": "Order delivered",
              },
            },
          },
          "orderInProgress": {
            "{$request.body#/callbackUrl}?event={$request.body#/eventName}": {
              "post": {
                "description": "A callback triggered every time an Order is updated status to "inProgress" (Description)",
                "externalDocs": {
                  "description": "Find out more",
                  "url": "https://more-details.com/demo",
                },
                "requestBody": {
                  "content": {
                    "application/json": {
                      "schema": {
                        "properties": {
                          "orderId": {
                            "example": "123",
                            "type": "string",
                          },
                          "status": {
                            "example": "inProgress",
                            "type": "string",
                          },
                          "timestamp": {
                            "example": "2018-10-19T16:46:45Z",
                            "format": "date-time",
                            "type": "string",
                          },
                        },
                        "type": "object",
                      },
                    },
                    "application/xml": {
                      "example": "<?xml version="1.0" encoding="UTF-8"?>
<root>
  <orderId>123</orderId>
  <status>inProgress</status>
  <timestamp>2018-10-19T16:46:45Z</timestamp>
</root>
",
                      "schema": {
                        "properties": {
                          "orderId": {
                            "example": "123",
                            "type": "string",
                          },
                        },
                        "type": "object",
                      },
                    },
                  },
                },
                "responses": {
                  "200": {
                    "content": {
                      "application/json": {
                        "schema": {
                          "properties": {
                            "someProp": {
                              "example": "123",
                              "type": "string",
                            },
                          },
                          "type": "object",
                        },
                      },
                    },
                    "description": "Callback successfully processed and no retries will be performed",
                  },
                  "299": {
                    "description": "Response for cancelling subscription",
                  },
                  "500": {
                    "description": "Callback processing failed and retries will be performed",
                  },
                },
                "summary": "Order in Progress (Summary)",
                "x-codeSamples": [
                  {
                    "lang": "C#",
                    "source": "PetStore.v1.Pet pet = new PetStore.v1.Pet();
pet.setApiKey("your api key");
pet.petType = PetStore.v1.Pet.TYPE_DOG;
pet.name = "Rex";
// set other fields
PetStoreResponse response = pet.create();
if (response.statusCode == HttpStatusCode.Created)
{
  // Successfully created
}
else
{
  // Something wrong -- check response for errors
  Console.WriteLine(response.getRawResponse());
}
",
                  },
                  {
                    "lang": "PHP",
                    "source": "$form = new \\PetStore\\Entities\\Pet();
$form->setPetType("Dog");
$form->setName("Rex");
// set other fields
try {
    $pet = $client->pets()->create($form);
} catch (UnprocessableEntityException $e) {
    var_dump($e->getErrors());
}
",
                  },
                ],
              },
              "put": {
                "description": "Order in Progress (Only Description)",
                "requestBody": {
                  "content": {
                    "application/json": {
                      "schema": {
                        "properties": {
                          "orderId": {
                            "example": "123",
                            "type": "string",
                          },
                          "status": {
                            "example": "inProgress",
                            "type": "string",
                          },
                          "timestamp": {
                            "example": "2018-10-19T16:46:45Z",
                            "format": "date-time",
                            "type": "string",
                          },
                        },
                        "type": "object",
                      },
                    },
                    "application/xml": {
                      "example": "<?xml version="1.0" encoding="UTF-8"?>
<root>
  <orderId>123</orderId>
  <status>inProgress</status>
  <timestamp>2018-10-19T16:46:45Z</timestamp>
</root>
",
                      "schema": {
                        "properties": {
                          "orderId": {
                            "example": "123",
                            "type": "string",
                          },
                        },
                        "type": "object",
                      },
                    },
                  },
                },
                "responses": {
                  "200": {
                    "content": {
                      "application/json": {
                        "schema": {
                          "properties": {
                            "someProp": {
                              "example": "123",
                              "type": "string",
                            },
                          },
                          "type": "object",
                        },
                      },
                    },
                    "description": "Callback successfully processed and no retries will be performed",
                  },
                },
                "servers": [
                  {
                    "description": "Operation level server 1 (Operation override)",
                    "url": "//callback-url.operation-level/v1",
                  },
                  {
                    "description": "Operation level server 2 (Operation override)",
                    "url": "//callback-url.operation-level/v2",
                  },
                ],
              },
              "servers": [
                {
                  "description": "Path level server 1",
                  "url": "//callback-url.path-level/v1",
                },
                {
                  "description": "Path level server 2",
                  "url": "//callback-url.path-level/v2",
                },
              ],
            },
          },
          "orderShipped": {
            "{$request.body#/callbackUrl}?event={$request.body#/eventName}": {
              "post": {
                "description": "Very long description
Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor
incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis
nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu
fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in
culpa qui officia deserunt mollit anim id est laborum.
",
                "requestBody": {
                  "content": {
                    "application/json": {
                      "schema": {
                        "properties": {
                          "estimatedDeliveryDate": {
                            "example": "2018-11-11T16:00:00Z",
                            "format": "date-time",
                            "type": "string",
                          },
                          "orderId": {
                            "example": "123",
                            "type": "string",
                          },
                          "timestamp": {
                            "example": "2018-10-19T16:46:45Z",
                            "format": "date-time",
                            "type": "string",
                          },
                        },
                        "type": "object",
                      },
                    },
                  },
                },
                "responses": {
                  "200": {
                    "description": "Callback successfully processed and no retries will be performed",
                  },
                },
              },
            },
          },
        },
        "description": "Add subscription for a store events",
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "properties": {
                  "callbackUrl": {
                    "description": "This URL will be called by the server when the desired event will occur",
                    "example": "https://myserver.com/send/callback/here",
                    "format": "uri",
                    "type": "string",
                  },
                  "eventName": {
                    "description": "Event name for the subscription",
                    "enum": [
                      "orderInProgress",
                      "orderShipped",
                      "orderDelivered",
                    ],
                    "example": "orderInProgress",
                    "type": "string",
                  },
                },
                "required": [
                  "callbackUrl",
                  "eventName",
                ],
                "type": "object",
              },
            },
          },
        },
        "responses": {
          "201": {
            "content": {
              "application/json": {
                "schema": {
                  "properties": {
                    "subscriptionId": {
                      "example": "AAA-123-BBB-456",
                      "type": "string",
                    },
                  },
                  "type": "object",
                },
              },
            },
            "description": "Subscription added",
          },
        },
        "summary": "Subscribe to the Store events",
        "tags": [
          "store",
        ],
      },
    },
    "/user": {
      "post": {
        "description": "This can only be done by the logged in user.",
        "operationId": "createUser",
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/User",
              },
            },
          },
          "description": "Created user object",
          "required": true,
        },
        "responses": {
          "default": {
            "description": "successful operation",
          },
        },
        "summary": "Create user",
        "tags": [
          "user",
        ],
      },
    },
    "/user/createWithArray": {
      "post": {
        "description": "",
        "operationId": "createUsersWithArrayInput",
        "requestBody": {
          "$ref": "#/components/requestBodies/UserArray",
        },
        "responses": {
          "default": {
            "description": "successful operation",
          },
        },
        "summary": "Creates list of users with given input array",
        "tags": [
          "user",
        ],
      },
    },
    "/user/createWithList": {
      "post": {
        "description": "",
        "operationId": "createUsersWithListInput",
        "requestBody": {
          "$ref": "#/components/requestBodies/UserArray",
        },
        "responses": {
          "default": {
            "description": "successful operation",
          },
        },
        "summary": "Creates list of users with given input array",
        "tags": [
          "user",
        ],
      },
    },
    "/user/login": {
      "get": {
        "description": "",
        "operationId": "loginUser",
        "parameters": [
          {
            "description": "The user name for login",
            "in": "query",
            "name": "username",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
          {
            "description": "The password for login in clear text",
            "in": "query",
            "name": "password",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "responses": {
          "200": {
            "content": {
              "application/json": {
                "examples": {
                  "response": {
                    "value": "OK",
                  },
                },
                "schema": {
                  "type": "string",
                },
              },
              "application/xml": {
                "examples": {
                  "response": {
                    "value": "<Message> OK </Message>",
                  },
                },
                "schema": {
                  "type": "string",
                },
              },
              "text/plain": {
                "examples": {
                  "response": {
                    "value": "OK",
                  },
                },
              },
            },
            "description": "successful operation",
            "headers": {
              "X-Expires-After": {
                "description": "date in UTC when token expires",
                "schema": {
                  "format": "date-time",
                  "type": "string",
                },
              },
              "X-Rate-Limit": {
                "description": "calls per hour allowed by the user",
                "schema": {
                  "format": "int32",
                  "type": "integer",
                },
              },
            },
          },
          "400": {
            "description": "Invalid username/password supplied",
          },
        },
        "summary": "Logs user into the system",
        "tags": [
          "user",
        ],
      },
    },
    "/user/logout": {
      "get": {
        "description": "",
        "operationId": "logoutUser",
        "responses": {
          "default": {
            "description": "successful operation",
          },
        },
        "summary": "Logs out current logged in user session",
        "tags": [
          "user",
        ],
      },
    },
    "/user/{username}": {
      "delete": {
        "description": "This can only be done by the logged in user.",
        "operationId": "deleteUser",
        "parameters": [
          {
            "description": "The name that needs to be deleted",
            "in": "path",
            "name": "username",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "responses": {
          "400": {
            "description": "Invalid username supplied",
          },
          "404": {
            "description": "User not found",
          },
        },
        "summary": "Delete user",
        "tags": [
          "user",
        ],
      },
      "get": {
        "description": "",
        "operationId": "getUserByName",
        "parameters": [
          {
            "description": "The name that needs to be fetched. Use user1 for testing. ",
            "in": "path",
            "name": "username",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "responses": {
          "200": {
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/User",
                },
              },
              "application/xml": {
                "schema": {
                  "$ref": "#/components/schemas/User",
                },
              },
            },
            "description": "successful operation",
          },
          "400": {
            "description": "Invalid username supplied",
          },
          "404": {
            "description": "User not found",
          },
        },
        "summary": "Get user by user name",
        "tags": [
          "user",
        ],
      },
      "put": {
        "description": "This can only be done by the logged in user.",
        "operationId": "updateUser",
        "parameters": [
          {
            "description": "name that need to be deleted",
            "in": "path",
            "name": "username",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/User",
              },
            },
          },
          "description": "Updated user object",
          "required": true,
        },
        "responses": {
          "400": {
            "description": "Invalid user supplied",
          },
          "404": {
            "description": "User not found",
          },
        },
        "summary": "Updated user",
        "tags": [
          "user",
        ],
      },
    },
  },
  "security": [
    {},
  ],
  "servers": [
    {
      "description": "Default server",
      "url": "//petstore.swagger.io/v2",
    },
    {
      "description": "Sandbox server",
      "url": "//petstore.swagger.io/sandbox",
    },
  ],
  "tags": [
    {
      "description": "Everything about your Pets",
      "name": "pet",
    },
    {
      "description": "Access to Petstore orders",
      "name": "store",
    },
    {
      "description": "Operations about user",
      "name": "user",
    },
    {
      "description": "<SchemaDefinition schemaRef="#/components/schemas/Pet" />
",
      "name": "pet_model",
      "x-displayName": "The Pet Model",
    },
    {
      "description": "<SchemaDefinition schemaRef="#/components/schemas/Order" exampleRef="#/components/examples/Order" showReadOnly={true} showWriteOnly={true} />
",
      "name": "store_model",
      "x-displayName": "The Order Model",
    },
  ],
  "x-tagGroups": [
    {
      "name": "General",
      "tags": [
        "pet",
        "store",
      ],
    },
    {
      "name": "User Management",
      "tags": [
        "user",
      ],
    },
    {
      "name": "Models",
      "tags": [
        "pet_model",
        "store_model",
      ],
    },
  ],
  "x-webhooks": {
    "newPet": {
      "post": {
        "description": "Information about a new pet in the systems",
        "operationId": "newPet",
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/Pet",
              },
            },
          },
        },
        "responses": {
          "200": {
            "description": "Return a 200 status to indicate that the data was received successfully",
          },
        },
        "summary": "New pet",
        "tags": [
          "pet",
        ],
      },
    },
  },
}
`;

exports[`#loadAndBundleSpec should load And Bundle Spec demo/openapi-3-1.yaml 1`] = `
{
  "components": {
    "examples": {
      "Order": {
        "value": {
          "complete": false,
          "quantity": 1,
          "shipDate": "2018-10-19T16:46:45Z",
          "status": "placed",
        },
      },
    },
    "pathItems": {
      "webhooks": {
        "post": {
          "description": "Info about new cat",
          "operationId": "createdCat",
          "requestBody": {
            "content": {
              "multipart/form-data": {
                "schema": {
                  "$ref": "#/components/schemas/Cat",
                },
              },
            },
            "description": "Information about cat in the system",
          },
          "responses": {
            "200": {
              "description": "create Cat details",
            },
          },
          "summary": "Create new cat",
          "tags": [
            "webhooks",
          ],
        },
        "put": {
          "description": "Get a cat details after update",
          "operationId": "updatedCat",
          "requestBody": {
            "content": {
              "multipart/form-data": {
                "schema": {
                  "$ref": "#/components/schemas/Cat",
                },
              },
            },
            "description": "Information about cat in the system",
          },
          "responses": {
            "200": {
              "description": "update Cat details",
            },
          },
          "summary": "Get a cat details after update",
          "tags": [
            "webhooks",
          ],
        },
      },
    },
    "requestBodies": {
      "Pet": {
        "content": {
          "application/json": {
            "schema": {
              "$ref": "#/components/schemas/Pet",
              "description": "My Pet",
              "title": "Pettie",
            },
          },
          "application/xml": {
            "schema": {
              "properties": {
                "name": {
                  "description": "hooray",
                  "type": "string",
                },
              },
              "type": "object",
            },
          },
        },
        "description": "Pet object that needs to be added to the store",
        "required": true,
      },
      "UserArray": {
        "content": {
          "application/json": {
            "schema": {
              "items": {
                "$ref": "#/components/schemas/User",
              },
              "type": "array",
            },
          },
        },
        "description": "List of user object",
        "required": true,
      },
    },
    "schemas": {
      "ApiResponse": {
        "patternProperties": {
          "^O_\\\\w+\\\\.[1-9]{2,4}$": {
            "properties": {
              "nestedProperty": {
                "default": "lazy",
                "description": "The measured skill for hunting",
                "enum": [
                  "clueless",
                  "lazy",
                  "adventurous",
                  "aggressive",
                ],
                "example": "adventurous",
                "type": [
                  "string",
                  "boolean",
                ],
              },
            },
            "type": "object",
          },
          "^S_\\\\w+\\\\.[1-9]{2,4}$": {
            "description": "The measured skill for hunting",
            "else": {
              "maxLength": 10,
              "minLength": 1,
            },
            "if": {
              "x-displayName": "fieldName === 'status'",
            },
            "then": {
              "enum": [
                "success",
                "failed",
              ],
              "format": "url",
              "type": "string",
            },
          },
        },
        "properties": {
          "code": {
            "format": "int32",
            "type": "integer",
          },
          "message": {
            "type": "string",
          },
          "type": {
            "type": "string",
          },
        },
        "type": "object",
      },
      "Cat": {
        "allOf": [
          {
            "$ref": "#/components/schemas/Pet",
          },
          {
            "properties": {
              "huntingSkill": {
                "default": "lazy",
                "description": "The measured skill for hunting",
                "enum": [
                  "clueless",
                  "lazy",
                  "adventurous",
                  "aggressive",
                ],
                "example": "adventurous",
                "type": [
                  "string",
                  "boolean",
                ],
              },
            },
            "required": [
              "huntingSkill",
            ],
            "type": "object",
          },
        ],
        "description": "A representation of a cat",
      },
      "Category": {
        "properties": {
          "id": {
            "$ref": "#/components/schemas/Id",
            "description": "Category ID",
          },
          "name": {
            "description": "Category name",
            "minLength": 1,
            "type": "string",
          },
          "sub": {
            "description": "Test Sub Category",
            "properties": {
              "prop1": {
                "description": "Dumb Property",
                "type": "string",
              },
            },
            "type": "object",
          },
        },
        "type": "object",
        "xml": {
          "name": "Category",
        },
      },
      "Dog": {
        "allOf": [
          {
            "$ref": "#/components/schemas/Pet",
          },
          {
            "properties": {
              "packSize": {
                "default": 1,
                "description": "The size of the pack the dog is from",
                "format": "int32",
                "minimum": 1,
                "type": "integer",
              },
            },
            "required": [
              "packSize",
            ],
            "type": "object",
          },
        ],
        "description": "A representation of a dog",
      },
      "HoneyBee": {
        "allOf": [
          {
            "$ref": "#/components/schemas/Pet",
          },
          {
            "properties": {
              "honeyPerDay": {
                "description": "Average amount of honey produced per day in ounces",
                "example": 3.14,
                "multipleOf": 0.01,
                "type": "number",
              },
            },
            "required": [
              "honeyPerDay",
            ],
            "type": "object",
          },
        ],
        "description": "A representation of a honey bee",
      },
      "Id": {
        "format": "int64",
        "readOnly": true,
        "type": "integer",
      },
      "Order": {
        "properties": {
          "complete": {
            "default": false,
            "description": "Indicates whenever order was completed or not",
            "readOnly": true,
            "type": "boolean",
          },
          "id": {
            "$ref": "#/components/schemas/Id",
            "description": "Order ID",
          },
          "petId": {
            "$ref": "#/components/schemas/Id",
            "description": "Pet ID",
          },
          "quantity": {
            "default": 1,
            "format": "int32",
            "minimum": 1,
            "type": "integer",
          },
          "requestId": {
            "description": "Unique Request Id",
            "type": "string",
            "writeOnly": true,
          },
          "shipDate": {
            "description": "Estimated ship date",
            "format": "date-time",
            "type": "string",
          },
          "status": {
            "description": "Order Status",
            "enum": [
              "placed",
              "approved",
              "delivered",
            ],
            "type": "string",
          },
        },
        "type": "object",
        "xml": {
          "name": "Order",
        },
      },
      "Pet": {
        "discriminator": {
          "mapping": {
            "bee": "#/components/schemas/HoneyBee",
            "cat": "#/components/schemas/Cat",
            "dog": "#/components/schemas/Dog",
          },
          "propertyName": "petType",
        },
        "properties": {
          "category": {
            "$ref": "#/components/schemas/Category",
            "description": "Categories this pet belongs to",
          },
          "friend": {
            "$ref": "#/components/schemas/Pet",
          },
          "huntingSkill": {
            "enum": [
              0,
              1,
              2,
            ],
            "type": [
              "integer",
            ],
          },
          "id": {
            "$ref": "#/components/schemas/Id",
            "description": "Pet ID",
            "externalDocs": {
              "description": "Find more info here",
              "url": "https://example.com",
            },
          },
          "name": {
            "description": "The name given to a pet",
            "example": "Guru",
            "type": "string",
          },
          "petType": {
            "description": "Type of a pet",
            "type": "string",
          },
          "photoUrls": {
            "default": [],
            "description": "The list of URL to a cute photos featuring pet",
            "else": {
              "maxItems": 20,
              "minItems": 1,
              "type": [
                "integer",
                "null",
              ],
              "x-displayName": "notString",
            },
            "if": {
              "type": "string",
              "x-displayName": "isString",
            },
            "items": {
              "format": "url",
              "type": "string",
            },
            "maxItems": 10,
            "minItems": 1,
            "then": {
              "maxItems": 15,
              "minItems": 1,
            },
            "type": [
              "string",
              "integer",
              "null",
            ],
            "xml": {
              "name": "photoUrl",
              "wrapped": true,
            },
          },
          "status": {
            "default": "pending",
            "description": "Pet status in the store",
            "enum": [
              "available",
              "pending",
              "sold",
            ],
            "type": "string",
          },
          "tags": {
            "description": "Tags attached to the pet",
            "exclusiveMaximum": 100,
            "exclusiveMinimum": 0,
            "items": {
              "$ref": "#/components/schemas/Tag",
            },
            "type": "array",
            "xml": {
              "name": "tag",
              "wrapped": true,
            },
          },
        },
        "required": [
          "name",
          "photoUrls",
        ],
        "type": "object",
        "xml": {
          "name": "Pet",
        },
      },
      "Tag": {
        "properties": {
          "id": {
            "$ref": "#/components/schemas/Id",
            "description": "Tag ID",
            "type": "number",
          },
          "name": {
            "description": "Tag name",
            "minLength": 1,
            "type": "string",
          },
        },
        "type": "object",
        "xml": {
          "name": "Tag",
        },
      },
      "User": {
        "else": {
          "required": [],
        },
        "if": {
          "properties": {
            "userStatus": {
              "enum": [
                10,
              ],
            },
          },
          "title": "userStatus === 10",
        },
        "properties": {
          "addresses": {
            "items": {
              "type": "string",
            },
            "maxLength": 10,
            "minItems": 0,
            "prefixItems": [
              {
                "properties": {
                  "city": {
                    "minLength": 0,
                    "type": "string",
                  },
                  "country": {
                    "minLength": 0,
                    "type": "string",
                  },
                  "street": {
                    "description": "includes build/apartment number",
                    "minLength": 0,
                    "type": "string",
                  },
                },
                "type": "object",
              },
              {
                "type": "number",
              },
            ],
            "type": "array",
          },
          "email": {
            "description": "User email address",
            "example": "<EMAIL>",
            "format": "email",
            "type": "string",
          },
          "firstName": {
            "description": "User first name",
            "example": "John",
            "minLength": 1,
            "type": "string",
          },
          "id": {
            "$ref": "#/components/schemas/Id",
          },
          "image": {
            "contentEncoding": "base64",
            "contentMediaType": "image/png",
            "description": "User image",
            "type": "string",
          },
          "lastName": {
            "description": "User last name",
            "example": "Smith",
            "minLength": 1,
            "type": "string",
          },
          "password": {
            "description": "User password, MUST contain a mix of upper and lower case letters, as well as digits",
            "example": "drowssaP123",
            "format": "password",
            "minLength": 8,
            "pattern": "/(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])/",
            "type": "string",
          },
          "pet": {
            "oneOf": [
              {
                "$ref": "#/components/schemas/Pet",
                "title": "Pettie",
              },
              {
                "$ref": "#/components/schemas/Tag",
              },
            ],
          },
          "phone": {
            "description": "User phone number in international format",
            "example": "******-555-0192",
            "pattern": "/^\\+(?:[0-9]-?){6,14}[0-9]$/",
            "type": "string",
          },
          "userStatus": {
            "description": "User status",
            "format": "int32",
            "type": "integer",
          },
          "username": {
            "description": "User supplied username",
            "example": "John78",
            "minLength": 4,
            "type": "string",
          },
        },
        "then": {
          "required": [
            "phone",
          ],
        },
        "type": "object",
        "xml": {
          "name": "User",
        },
      },
    },
    "securitySchemes": {
      "api_key": {
        "description": "For this sample, you can use the api key \`special-key\` to test the authorization filters.
",
        "in": "header",
        "name": "api_key",
        "type": "apiKey",
      },
      "petstore_auth": {
        "description": "Get access to data while protecting your account credentials.
OAuth2 is also a safer and more secure way to give you access.
",
        "flows": {
          "implicit": {
            "authorizationUrl": "http://petstore.swagger.io/api/oauth/dialog",
            "scopes": {
              "read:pets": "read your pets",
              "write:pets": "modify pets in your account",
            },
          },
        },
        "type": "oauth2",
      },
    },
  },
  "externalDocs": {
    "description": "Find out how to create Github repo for your OpenAPI spec.",
    "url": "https://github.com/Rebilly/generator-openapi-repo",
  },
  "info": {
    "contact": {
      "email": "<EMAIL>",
      "name": "API Support",
      "url": "https://github.com/Redocly/redoc",
    },
    "description": "This is a sample server Petstore server.
You can find out more about Swagger at
[http://swagger.io](http://swagger.io) or on [irc.freenode.net, #swagger](http://swagger.io/irc/).
For this sample, you can use the api key \`special-key\` to test the authorization filters.

# Introduction
This API is documented in **OpenAPI format** and is based on
[Petstore sample](http://petstore.swagger.io/) provided by [swagger.io](http://swagger.io) team.
It was **extended** to illustrate features of [generator-openapi-repo](https://github.com/Rebilly/generator-openapi-repo)
tool and [ReDoc](https://github.com/Redocly/redoc) documentation. In addition to standard
OpenAPI syntax we use a few [vendor extensions](https://github.com/Redocly/redoc/blob/main/docs/redoc-vendor-extensions.md).

# OpenAPI Specification
This API is documented in **OpenAPI format** and is based on
[Petstore sample](http://petstore.swagger.io/) provided by [swagger.io](http://swagger.io) team.
It was **extended** to illustrate features of [generator-openapi-repo](https://github.com/Rebilly/generator-openapi-repo)
tool and [ReDoc](https://github.com/Redocly/redoc) documentation. In addition to standard
OpenAPI syntax we use a few [vendor extensions](https://github.com/Redocly/redoc/blob/main/docs/redoc-vendor-extensions.md).

# Cross-Origin Resource Sharing
This API features Cross-Origin Resource Sharing (CORS) implemented in compliance with  [W3C spec](https://www.w3.org/TR/cors/).
And that allows cross-domain communication from the browser.
All responses have a wildcard same-origin which makes them completely public and accessible to everyone, including any code on any site.

# Authentication

Petstore offers two forms of authentication:
  - API Key
  - OAuth2
OAuth2 - an open protocol to allow secure authorization in a simple
and standard method from web, mobile and desktop applications.

<SecurityDefinitions />
",
    "license": {
      "identifier": "Apache 2.0",
      "name": "Apache 2.0",
      "url": "http://www.apache.org/licenses/LICENSE-2.0.html",
    },
    "summary": "My lovely API",
    "termsOfService": "http://swagger.io/terms/",
    "title": "Swagger Petstore",
    "version": "1.0.0",
    "x-logo": {
      "altText": "Petstore logo",
      "url": "https://redocly.github.io/redoc/petstore-logo.png",
    },
  },
  "openapi": "3.1.0",
  "paths": {
    "/pet": {
      "delete": {
        "operationId": "deletePetBy"Id",
        "summary": "OperationId with quotes",
        "tags": [
          "pet",
        ],
      },
      "get": {
        "operationId": "delete\\PetById",
        "summary": "OperationId with backslash",
        "tags": [
          "pet",
        ],
      },
      "parameters": [
        {
          "description": "The language you prefer for messages. Supported values are en-AU, en-CA, en-GB, en-US",
          "example": "en-US",
          "in": "header",
          "name": "Accept-Language",
          "required": false,
          "schema": {
            "default": "en-AU",
            "type": "string",
          },
        },
        {
          "description": "Some cookie",
          "in": "cookie",
          "name": "cookieParam",
          "required": true,
          "schema": {
            "format": "int64",
            "type": "integer",
          },
        },
      ],
      "post": {
        "description": "Add new pet to the store inventory.",
        "operationId": "addPet",
        "requestBody": {
          "$ref": "#/components/requestBodies/Pet",
        },
        "responses": {
          "405": {
            "description": "Invalid input",
          },
        },
        "security": [
          {
            "petstore_auth": [
              "write:pets",
              "read:pets",
            ],
          },
        ],
        "summary": "Add a new pet to the store",
        "tags": [
          "pet",
        ],
        "x-codeSamples": [
          {
            "lang": "C#",
            "source": "PetStore.v1.Pet pet = new PetStore.v1.Pet();
pet.setApiKey("your api key");
pet.petType = PetStore.v1.Pet.TYPE_DOG;
pet.name = "Rex";
// set other fields
PetStoreResponse response = pet.create();
if (response.statusCode == HttpStatusCode.Created)
{
  // Successfully created
}
else
{
  // Something wrong -- check response for errors
  Console.WriteLine(response.getRawResponse());
}
",
          },
          {
            "lang": "PHP",
            "source": "$form = new \\PetStore\\Entities\\Pet();
$form->setPetType("Dog");
$form->setName("Rex");
// set other fields
try {
    $pet = $client->pets()->create($form);
} catch (UnprocessableEntityException $e) {
    var_dump($e->getErrors());
}
",
          },
        ],
      },
      "put": {
        "description": "",
        "operationId": "updatePet",
        "requestBody": {
          "$ref": "#/components/requestBodies/Pet",
        },
        "responses": {
          "400": {
            "description": "Invalid ID supplied",
          },
          "404": {
            "description": "Pet not found",
          },
          "405": {
            "description": "Validation exception",
          },
        },
        "security": [
          {
            "petstore_auth": [
              "write:pets",
              "read:pets",
            ],
          },
        ],
        "summary": "Update an existing pet",
        "tags": [
          "pet",
        ],
        "x-codeSamples": [
          {
            "lang": "PHP",
            "source": "$form = new \\PetStore\\Entities\\Pet();
$form->setPetId(1);
$form->setPetType("Dog");
$form->setName("Rex");
// set other fields
try {
    $pet = $client->pets()->update($form);
} catch (UnprocessableEntityException $e) {
    var_dump($e->getErrors());
}
",
          },
        ],
      },
    },
    "/pet/findByStatus": {
      "get": {
        "description": "Multiple status values can be provided with comma separated strings",
        "operationId": "findPetsByStatus",
        "parameters": [
          {
            "description": "Status values that need to be considered for filter",
            "in": "query",
            "name": "status",
            "required": true,
            "schema": {
              "items": {
                "default": "available",
                "enum": [
                  "available",
                  "pending",
                  "sold",
                ],
                "type": "string",
              },
              "maxItems": 3,
              "minItems": 1,
              "type": "array",
            },
            "style": "form",
          },
        ],
        "responses": {
          "200": {
            "content": {
              "application/json": {
                "schema": {
                  "items": {
                    "$ref": "#/components/schemas/Pet",
                  },
                  "type": "array",
                },
              },
              "application/xml": {
                "schema": {
                  "items": {
                    "$ref": "#/components/schemas/Pet",
                  },
                  "type": "array",
                },
              },
            },
            "description": "successful operation",
          },
          "400": {
            "description": "Invalid status value",
          },
        },
        "security": [
          {
            "petstore_auth": [
              "write:pets",
              "read:pets",
            ],
          },
        ],
        "summary": "Finds Pets by status",
        "tags": [
          "pet",
        ],
      },
    },
    "/pet/findByTags": {
      "get": {
        "deprecated": true,
        "description": "Multiple tags can be provided with comma separated strings. Use tag1, tag2, tag3 for testing.",
        "operationId": "findPetsByTags",
        "parameters": [
          {
            "description": "Tags to filter by",
            "in": "query",
            "name": "tags",
            "required": true,
            "schema": {
              "items": {
                "type": "string",
              },
              "type": "array",
            },
            "style": "form",
          },
        ],
        "responses": {
          "200": {
            "content": {
              "application/json": {
                "schema": {
                  "items": {
                    "$ref": "#/components/schemas/Pet",
                  },
                  "type": "array",
                },
              },
              "application/xml": {
                "schema": {
                  "items": {
                    "$ref": "#/components/schemas/Pet",
                  },
                  "type": "array",
                },
              },
            },
            "description": "successful operation",
          },
          "400": {
            "description": "Invalid tag value",
          },
        },
        "security": [
          {
            "petstore_auth": [
              "write:pets",
              "read:pets",
            ],
          },
        ],
        "summary": "Finds Pets by tags",
        "tags": [
          "pet",
        ],
      },
    },
    "/pet/{petId}": {
      "delete": {
        "description": "",
        "operationId": "deletePet",
        "parameters": [
          {
            "example": "Bearer <TOKEN>",
            "in": "header",
            "name": "api_key",
            "required": false,
            "schema": {
              "type": "string",
            },
          },
          {
            "description": "Pet id to delete",
            "in": "path",
            "name": "petId",
            "required": true,
            "schema": {
              "format": "int64",
              "type": "integer",
            },
          },
        ],
        "responses": {
          "400": {
            "description": "Invalid pet value",
          },
        },
        "security": [
          {
            "petstore_auth": [
              "write:pets",
              "read:pets",
            ],
          },
        ],
        "summary": "Deletes a pet",
        "tags": [
          "pet",
        ],
      },
      "get": {
        "description": "Returns a single pet",
        "operationId": "getPetById",
        "parameters": [
          {
            "deprecated": true,
            "description": "ID of pet to return",
            "in": "path",
            "name": "petId",
            "required": true,
            "schema": {
              "format": "int64",
              "type": "integer",
            },
          },
        ],
        "responses": {
          "200": {
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/Pet",
                },
              },
              "application/xml": {
                "schema": {
                  "$ref": "#/components/schemas/Pet",
                },
              },
            },
            "description": "successful operation",
          },
          "400": {
            "description": "Invalid ID supplied",
          },
          "404": {
            "description": "Pet not found",
          },
        },
        "security": [
          {
            "api_key": [],
          },
        ],
        "summary": "Find pet by ID",
        "tags": [
          "pet",
        ],
      },
      "post": {
        "description": "",
        "operationId": "updatePetWithForm",
        "parameters": [
          {
            "description": "ID of pet that needs to be updated",
            "in": "path",
            "name": "petId",
            "required": true,
            "schema": {
              "format": "int64",
              "type": "integer",
            },
          },
        ],
        "requestBody": {
          "content": {
            "application/x-www-form-urlencoded": {
              "schema": {
                "properties": {
                  "name": {
                    "description": "Updated name of the pet",
                    "type": "string",
                  },
                  "status": {
                    "description": "Updated status of the pet",
                    "type": "string",
                  },
                },
                "type": "object",
              },
            },
          },
        },
        "responses": {
          "405": {
            "description": "Invalid input",
          },
        },
        "security": [
          {
            "petstore_auth": [
              "write:pets",
              "read:pets",
            ],
          },
        ],
        "summary": "Updates a pet in the store with form data",
        "tags": [
          "pet",
        ],
      },
    },
    "/pet/{petId}/uploadImage": {
      "post": {
        "description": "",
        "operationId": "uploadFile",
        "parameters": [
          {
            "description": "ID of pet to update",
            "in": "path",
            "name": "petId",
            "required": true,
            "schema": {
              "format": "int64",
              "type": "integer",
            },
          },
        ],
        "requestBody": {
          "content": {
            "application/octet-stream": {
              "schema": {
                "format": "binary",
                "type": "string",
              },
            },
          },
        },
        "responses": {
          "200": {
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ApiResponse",
                  "unevaluatedProperties": {
                    "format": "int32",
                    "type": "integer",
                  },
                },
              },
            },
            "description": "successful operation",
          },
        },
        "security": [
          {
            "petstore_auth": [
              "write:pets",
              "read:pets",
            ],
          },
        ],
        "summary": "uploads an image",
        "tags": [
          "pet",
        ],
      },
    },
    "/store/inventory": {
      "get": {
        "description": "Returns a map of status codes to quantities",
        "operationId": "getInventory",
        "responses": {
          "200": {
            "content": {
              "application/json": {
                "schema": {
                  "additionalProperties": {
                    "format": "int32",
                    "type": "integer",
                  },
                  "type": "object",
                },
              },
            },
            "description": "successful operation",
          },
        },
        "security": [
          {
            "api_key": [],
          },
        ],
        "summary": "Returns pet inventories by status",
        "tags": [
          "store",
        ],
      },
    },
    "/store/order": {
      "post": {
        "description": "",
        "operationId": "placeOrder",
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/Order",
              },
            },
          },
          "description": "order placed for purchasing the pet",
          "required": true,
        },
        "responses": {
          "200": {
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/Order",
                },
              },
              "application/xml": {
                "schema": {
                  "$ref": "#/components/schemas/Order",
                },
              },
            },
            "description": "successful operation",
          },
          "400": {
            "content": {
              "application/json": {
                "example": {
                  "message": "Invalid Order",
                  "status": 400,
                },
              },
            },
            "description": "Invalid Order",
          },
        },
        "summary": "Place an order for a pet",
        "tags": [
          "store",
        ],
      },
    },
    "/store/order/{orderId}": {
      "delete": {
        "description": "For valid response try integer IDs with value < 1000. Anything above 1000 or nonintegers will generate API errors",
        "operationId": "deleteOrder",
        "parameters": [
          {
            "description": "ID of the order that needs to be deleted",
            "in": "path",
            "name": "orderId",
            "required": true,
            "schema": {
              "minimum": 1,
              "type": "string",
            },
          },
        ],
        "responses": {
          "400": {
            "description": "Invalid ID supplied",
          },
          "404": {
            "description": "Order not found",
          },
        },
        "summary": "Delete purchase order by ID",
        "tags": [
          "store",
        ],
      },
      "get": {
        "description": "For valid response try integer IDs with value <= 5 or > 10. Other values will generated exceptions",
        "operationId": "getOrderById",
        "parameters": [
          {
            "description": "ID of pet that needs to be fetched",
            "in": "path",
            "name": "orderId",
            "required": true,
            "schema": {
              "format": "int64",
              "maximum": 5,
              "minimum": 1,
              "type": "integer",
            },
          },
        ],
        "responses": {
          "200": {
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/Order",
                },
              },
              "application/xml": {
                "schema": {
                  "$ref": "#/components/schemas/Order",
                },
              },
            },
            "description": "successful operation",
          },
          "400": {
            "description": "Invalid ID supplied",
          },
          "404": {
            "description": "Order not found",
          },
        },
        "summary": "Find purchase order by ID",
        "tags": [
          "store",
        ],
      },
    },
    "/store/subscribe": {
      "post": {
        "callbacks": {
          "orderDelivered": {
            "http://notificationServer.com?url={$request.body#/callbackUrl}&event={$request.body#/eventName}": {
              "post": {
                "deprecated": true,
                "description": "A callback triggered every time an Order is delivered to the recipient",
                "requestBody": {
                  "content": {
                    "application/json": {
                      "schema": {
                        "properties": {
                          "orderId": {
                            "example": "123",
                            "type": "string",
                          },
                          "timestamp": {
                            "example": "2018-10-19T16:46:45Z",
                            "format": "date-time",
                            "type": "string",
                          },
                        },
                        "type": "object",
                      },
                    },
                  },
                },
                "responses": {
                  "200": {
                    "description": "Callback successfully processed and no retries will be performed",
                  },
                },
                "summary": "Order delivered",
              },
            },
          },
          "orderInProgress": {
            "{$request.body#/callbackUrl}?event={$request.body#/eventName}": {
              "post": {
                "description": "A callback triggered every time an Order is updated status to "inProgress" (Description)",
                "externalDocs": {
                  "description": "Find out more",
                  "url": "https://more-details.com/demo",
                },
                "requestBody": {
                  "content": {
                    "application/json": {
                      "schema": {
                        "properties": {
                          "orderId": {
                            "example": "123",
                            "type": "string",
                          },
                          "status": {
                            "example": "inProgress",
                            "type": "string",
                          },
                          "timestamp": {
                            "example": "2018-10-19T16:46:45Z",
                            "format": "date-time",
                            "type": "string",
                          },
                        },
                        "type": "object",
                      },
                    },
                    "application/xml": {
                      "example": "<?xml version="1.0" encoding="UTF-8"?>
<root>
  <orderId>123</orderId>
  <status>inProgress</status>
  <timestamp>2018-10-19T16:46:45Z</timestamp>
</root>
",
                      "schema": {
                        "properties": {
                          "orderId": {
                            "example": "123",
                            "type": "string",
                          },
                        },
                        "type": "object",
                      },
                    },
                  },
                },
                "responses": {
                  "200": {
                    "content": {
                      "application/json": {
                        "schema": {
                          "properties": {
                            "someProp": {
                              "example": "123",
                              "type": "string",
                            },
                          },
                          "type": "object",
                        },
                      },
                    },
                    "description": "Callback successfully processed and no retries will be performed",
                  },
                  "299": {
                    "description": "Response for cancelling subscription",
                  },
                  "500": {
                    "description": "Callback processing failed and retries will be performed",
                  },
                },
                "summary": "Order in Progress (Summary)",
                "x-codeSamples": [
                  {
                    "lang": "C#",
                    "source": "PetStore.v1.Pet pet = new PetStore.v1.Pet();
pet.setApiKey("your api key");
pet.petType = PetStore.v1.Pet.TYPE_DOG;
pet.name = "Rex";
// set other fields
PetStoreResponse response = pet.create();
if (response.statusCode == HttpStatusCode.Created)
{
  // Successfully created
}
else
{
  // Something wrong -- check response for errors
  Console.WriteLine(response.getRawResponse());
}
",
                  },
                  {
                    "lang": "PHP",
                    "source": "$form = new \\PetStore\\Entities\\Pet();
$form->setPetType("Dog");
$form->setName("Rex");
// set other fields
try {
    $pet = $client->pets()->create($form);
} catch (UnprocessableEntityException $e) {
    var_dump($e->getErrors());
}
",
                  },
                ],
              },
              "put": {
                "description": "Order in Progress (Only Description)",
                "requestBody": {
                  "content": {
                    "application/json": {
                      "schema": {
                        "properties": {
                          "orderId": {
                            "example": "123",
                            "type": "string",
                          },
                          "status": {
                            "example": "inProgress",
                            "type": "string",
                          },
                          "timestamp": {
                            "example": "2018-10-19T16:46:45Z",
                            "format": "date-time",
                            "type": "string",
                          },
                        },
                        "type": "object",
                      },
                    },
                    "application/xml": {
                      "example": "<?xml version="1.0" encoding="UTF-8"?>
<root>
  <orderId>123</orderId>
  <status>inProgress</status>
  <timestamp>2018-10-19T16:46:45Z</timestamp>
</root>
",
                      "schema": {
                        "properties": {
                          "orderId": {
                            "example": "123",
                            "type": "string",
                          },
                        },
                        "type": "object",
                      },
                    },
                  },
                },
                "responses": {
                  "200": {
                    "content": {
                      "application/json": {
                        "schema": {
                          "properties": {
                            "someProp": {
                              "example": "123",
                              "type": "string",
                            },
                          },
                          "type": "object",
                        },
                      },
                    },
                    "description": "Callback successfully processed and no retries will be performed",
                  },
                },
                "servers": [
                  {
                    "description": "Operation level server 1 (Operation override)",
                    "url": "//callback-url.operation-level/v1",
                  },
                  {
                    "description": "Operation level server 2 (Operation override)",
                    "url": "//callback-url.operation-level/v2",
                  },
                ],
              },
              "servers": [
                {
                  "description": "Path level server 1",
                  "url": "//callback-url.path-level/v1",
                },
                {
                  "description": "Path level server 2",
                  "url": "//callback-url.path-level/v2",
                },
              ],
            },
          },
          "orderShipped": {
            "{$request.body#/callbackUrl}?event={$request.body#/eventName}": {
              "post": {
                "description": "Very long description
Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor
incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis
nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu
fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in
culpa qui officia deserunt mollit anim id est laborum.
",
                "requestBody": {
                  "content": {
                    "application/json": {
                      "schema": {
                        "properties": {
                          "estimatedDeliveryDate": {
                            "example": "2018-11-11T16:00:00Z",
                            "format": "date-time",
                            "type": "string",
                          },
                          "orderId": {
                            "example": "123",
                            "type": "string",
                          },
                          "timestamp": {
                            "example": "2018-10-19T16:46:45Z",
                            "format": "date-time",
                            "type": "string",
                          },
                        },
                        "type": "object",
                      },
                    },
                  },
                },
                "responses": {
                  "200": {
                    "description": "Callback successfully processed and no retries will be performed",
                  },
                },
              },
            },
          },
        },
        "description": "Add subscription for a store events",
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "properties": {
                  "callbackUrl": {
                    "description": "This URL will be called by the server when the desired event will occur",
                    "example": "https://myserver.com/send/callback/here",
                    "format": "uri",
                    "type": "string",
                  },
                  "eventName": {
                    "description": "Event name for the subscription",
                    "enum": [
                      "orderInProgress",
                      "orderShipped",
                      "orderDelivered",
                    ],
                    "example": "orderInProgress",
                    "type": "string",
                  },
                },
                "required": [
                  "callbackUrl",
                  "eventName",
                ],
                "type": "object",
              },
            },
          },
        },
        "responses": {
          "200": {
            "content": {
              "application/json": {
                "schema": {
                  "items": {
                    "items": {
                      "type": "number",
                    },
                    "maxItems": 777,
                    "minItems": 111,
                    "type": "array",
                  },
                  "maxItems": 999,
                  "minItems": 0,
                  "type": "array",
                },
              },
            },
            "description": "Successful operation",
          },
          "201": {
            "content": {
              "application/json": {
                "schema": {
                  "properties": {
                    "subscriptionId": {
                      "example": "AAA-123-BBB-456",
                      "type": "string",
                    },
                  },
                  "type": "object",
                },
              },
            },
            "description": "Subscription added",
          },
        },
        "summary": "Subscribe to the Store events",
        "tags": [
          "store",
        ],
      },
    },
    "/user": {
      "post": {
        "description": "This can only be done by the logged in user.",
        "operationId": "createUser",
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/User",
              },
            },
          },
          "description": "Created user object",
          "required": true,
        },
        "responses": {
          "default": {
            "description": "successful operation",
          },
        },
        "summary": "Create user",
        "tags": [
          "user",
        ],
      },
    },
    "/user/createWithArray": {
      "post": {
        "description": "",
        "operationId": "createUsersWithArrayInput",
        "requestBody": {
          "$ref": "#/components/requestBodies/UserArray",
        },
        "responses": {
          "default": {
            "description": "successful operation",
          },
        },
        "summary": "Creates list of users with given input array",
        "tags": [
          "user",
        ],
      },
    },
    "/user/createWithList": {
      "post": {
        "description": "",
        "operationId": "createUsersWithListInput",
        "requestBody": {
          "$ref": "#/components/requestBodies/UserArray",
        },
        "responses": {
          "default": {
            "description": "successful operation",
          },
        },
        "summary": "Creates list of users with given input array",
        "tags": [
          "user",
        ],
      },
    },
    "/user/login": {
      "get": {
        "description": "",
        "operationId": "loginUser",
        "parameters": [
          {
            "description": "The user name for login",
            "in": "query",
            "name": "username",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
          {
            "description": "The password for login in clear text",
            "in": "query",
            "name": "password",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "responses": {
          "200": {
            "content": {
              "application/json": {
                "examples": {
                  "response": {
                    "value": "OK",
                  },
                },
                "schema": {
                  "type": "string",
                },
              },
              "application/xml": {
                "examples": {
                  "response": {
                    "value": "<Message> OK </Message>",
                  },
                },
                "schema": {
                  "type": "string",
                },
              },
              "text/plain": {
                "examples": {
                  "response": {
                    "value": "OK",
                  },
                },
              },
            },
            "description": "successful operation",
            "headers": {
              "X-Expires-After": {
                "description": "date in UTC when token expires",
                "schema": {
                  "format": "date-time",
                  "type": "string",
                },
              },
              "X-Rate-Limit": {
                "description": "calls per hour allowed by the user",
                "schema": {
                  "format": "int32",
                  "type": "integer",
                },
              },
            },
          },
          "400": {
            "description": "Invalid username/password supplied",
          },
        },
        "summary": "Logs user into the system",
        "tags": [
          "user",
        ],
      },
    },
    "/user/logout": {
      "get": {
        "description": "",
        "operationId": "logoutUser",
        "responses": {
          "default": {
            "description": "successful operation",
          },
        },
        "summary": "Logs out current logged in user session",
        "tags": [
          "user",
        ],
      },
    },
    "/user/{username}": {
      "delete": {
        "description": "This can only be done by the logged in user.",
        "operationId": "deleteUser",
        "parameters": [
          {
            "description": "The name that needs to be deleted",
            "in": "path",
            "name": "username",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "responses": {
          "204": {
            "description": "User is deleted",
          },
          "400": {
            "description": "Invalid username supplied",
          },
          "404": {
            "description": "User not found",
          },
        },
        "summary": "Delete user",
        "tags": [
          "user",
        ],
      },
      "get": {
        "description": "",
        "operationId": "getUserByName",
        "parameters": [
          {
            "description": "The name that needs to be fetched. Use user1 for testing. ",
            "in": "path",
            "name": "username",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "responses": {
          "200": {
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/User",
                },
              },
              "application/xml": {
                "schema": {
                  "$ref": "#/components/schemas/User",
                },
              },
            },
            "description": "successful operation",
          },
          "400": {
            "description": "Invalid username supplied",
          },
          "404": {
            "description": "User not found",
          },
        },
        "summary": "Get user by user name",
        "tags": [
          "user",
        ],
      },
      "put": {
        "description": "This can only be done by the logged in user.",
        "operationId": "updateUser",
        "parameters": [
          {
            "description": "name that need to be updated",
            "in": "path",
            "name": "username",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/User",
              },
            },
          },
          "description": "Updated user object",
          "required": true,
        },
        "responses": {
          "200": {
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/User",
                },
              },
            },
            "description": "User is updated successfully",
          },
          "400": {
            "description": "Invalid user supplied",
          },
          "404": {
            "description": "User not found",
          },
        },
        "summary": "Updated user",
        "tags": [
          "user",
        ],
      },
    },
  },
  "security": [
    {},
  ],
  "servers": [
    {
      "description": "Default server",
      "url": "//petstore.swagger.io/v2",
    },
    {
      "description": "Sandbox server",
      "url": "//petstore.swagger.io/sandbox",
    },
  ],
  "tags": [
    {
      "description": "Everything about your Pets",
      "name": "pet",
    },
    {
      "description": "Access to Petstore orders",
      "name": "store",
    },
    {
      "description": "Operations about user",
      "name": "user",
    },
    {
      "description": "Everything about your Webhooks",
      "name": "webhooks",
    },
    {
      "description": "<SchemaDefinition schemaRef="#/components/schemas/Pet" />
",
      "name": "pet_model",
      "x-displayName": "The Pet Model",
    },
    {
      "description": "<SchemaDefinition schemaRef="#/components/schemas/Order" exampleRef="#/components/examples/Order" showReadOnly={true} showWriteOnly={true} />
",
      "name": "store_model",
      "x-displayName": "The Order Model",
    },
  ],
  "webhooks": {
    "myWebhook": {
      "$ref": "#/components/pathItems/webhooks",
      "description": "Overriding description",
      "summary": "Overriding summary",
    },
    "newPet": {
      "post": {
        "description": "Information about a new pet in the systems",
        "operationId": "newPet",
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/Pet",
              },
            },
          },
        },
        "responses": {
          "200": {
            "description": "Return a 200 status to indicate that the data was received successfully",
          },
        },
        "summary": "New pet",
        "tags": [
          "webhooks",
        ],
      },
    },
  },
  "x-tagGroups": [
    {
      "name": "General",
      "tags": [
        "pet",
        "store",
        "webhooks",
      ],
    },
    {
      "name": "User Management",
      "tags": [
        "user",
      ],
    },
    {
      "name": "Models",
      "tags": [
        "pet_model",
        "store_model",
      ],
    },
  ],
}
`;

exports[`#loadAndBundleSpec should load And Bundle Spec demo/swagger.yaml 1`] = `
{
  "components": {
    "requestBodies": {
      "Pet": {
        "content": {
          "application/json": {
            "schema": {
              "$ref": "#/components/schemas/Pet",
            },
          },
          "application/xml": {
            "schema": {
              "$ref": "#/components/schemas/Pet",
            },
          },
        },
        "description": "Pet object that needs to be added to the store",
        "required": true,
      },
      "UserArray": {
        "content": {
          "application/json": {
            "schema": {
              "items": {
                "$ref": "#/components/schemas/User",
              },
              "type": "array",
            },
          },
        },
        "description": "List of user object",
        "required": true,
      },
    },
    "schemas": {
      "ApiResponse": {
        "properties": {
          "code": {
            "format": "int32",
            "type": "integer",
          },
          "message": {
            "type": "string",
          },
          "type": {
            "type": "string",
          },
        },
        "type": "object",
      },
      "Cat": {
        "allOf": [
          {
            "$ref": "#/components/schemas/Pet",
          },
          {
            "properties": {
              "huntingSkill": {
                "default": "lazy",
                "description": "The measured skill for hunting",
                "enum": [
                  "clueless",
                  "lazy",
                  "adventurous",
                  "aggressive",
                ],
                "type": "string",
              },
            },
            "required": [
              "huntingSkill",
            ],
            "type": "object",
          },
        ],
        "description": "A representation of a cat",
      },
      "Category": {
        "properties": {
          "id": {
            "allOf": [
              {
                "$ref": "#/components/schemas/Id",
              },
            ],
            "description": "Category ID",
          },
          "name": {
            "description": "Category name",
            "minLength": 1,
            "type": "string",
          },
          "sub": {
            "description": "Test Sub Category",
            "properties": {
              "prop1": {
                "description": "Dumb Property",
                "type": "string",
              },
            },
            "type": "object",
          },
        },
        "type": "object",
        "xml": {
          "name": "Category",
        },
      },
      "Dog": {
        "allOf": [
          {
            "$ref": "#/components/schemas/Pet",
          },
          {
            "properties": {
              "packSize": {
                "default": 1,
                "description": "The size of the pack the dog is from",
                "format": "int32",
                "minimum": 1,
                "type": "integer",
              },
            },
            "required": [
              "packSize",
            ],
            "type": "object",
          },
        ],
        "description": "A representation of a dog",
      },
      "HoneyBee": {
        "allOf": [
          {
            "$ref": "#/components/schemas/Pet",
          },
          {
            "properties": {
              "honeyPerDay": {
                "description": "Average amount of honey produced per day in ounces",
                "example": 3.14,
                "type": "number",
              },
            },
            "required": [
              "honeyPerDay",
            ],
            "type": "object",
          },
        ],
        "description": "A representation of a honey bee",
      },
      "Id": {
        "format": "int64",
        "type": "integer",
      },
      "Order": {
        "properties": {
          "complete": {
            "default": false,
            "description": "Indicates whenever order was completed or not",
            "type": "boolean",
          },
          "id": {
            "allOf": [
              {
                "$ref": "#/components/schemas/Id",
              },
            ],
            "description": "Order ID",
          },
          "petId": {
            "allOf": [
              {
                "$ref": "#/components/schemas/Id",
              },
            ],
            "description": "Pet ID",
          },
          "quantity": {
            "default": 1,
            "format": "int32",
            "minimum": 1,
            "type": "integer",
          },
          "shipDate": {
            "description": "Estimated ship date",
            "format": "date-time",
            "type": "string",
          },
          "status": {
            "description": "Order Status",
            "enum": [
              "placed",
              "approved",
              "delivered",
            ],
            "type": "string",
          },
        },
        "type": "object",
        "xml": {
          "name": "Order",
        },
      },
      "Pet": {
        "discriminator": {
          "propertyName": "petType",
        },
        "properties": {
          "category": {
            "allOf": [
              {
                "$ref": "#/components/schemas/Category",
              },
            ],
            "description": "Categories this pet belongs to",
          },
          "id": {
            "allOf": [
              {
                "$ref": "#/components/schemas/Id",
              },
            ],
            "description": "Pet ID",
          },
          "name": {
            "description": "The name given to a pet",
            "example": "Guru",
            "type": "string",
          },
          "petType": {
            "description": "Type of a pet",
            "type": "string",
          },
          "photoUrls": {
            "default": [],
            "description": "The list of URL to a cute photos featuring pet",
            "items": {
              "format": "url",
              "type": "string",
            },
            "type": "array",
            "xml": {
              "name": "photoUrl",
              "wrapped": true,
            },
          },
          "status": {
            "description": "Pet status in the store",
            "enum": [
              "available",
              "pending",
              "sold",
            ],
            "type": "string",
          },
          "tags": {
            "description": "Tags attached to the pet",
            "items": {
              "$ref": "#/components/schemas/Tag",
            },
            "type": "array",
            "xml": {
              "name": "tag",
              "wrapped": true,
            },
          },
        },
        "required": [
          "name",
          "photoUrls",
        ],
        "type": "object",
        "xml": {
          "name": "Pet",
        },
      },
      "Tag": {
        "properties": {
          "id": {
            "allOf": [
              {
                "$ref": "#/components/schemas/Id",
              },
            ],
            "description": "Tag ID",
          },
          "name": {
            "description": "Tag name",
            "minLength": 1,
            "type": "string",
          },
        },
        "type": "object",
        "xml": {
          "name": "Tag",
        },
      },
      "User": {
        "properties": {
          "email": {
            "description": "User email address",
            "example": "<EMAIL>",
            "format": "email",
            "type": "string",
          },
          "firstName": {
            "description": "User first name",
            "example": "John",
            "minLength": 1,
            "type": "string",
          },
          "id": {
            "$ref": "#/components/schemas/Id",
          },
          "lastName": {
            "description": "User last name",
            "example": "Smith",
            "minLength": 1,
            "type": "string",
          },
          "password": {
            "description": "User password, MUST contain a mix of upper and lower case letters, as well as digits",
            "example": "drowssaP123",
            "format": "password",
            "minLength": 8,
            "pattern": "(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])",
            "type": "string",
          },
          "phone": {
            "description": "User phone number in international format",
            "example": "******-555-0192",
            "nullable": true,
            "pattern": "^\\+(?:[0-9]-?){6,14}[0-9]$",
            "type": "string",
          },
          "userStatus": {
            "description": "User status",
            "format": "int32",
            "type": "integer",
          },
          "username": {
            "description": "User supplied username",
            "example": "John78",
            "minLength": 4,
            "type": "string",
          },
        },
        "type": "object",
        "xml": {
          "name": "User",
        },
      },
    },
    "securitySchemes": {
      "api_key": {
        "description": "For this sample, you can use the api key \`special-key\` to test the authorization filters.
",
        "in": "header",
        "name": "api_key",
        "type": "apiKey",
      },
      "petstore_auth": {
        "description": "Get access to data while protecting your account credentials.
OAuth2 is also a safer and more secure way to give you access.
",
        "flows": {
          "implicit": {
            "authorizationUrl": "http://petstore.swagger.io/api/oauth/dialog",
            "scopes": {
              "read:pets": "read your pets",
              "write:pets": "modify pets in your account",
            },
          },
        },
        "type": "oauth2",
      },
    },
  },
  "externalDocs": {
    "description": "Find out how to create Github repo for your OpenAPI spec.",
    "url": "https://github.com/Rebilly/generator-openapi-repo",
  },
  "info": {
    "contact": {
      "email": "<EMAIL>",
      "url": "https://github.com/Redocly/redoc",
    },
    "description": "This is a sample server Petstore server.
You can find out more about Swagger at
[http://swagger.io](http://swagger.io) or on [irc.freenode.net, #swagger](http://swagger.io/irc/).
For this sample, you can use the api key \`special-key\` to test the authorization filters.
# Introduction
This API is documented in **OpenAPI format** and is based on
[Petstore sample](http://petstore.swagger.io/) provided by [swagger.io](http://swagger.io) team.
It was **extended** to illustrate features of [generator-openapi-repo](https://github.com/Rebilly/generator-openapi-repo)
tool and [ReDoc](https://github.com/Redocly/redoc) documentation. In addition to standard
OpenAPI syntax we use a few [vendor extensions](https://github.com/Redocly/redoc/blob/main/docs/redoc-vendor-extensions.md).
# OpenAPI Specification
This API is documented in **OpenAPI format** and is based on
[Petstore sample](http://petstore.swagger.io/) provided by [swagger.io](http://swagger.io) team.
It was **extended** to illustrate features of [generator-openapi-repo](https://github.com/Rebilly/generator-openapi-repo)
tool and [ReDoc](https://github.com/Redocly/redoc) documentation. In addition to standard
OpenAPI syntax we use a few [vendor extensions](https://github.com/Redocly/redoc/blob/main/docs/redoc-vendor-extensions.md).
# Cross-Origin Resource Sharing
This API features Cross-Origin Resource Sharing (CORS) implemented in compliance with  [W3C spec](https://www.w3.org/TR/cors/).
And that allows cross-domain communication from the browser.
All responses have a wildcard same-origin which makes them completely public and accessible to everyone, including any code on any site.
# Authentication
Petstore offers two forms of authentication:
  - API Key
  - OAuth2

OAuth2 - an open protocol to allow secure authorization in a simple
and standard method from web, mobile and desktop applications.
<!-- ReDoc-Inject: <security-definitions> -->
",
    "license": {
      "name": "Apache 2.0",
      "url": "http://www.apache.org/licenses/LICENSE-2.0.html",
    },
    "termsOfService": "http://swagger.io/terms/",
    "title": "Swagger Petstore",
    "version": "1.0.0",
    "x-logo": {
      "altText": "Petstore logo",
      "url": "https://redocly.github.io/redoc/petstore-logo.png",
    },
  },
  "openapi": "3.0.0",
  "paths": {
    "/pet": {
      "post": {
        "description": "Add new pet to the store inventory.",
        "operationId": "addPet",
        "requestBody": {
          "$ref": "#/components/requestBodies/Pet",
        },
        "responses": {
          "405": {
            "description": "Invalid input",
          },
        },
        "security": [
          {
            "petstore_auth": [
              "write:pets",
              "read:pets",
            ],
          },
        ],
        "summary": "Add a new pet to the store",
        "tags": [
          "pet",
        ],
        "x-code-samples": [
          {
            "lang": "C#",
            "source": "PetStore.v1.Pet pet = new PetStore.v1.Pet();
pet.setApiKey("your api key");
pet.petType = PetStore.v1.Pet.TYPE_DOG;
pet.name = "Rex";
// set other fields
PetStoreResponse response = pet.create();
if (response.statusCode == HttpStatusCode.Created)
{
  // Successfully created
}
else
{
  // Something wrong -- check response for errors
  Console.WriteLine(response.getRawResponse());
}
",
          },
          {
            "lang": "PHP",
            "source": "$form = new \\PetStore\\Entities\\Pet();
$form->setPetType("Dog");
$form->setName("Rex");
// set other fields
try {
    $pet = $client->pets()->create($form);
} catch (UnprocessableEntityException $e) {
    var_dump($e->getErrors());
}
",
          },
        ],
      },
      "put": {
        "description": "",
        "operationId": "updatePet",
        "requestBody": {
          "$ref": "#/components/requestBodies/Pet",
        },
        "responses": {
          "400": {
            "description": "Invalid ID supplied",
          },
          "404": {
            "description": "Pet not found",
          },
          "405": {
            "description": "Validation exception",
          },
        },
        "security": [
          {
            "petstore_auth": [
              "write:pets",
              "read:pets",
            ],
          },
        ],
        "summary": "Update an existing pet",
        "tags": [
          "pet",
        ],
        "x-code-samples": [
          {
            "lang": "PHP",
            "source": "$form = new \\PetStore\\Entities\\Pet();
$form->setPetId(1);
$form->setPetType("Dog");
$form->setName("Rex");
// set other fields
try {
    $pet = $client->pets()->update($form);
} catch (UnprocessableEntityException $e) {
    var_dump($e->getErrors());
}
",
          },
        ],
      },
    },
    "/pet/findByStatus": {
      "get": {
        "description": "Multiple status values can be provided with comma separated strings",
        "operationId": "findPetsByStatus",
        "parameters": [
          {
            "description": "Status values that need to be considered for filter",
            "explode": false,
            "in": "query",
            "name": "status",
            "required": true,
            "schema": {
              "items": {
                "default": "available",
                "enum": [
                  "available",
                  "pending",
                  "sold",
                ],
                "type": "string",
              },
              "type": "array",
            },
            "style": "form",
          },
        ],
        "responses": {
          "200": {
            "content": {
              "application/json": {
                "schema": {
                  "items": {
                    "$ref": "#/components/schemas/Pet",
                  },
                  "type": "array",
                },
              },
              "application/xml": {
                "schema": {
                  "items": {
                    "$ref": "#/components/schemas/Pet",
                  },
                  "type": "array",
                },
              },
            },
            "description": "successful operation",
          },
          "400": {
            "description": "Invalid status value",
          },
        },
        "security": [
          {
            "petstore_auth": [
              "write:pets",
              "read:pets",
            ],
          },
        ],
        "summary": "Finds Pets by status",
        "tags": [
          "pet",
        ],
      },
    },
    "/pet/findByTags": {
      "get": {
        "deprecated": true,
        "description": "Multiple tags can be provided with comma separated strings. Use tag1, tag2, tag3 for testing.",
        "operationId": "findPetsByTags",
        "parameters": [
          {
            "description": "Tags to filter by",
            "explode": false,
            "in": "query",
            "name": "tags",
            "required": true,
            "schema": {
              "items": {
                "type": "string",
              },
              "type": "array",
            },
            "style": "form",
          },
        ],
        "responses": {
          "200": {
            "content": {
              "application/json": {
                "schema": {
                  "items": {
                    "$ref": "#/components/schemas/Pet",
                  },
                  "type": "array",
                },
              },
              "application/xml": {
                "schema": {
                  "items": {
                    "$ref": "#/components/schemas/Pet",
                  },
                  "type": "array",
                },
              },
            },
            "description": "successful operation",
          },
          "400": {
            "description": "Invalid tag value",
          },
        },
        "security": [
          {
            "petstore_auth": [
              "write:pets",
              "read:pets",
            ],
          },
        ],
        "summary": "Finds Pets by tags",
        "tags": [
          "pet",
        ],
      },
    },
    "/pet/{petId}": {
      "delete": {
        "description": "",
        "operationId": "deletePet",
        "parameters": [
          {
            "example": "Bearer <TOKEN>",
            "in": "header",
            "name": "api_key",
            "required": false,
            "schema": {
              "type": "string",
            },
          },
          {
            "description": "Pet id to delete",
            "in": "path",
            "name": "petId",
            "required": true,
            "schema": {
              "format": "int64",
              "type": "integer",
            },
          },
        ],
        "responses": {
          "400": {
            "description": "Invalid pet value",
          },
        },
        "security": [
          {
            "petstore_auth": [
              "write:pets",
              "read:pets",
            ],
          },
        ],
        "summary": "Deletes a pet",
        "tags": [
          "pet",
        ],
      },
      "get": {
        "description": "Returns a single pet",
        "operationId": "getPetById",
        "parameters": [
          {
            "description": "ID of pet to return",
            "in": "path",
            "name": "petId",
            "required": true,
            "schema": {
              "format": "int64",
              "type": "integer",
            },
          },
        ],
        "responses": {
          "200": {
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/Pet",
                },
              },
              "application/xml": {
                "schema": {
                  "$ref": "#/components/schemas/Pet",
                },
              },
            },
            "description": "successful operation",
          },
          "400": {
            "description": "Invalid ID supplied",
          },
          "404": {
            "description": "Pet not found",
          },
        },
        "security": [
          {
            "api_key": [],
          },
        ],
        "summary": "Find pet by ID",
        "tags": [
          "pet",
        ],
      },
      "post": {
        "description": "",
        "operationId": "updatePetWithForm",
        "parameters": [
          {
            "description": "ID of pet that needs to be updated",
            "in": "path",
            "name": "petId",
            "required": true,
            "schema": {
              "format": "int64",
              "type": "integer",
            },
          },
        ],
        "requestBody": {
          "content": {
            "application/x-www-form-urlencoded": {
              "schema": {
                "properties": {
                  "name": {
                    "description": "Updated name of the pet",
                    "type": "string",
                  },
                  "status": {
                    "description": "Updated status of the pet",
                    "type": "string",
                  },
                },
                "type": "object",
              },
            },
          },
        },
        "responses": {
          "405": {
            "description": "Invalid input",
          },
        },
        "security": [
          {
            "petstore_auth": [
              "write:pets",
              "read:pets",
            ],
          },
        ],
        "summary": "Updates a pet in the store with form data",
        "tags": [
          "pet",
        ],
      },
    },
    "/pet/{petId}/uploadImage": {
      "post": {
        "description": "",
        "operationId": "uploadFile",
        "parameters": [
          {
            "description": "ID of pet to update",
            "in": "path",
            "name": "petId",
            "required": true,
            "schema": {
              "format": "int64",
              "type": "integer",
            },
          },
        ],
        "requestBody": {
          "content": {
            "multipart/form-data": {
              "schema": {
                "properties": {
                  "additionalMetadata": {
                    "description": "Additional data to pass to server",
                    "type": "string",
                  },
                  "file": {
                    "description": "file to upload",
                    "format": "binary",
                    "type": "string",
                  },
                },
                "type": "object",
              },
            },
          },
        },
        "responses": {
          "200": {
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ApiResponse",
                },
              },
            },
            "description": "successful operation",
          },
        },
        "security": [
          {
            "petstore_auth": [
              "write:pets",
              "read:pets",
            ],
          },
        ],
        "summary": "uploads an image",
        "tags": [
          "pet",
        ],
      },
    },
    "/store/inventory": {
      "get": {
        "description": "Returns a map of status codes to quantities",
        "operationId": "getInventory",
        "responses": {
          "200": {
            "content": {
              "application/json": {
                "schema": {
                  "additionalProperties": {
                    "format": "int32",
                    "type": "integer",
                  },
                  "type": "object",
                },
              },
            },
            "description": "successful operation",
          },
        },
        "security": [
          {
            "api_key": [],
          },
        ],
        "summary": "Returns pet inventories by status",
        "tags": [
          "store",
        ],
      },
    },
    "/store/order": {
      "post": {
        "description": "",
        "operationId": "placeOrder",
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/Order",
              },
            },
          },
          "description": "order placed for purchasing the pet",
          "required": true,
        },
        "responses": {
          "200": {
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/Order",
                },
              },
              "application/xml": {
                "schema": {
                  "$ref": "#/components/schemas/Order",
                },
              },
            },
            "description": "successful operation",
          },
          "400": {
            "description": "Invalid Order",
          },
        },
        "summary": "Place an order for a pet",
        "tags": [
          "store",
        ],
      },
    },
    "/store/order/{orderId}": {
      "delete": {
        "description": "For valid response try integer IDs with value < 1000. Anything above 1000 or nonintegers will generate API errors",
        "operationId": "deleteOrder",
        "parameters": [
          {
            "description": "ID of the order that needs to be deleted",
            "in": "path",
            "name": "orderId",
            "required": true,
            "schema": {
              "minimum": 1,
              "type": "string",
            },
          },
        ],
        "responses": {
          "400": {
            "description": "Invalid ID supplied",
          },
          "404": {
            "description": "Order not found",
          },
        },
        "summary": "Delete purchase order by ID",
        "tags": [
          "store",
        ],
      },
      "get": {
        "description": "For valid response try integer IDs with value <= 5 or > 10. Other values will generated exceptions",
        "operationId": "getOrderById",
        "parameters": [
          {
            "description": "ID of pet that needs to be fetched",
            "in": "path",
            "name": "orderId",
            "required": true,
            "schema": {
              "format": "int64",
              "maximum": 5,
              "minimum": 1,
              "type": "integer",
            },
          },
        ],
        "responses": {
          "200": {
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/Order",
                },
              },
              "application/xml": {
                "schema": {
                  "$ref": "#/components/schemas/Order",
                },
              },
            },
            "description": "successful operation",
          },
          "400": {
            "description": "Invalid ID supplied",
          },
          "404": {
            "description": "Order not found",
          },
        },
        "summary": "Find purchase order by ID",
        "tags": [
          "store",
        ],
      },
    },
    "/user": {
      "post": {
        "description": "This can only be done by the logged in user.",
        "operationId": "createUser",
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/User",
              },
            },
          },
          "description": "Created user object",
          "required": true,
        },
        "responses": {
          "default": {
            "description": "successful operation",
          },
        },
        "summary": "Create user",
        "tags": [
          "user",
        ],
      },
    },
    "/user/createWithArray": {
      "post": {
        "description": "",
        "operationId": "createUsersWithArrayInput",
        "requestBody": {
          "$ref": "#/components/requestBodies/UserArray",
        },
        "responses": {
          "default": {
            "description": "successful operation",
          },
        },
        "summary": "Creates list of users with given input array",
        "tags": [
          "user",
        ],
      },
    },
    "/user/createWithList": {
      "post": {
        "description": "",
        "operationId": "createUsersWithListInput",
        "requestBody": {
          "$ref": "#/components/requestBodies/UserArray",
        },
        "responses": {
          "default": {
            "description": "successful operation",
          },
        },
        "summary": "Creates list of users with given input array",
        "tags": [
          "user",
        ],
      },
    },
    "/user/login": {
      "get": {
        "description": "",
        "operationId": "loginUser",
        "parameters": [
          {
            "description": "The user name for login",
            "in": "query",
            "name": "username",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
          {
            "description": "The password for login in clear text",
            "in": "query",
            "name": "password",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "responses": {
          "200": {
            "content": {
              "application/json": {
                "examples": {
                  "response": {
                    "value": "OK",
                  },
                },
                "schema": {
                  "type": "string",
                },
              },
              "application/xml": {
                "examples": {
                  "response": {
                    "value": "<message> OK </message>",
                  },
                },
                "schema": {
                  "type": "string",
                },
              },
              "text/plain": {
                "examples": {
                  "response": {
                    "value": "OK",
                  },
                },
              },
            },
            "description": "successful operation",
            "headers": {
              "X-Expires-After": {
                "description": "date in UTC when token expires",
                "schema": {
                  "format": "date-time",
                  "type": "string",
                },
              },
              "X-Rate-Limit": {
                "description": "calls per hour allowed by the user",
                "schema": {
                  "format": "int32",
                  "type": "integer",
                },
              },
            },
          },
          "400": {
            "description": "Invalid username/password supplied",
          },
        },
        "summary": "Logs user into the system",
        "tags": [
          "user",
        ],
      },
    },
    "/user/logout": {
      "get": {
        "description": "",
        "operationId": "logoutUser",
        "responses": {
          "default": {
            "description": "successful operation",
          },
        },
        "summary": "Logs out current logged in user session",
        "tags": [
          "user",
        ],
      },
    },
    "/user/{username}": {
      "delete": {
        "description": "This can only be done by the logged in user.",
        "operationId": "deleteUser",
        "parameters": [
          {
            "description": "The name that needs to be deleted",
            "in": "path",
            "name": "username",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "responses": {
          "400": {
            "description": "Invalid username supplied",
          },
          "404": {
            "description": "User not found",
          },
        },
        "summary": "Delete user",
        "tags": [
          "user",
        ],
      },
      "get": {
        "description": "",
        "operationId": "getUserByName",
        "parameters": [
          {
            "description": "The name that needs to be fetched. Use user1 for testing. ",
            "in": "path",
            "name": "username",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "responses": {
          "200": {
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/User",
                },
              },
              "application/xml": {
                "schema": {
                  "$ref": "#/components/schemas/User",
                },
              },
            },
            "description": "successful operation",
          },
          "400": {
            "description": "Invalid username supplied",
          },
          "404": {
            "description": "User not found",
          },
        },
        "summary": "Get user by user name",
        "tags": [
          "user",
        ],
      },
      "put": {
        "description": "This can only be done by the logged in user.",
        "operationId": "updateUser",
        "parameters": [
          {
            "description": "name that need to be deleted",
            "in": "path",
            "name": "username",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/User",
              },
            },
          },
          "description": "Updated user object",
          "required": true,
        },
        "responses": {
          "400": {
            "description": "Invalid user supplied",
          },
          "404": {
            "description": "User not found",
          },
        },
        "summary": "Updated user",
        "tags": [
          "user",
        ],
      },
    },
  },
  "servers": [
    {
      "description": "Default server",
      "url": "//petstore.swagger.io/v2",
    },
    {
      "description": "Sandbox server",
      "url": "//petstore.swagger.io/sandbox",
    },
  ],
  "tags": [
    {
      "description": "Everything about your Pets",
      "name": "pet",
    },
    {
      "description": "Access to Petstore orders",
      "name": "store",
    },
    {
      "description": "Operations about user",
      "name": "user",
    },
  ],
  "x-tagGroups": [
    {
      "name": "General",
      "tags": [
        "pet",
        "store",
      ],
    },
    {
      "name": "User Management",
      "tags": [
        "user",
      ],
    },
  ],
}
`;
