{"openapi": "2.0.0", "components": {"schemas": {"Pet": {"type": "object", "required": ["type"], "discriminator": {"propertyName": "type"}, "properties": {"type": {"type": "string"}}}, "Dog": {"type": "object", "allOf": [{"$ref": "#/components/schemas/Pet"}], "properties": {"packSize": {"type": "number"}}}, "Cat": {"type": "object", "allOf": [{"$ref": "#/components/schemas/Pet"}, {"properties": {"packSize": {"type": "number"}}}]}}}}