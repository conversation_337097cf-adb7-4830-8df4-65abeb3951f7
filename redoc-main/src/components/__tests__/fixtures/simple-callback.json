{"openapi": "3.0.0", "info": {"version": "1.0", "title": "Foo"}, "components": {"callbacks": {"Test": {"/test": {"post": {"operationId": "testCallback", "description": "Test callback.", "requestBody": {"content": {"application/json": {"schema": {"title": "TestTitle", "type": "object", "description": "Test description", "properties": {"type": {"type": "string", "description": "The type of response.", "enum": ["TestResponse.Complete"]}, "status": {"type": "string", "enum": ["FAILURE", "SUCCESS"]}}, "required": ["status"]}}}}, "parameters": [{"name": "X-Test-Header", "in": "header", "required": true, "example": "1", "description": "This is a test header parameter", "schema": {"type": "string"}}], "responses": {"204": {"description": "Test response."}}}}}}}}