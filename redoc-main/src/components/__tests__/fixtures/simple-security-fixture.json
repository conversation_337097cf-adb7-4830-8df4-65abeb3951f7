{"openapi": "3.0", "info": {"title": "test", "version": "0"}, "paths": {"/pet": {"put": {"summary": "Add a new pet to the store", "description": "Add new pet to the store inventory.", "operationId": "updatePet", "responses": {"405": {"description": "Invalid input"}}, "security": [{"GitLab_PersonalAccessToken": [], "GitLab_OpenIdConnect": [], "basicAuth": []}, {"petstore_auth": ["write:pets", "read:pets"]}]}}}, "components": {"securitySchemes": {"petstore_auth": {"description": "Get access to data while protecting your account credentials.\nOAuth2 is also a safer and more secure way to give you access.\n", "type": "oauth2", "bearerFormat": "", "flows": {"implicit": {"authorizationUrl": "http://petstore.swagger.io/api/oauth/dialog", "scopes": {"write:pets": "modify pets in your account", "read:pets": "read your pets"}}}}, "GitLab_PersonalAccessToken": {"description": "GitLab Personal Access Token description", "type": "<PERSON><PERSON><PERSON><PERSON>", "name": "PRIVATE-TOKEN", "in": "header", "bearerFormat": "", "flows": {}}, "GitLab_OpenIdConnect": {"description": "GitLab OpenIdConnect description", "bearerFormat": "", "type": "openIdConnect", "openIdConnectUrl": "https://gitlab.com/.well-known/openid-configuration"}, "basicAuth": {"type": "http", "scheme": "basic"}}}}