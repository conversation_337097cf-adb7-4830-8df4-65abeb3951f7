// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Components SchemaView discriminator should correctly render SchemaView 1`] = `
<Unknown
  discriminator={
    {
      "fieldName": "type",
      "parentSchema": SchemaModel {
        "activeOneOf": 0,
        "const": "",
        "constraints": [],
        "contentEncoding": undefined,
        "contentMediaType": undefined,
        "default": undefined,
        "deprecated": false,
        "description": "",
        "discriminatorProp": "type",
        "displayFormat": undefined,
        "displayType": "object",
        "enum": [],
        "example": undefined,
        "examples": undefined,
        "externalDocs": undefined,
        "format": undefined,
        "isCircular": false,
        "isPrimitive": false,
        "maxItems": undefined,
        "minItems": undefined,
        "oneOf": [
          SchemaModel {
            "activeOneOf": 0,
            "const": "",
            "constraints": [],
            "contentEncoding": undefined,
            "contentMediaType": undefined,
            "default": undefined,
            "deprecated": false,
            "description": "",
            "displayFormat": undefined,
            "displayType": "object",
            "enum": [],
            "example": undefined,
            "examples": undefined,
            "externalDocs": undefined,
            "fields": [
              FieldModel {
                "const": "",
                "deprecated": false,
                "description": "",
                "example": undefined,
                "expanded": undefined,
                "explode": false,
                "in": undefined,
                "kind": "field",
                "name": "packSize",
                "required": false,
                "schema": SchemaModel {
                  "activeOneOf": 0,
                  "const": "",
                  "constraints": [],
                  "contentEncoding": undefined,
                  "contentMediaType": undefined,
                  "default": undefined,
                  "deprecated": false,
                  "description": "",
                  "displayFormat": undefined,
                  "displayType": "number",
                  "enum": [],
                  "example": undefined,
                  "examples": undefined,
                  "externalDocs": undefined,
                  "format": undefined,
                  "isCircular": false,
                  "isPrimitive": true,
                  "maxItems": undefined,
                  "minItems": undefined,
                  "options": RedocNormalizedOptions {
                    "allowedMdComponents": {},
                    "disableSearch": false,
                    "downloadDefinitionUrl": undefined,
                    "downloadFileName": undefined,
                    "downloadUrls": undefined,
                    "enumSkipQuotes": false,
                    "expandDefaultServerVariables": false,
                    "expandResponses": {},
                    "expandSingleSchemaField": false,
                    "generatedSamplesMaxDepth": 10,
                    "hideDownloadButtons": false,
                    "hideFab": false,
                    "hideHostname": false,
                    "hidePropertiesPrefix": true,
                    "hideRequestPayloadSample": false,
                    "hideSchemaPattern": false,
                    "hideSchemaTitles": false,
                    "hideSecuritySection": false,
                    "hideSingleRequestSampleTab": false,
                    "ignoreNamedSchemas": Set {},
                    "jsonSamplesExpandLevel": 2,
                    "maxDisplayedEnumValues": undefined,
                    "menuToggle": true,
                    "minCharacterLengthToInitSearch": 3,
                    "nativeScrollbars": false,
                    "nonce": undefined,
                    "onlyRequiredInSamples": false,
                    "pathInMiddlePanel": false,
                    "payloadSampleIdx": 0,
                    "sanitize": false,
                    "schemaDefinitionsTagName": undefined,
                    "schemasExpansionLevel": 0,
                    "scrollYOffset": [Function],
                    "showExtensions": false,
                    "showObjectSchemaExamples": false,
                    "showSecuritySchemeType": false,
                    "showWebhookVerb": false,
                    "sideNavStyle": "summary-only",
                    "simpleOneOfTypeLabel": false,
                    "sortEnumValuesAlphabetically": false,
                    "sortOperationsAlphabetically": false,
                    "sortPropsAlphabetically": false,
                    "sortRequiredPropsFirst": false,
                    "sortTagsAlphabetically": false,
                    "theme": {
                      "breakpoints": {
                        "large": "105rem",
                        "medium": "75rem",
                        "small": "50rem",
                      },
                      "codeBlock": {
                        "backgroundColor": "#11171a",
                      },
                      "colors": {
                        "border": {
                          "dark": "rgba(0,0,0, 0.1)",
                          "light": "#ffffff",
                        },
                        "error": {
                          "contrastText": "#fff",
                          "dark": "#7a1210",
                          "light": "#eb6d6b",
                          "main": "#d41f1c",
                        },
                        "gray": {
                          "100": "#F5F5F5",
                          "50": "#FAFAFA",
                        },
                        "http": {
                          "basic": "#707070",
                          "delete": "#cc3333",
                          "get": "#2F8132",
                          "head": "#A23DAD",
                          "link": "#07818F",
                          "options": "#947014",
                          "patch": "#bf581d",
                          "post": "#186FAF",
                          "put": "#95507c",
                        },
                        "primary": {
                          "contrastText": "#fff",
                          "dark": "#1a1a51",
                          "light": "#6868cf",
                          "main": "#32329f",
                        },
                        "responses": {
                          "error": {
                            "backgroundColor": "rgba(212,31,28,0.07)",
                            "color": "#d41f1c",
                            "tabTextColor": "#d41f1c",
                          },
                          "info": {
                            "backgroundColor": "rgba(135,206,235,0.1)",
                            "color": "#87ceeb",
                            "tabTextColor": "#87ceeb",
                          },
                          "redirect": {
                            "backgroundColor": "rgba(255,165,0,0.1)",
                            "color": "#ffa500",
                            "tabTextColor": "#ffa500",
                          },
                          "success": {
                            "backgroundColor": "rgba(29,129,39,0.07)",
                            "color": "#1d8127",
                            "tabTextColor": "#1d8127",
                          },
                        },
                        "success": {
                          "contrastText": "#fff",
                          "dark": "#0a2e0e",
                          "light": "#86e490",
                          "main": "#1d8127",
                        },
                        "text": {
                          "primary": "#333333",
                          "secondary": "#666",
                        },
                        "tonalOffset": 0.2,
                        "warning": {
                          "contrastText": "#ffffff",
                          "dark": "#996300",
                          "light": "#ffc966",
                          "main": "#ffa500",
                        },
                      },
                      "extensionsHook": undefined,
                      "fab": {
                        "backgroundColor": "#f2f2f2",
                        "color": "#0065FB",
                      },
                      "logo": {
                        "gutter": "2px",
                        "maxHeight": "260px",
                        "maxWidth": "260px",
                      },
                      "rightPanel": {
                        "backgroundColor": "#263238",
                        "servers": {
                          "overlay": {
                            "backgroundColor": "#fafafa",
                            "textColor": "#263238",
                          },
                          "url": {
                            "backgroundColor": "#fff",
                          },
                        },
                        "textColor": "#ffffff",
                        "width": "40%",
                      },
                      "schema": {
                        "arrow": {
                          "color": "#666",
                          "size": "1.1em",
                        },
                        "defaultDetailsWidth": "75%",
                        "labelsTextSize": "0.9em",
                        "linesColor": "#7c7cbb",
                        "nestedBackground": "#fafafa",
                        "nestingSpacing": "1em",
                        "requireLabelColor": "#d41f1c",
                        "typeNameColor": "#666",
                        "typeTitleColor": "#666",
                      },
                      "sidebar": {
                        "activeTextColor": "#32329f",
                        "arrow": {
                          "color": "#333333",
                          "size": "1.5em",
                        },
                        "backgroundColor": "#fafafa",
                        "groupItems": {
                          "activeBackgroundColor": "#e1e1e1",
                          "activeTextColor": "#32329f",
                          "textTransform": "uppercase",
                        },
                        "level1Items": {
                          "activeBackgroundColor": "#ededed",
                          "activeTextColor": "#32329f",
                          "textTransform": "none",
                        },
                        "textColor": "#333333",
                        "width": "260px",
                      },
                      "spacing": {
                        "sectionHorizontal": 40,
                        "sectionVertical": 40,
                        "unit": 5,
                      },
                      "typography": {
                        "code": {
                          "backgroundColor": "rgba(38, 50, 56, 0.05)",
                          "color": "#e53935",
                          "fontFamily": "Courier, monospace",
                          "fontSize": "13px",
                          "fontWeight": "400",
                          "lineHeight": "1.5em",
                          "wrap": false,
                        },
                        "fontFamily": "Roboto, sans-serif",
                        "fontSize": "14px",
                        "fontWeightBold": "600",
                        "fontWeightLight": "300",
                        "fontWeightRegular": "400",
                        "headings": {
                          "fontFamily": "Montserrat, sans-serif",
                          "fontWeight": "400",
                          "lineHeight": "1.6em",
                        },
                        "lineHeight": "1.5em",
                        "links": {
                          "color": "#32329f",
                          "hover": "#6868cf",
                          "hoverTextDecoration": "auto",
                          "textDecoration": "auto",
                          "visited": "#32329f",
                        },
                        "optimizeSpeed": true,
                        "smoothing": "antialiased",
                      },
                    },
                    "unstable_ignoreMimeParameters": false,
                  },
                  "pattern": undefined,
                  "pointer": "#/components/schemas/Dog/properties/packSize",
                  "rawSchema": {
                    "default": undefined,
                    "type": "number",
                  },
                  "readOnly": false,
                  "refsStack": [
                    "#/components/schemas/Dog",
                    "#/components/schemas/Dog/properties/packSize",
                  ],
                  "schema": {
                    "default": undefined,
                    "type": "number",
                  },
                  "title": "",
                  "type": "number",
                  "typePrefix": "",
                  "writeOnly": false,
                  "x-enumDescriptions": undefined,
                },
              },
              FieldModel {
                "const": "",
                "deprecated": false,
                "description": "",
                "example": undefined,
                "expanded": undefined,
                "explode": false,
                "in": undefined,
                "kind": "field",
                "name": "type",
                "required": true,
                "schema": SchemaModel {
                  "activeOneOf": 0,
                  "const": "",
                  "constraints": [],
                  "contentEncoding": undefined,
                  "contentMediaType": undefined,
                  "default": undefined,
                  "deprecated": false,
                  "description": "",
                  "displayFormat": undefined,
                  "displayType": "string",
                  "enum": [],
                  "example": undefined,
                  "examples": undefined,
                  "externalDocs": undefined,
                  "format": undefined,
                  "isCircular": false,
                  "isPrimitive": true,
                  "maxItems": undefined,
                  "minItems": undefined,
                  "options": RedocNormalizedOptions {
                    "allowedMdComponents": {},
                    "disableSearch": false,
                    "downloadDefinitionUrl": undefined,
                    "downloadFileName": undefined,
                    "downloadUrls": undefined,
                    "enumSkipQuotes": false,
                    "expandDefaultServerVariables": false,
                    "expandResponses": {},
                    "expandSingleSchemaField": false,
                    "generatedSamplesMaxDepth": 10,
                    "hideDownloadButtons": false,
                    "hideFab": false,
                    "hideHostname": false,
                    "hidePropertiesPrefix": true,
                    "hideRequestPayloadSample": false,
                    "hideSchemaPattern": false,
                    "hideSchemaTitles": false,
                    "hideSecuritySection": false,
                    "hideSingleRequestSampleTab": false,
                    "ignoreNamedSchemas": Set {},
                    "jsonSamplesExpandLevel": 2,
                    "maxDisplayedEnumValues": undefined,
                    "menuToggle": true,
                    "minCharacterLengthToInitSearch": 3,
                    "nativeScrollbars": false,
                    "nonce": undefined,
                    "onlyRequiredInSamples": false,
                    "pathInMiddlePanel": false,
                    "payloadSampleIdx": 0,
                    "sanitize": false,
                    "schemaDefinitionsTagName": undefined,
                    "schemasExpansionLevel": 0,
                    "scrollYOffset": [Function],
                    "showExtensions": false,
                    "showObjectSchemaExamples": false,
                    "showSecuritySchemeType": false,
                    "showWebhookVerb": false,
                    "sideNavStyle": "summary-only",
                    "simpleOneOfTypeLabel": false,
                    "sortEnumValuesAlphabetically": false,
                    "sortOperationsAlphabetically": false,
                    "sortPropsAlphabetically": false,
                    "sortRequiredPropsFirst": false,
                    "sortTagsAlphabetically": false,
                    "theme": {
                      "breakpoints": {
                        "large": "105rem",
                        "medium": "75rem",
                        "small": "50rem",
                      },
                      "codeBlock": {
                        "backgroundColor": "#11171a",
                      },
                      "colors": {
                        "border": {
                          "dark": "rgba(0,0,0, 0.1)",
                          "light": "#ffffff",
                        },
                        "error": {
                          "contrastText": "#fff",
                          "dark": "#7a1210",
                          "light": "#eb6d6b",
                          "main": "#d41f1c",
                        },
                        "gray": {
                          "100": "#F5F5F5",
                          "50": "#FAFAFA",
                        },
                        "http": {
                          "basic": "#707070",
                          "delete": "#cc3333",
                          "get": "#2F8132",
                          "head": "#A23DAD",
                          "link": "#07818F",
                          "options": "#947014",
                          "patch": "#bf581d",
                          "post": "#186FAF",
                          "put": "#95507c",
                        },
                        "primary": {
                          "contrastText": "#fff",
                          "dark": "#1a1a51",
                          "light": "#6868cf",
                          "main": "#32329f",
                        },
                        "responses": {
                          "error": {
                            "backgroundColor": "rgba(212,31,28,0.07)",
                            "color": "#d41f1c",
                            "tabTextColor": "#d41f1c",
                          },
                          "info": {
                            "backgroundColor": "rgba(135,206,235,0.1)",
                            "color": "#87ceeb",
                            "tabTextColor": "#87ceeb",
                          },
                          "redirect": {
                            "backgroundColor": "rgba(255,165,0,0.1)",
                            "color": "#ffa500",
                            "tabTextColor": "#ffa500",
                          },
                          "success": {
                            "backgroundColor": "rgba(29,129,39,0.07)",
                            "color": "#1d8127",
                            "tabTextColor": "#1d8127",
                          },
                        },
                        "success": {
                          "contrastText": "#fff",
                          "dark": "#0a2e0e",
                          "light": "#86e490",
                          "main": "#1d8127",
                        },
                        "text": {
                          "primary": "#333333",
                          "secondary": "#666",
                        },
                        "tonalOffset": 0.2,
                        "warning": {
                          "contrastText": "#ffffff",
                          "dark": "#996300",
                          "light": "#ffc966",
                          "main": "#ffa500",
                        },
                      },
                      "extensionsHook": undefined,
                      "fab": {
                        "backgroundColor": "#f2f2f2",
                        "color": "#0065FB",
                      },
                      "logo": {
                        "gutter": "2px",
                        "maxHeight": "260px",
                        "maxWidth": "260px",
                      },
                      "rightPanel": {
                        "backgroundColor": "#263238",
                        "servers": {
                          "overlay": {
                            "backgroundColor": "#fafafa",
                            "textColor": "#263238",
                          },
                          "url": {
                            "backgroundColor": "#fff",
                          },
                        },
                        "textColor": "#ffffff",
                        "width": "40%",
                      },
                      "schema": {
                        "arrow": {
                          "color": "#666",
                          "size": "1.1em",
                        },
                        "defaultDetailsWidth": "75%",
                        "labelsTextSize": "0.9em",
                        "linesColor": "#7c7cbb",
                        "nestedBackground": "#fafafa",
                        "nestingSpacing": "1em",
                        "requireLabelColor": "#d41f1c",
                        "typeNameColor": "#666",
                        "typeTitleColor": "#666",
                      },
                      "sidebar": {
                        "activeTextColor": "#32329f",
                        "arrow": {
                          "color": "#333333",
                          "size": "1.5em",
                        },
                        "backgroundColor": "#fafafa",
                        "groupItems": {
                          "activeBackgroundColor": "#e1e1e1",
                          "activeTextColor": "#32329f",
                          "textTransform": "uppercase",
                        },
                        "level1Items": {
                          "activeBackgroundColor": "#ededed",
                          "activeTextColor": "#32329f",
                          "textTransform": "none",
                        },
                        "textColor": "#333333",
                        "width": "260px",
                      },
                      "spacing": {
                        "sectionHorizontal": 40,
                        "sectionVertical": 40,
                        "unit": 5,
                      },
                      "typography": {
                        "code": {
                          "backgroundColor": "rgba(38, 50, 56, 0.05)",
                          "color": "#e53935",
                          "fontFamily": "Courier, monospace",
                          "fontSize": "13px",
                          "fontWeight": "400",
                          "lineHeight": "1.5em",
                          "wrap": false,
                        },
                        "fontFamily": "Roboto, sans-serif",
                        "fontSize": "14px",
                        "fontWeightBold": "600",
                        "fontWeightLight": "300",
                        "fontWeightRegular": "400",
                        "headings": {
                          "fontFamily": "Montserrat, sans-serif",
                          "fontWeight": "400",
                          "lineHeight": "1.6em",
                        },
                        "lineHeight": "1.5em",
                        "links": {
                          "color": "#32329f",
                          "hover": "#6868cf",
                          "hoverTextDecoration": "auto",
                          "textDecoration": "auto",
                          "visited": "#32329f",
                        },
                        "optimizeSpeed": true,
                        "smoothing": "antialiased",
                      },
                    },
                    "unstable_ignoreMimeParameters": false,
                  },
                  "pattern": undefined,
                  "pointer": "#/components/schemas/Dog/properties/type",
                  "rawSchema": {
                    "default": undefined,
                    "type": "string",
                    "x-refsStack": [
                      "#/components/schemas/Dog",
                      "#/components/schemas/Pet",
                    ],
                  },
                  "readOnly": false,
                  "refsStack": [
                    "#/components/schemas/Dog",
                    "#/components/schemas/Dog",
                    "#/components/schemas/Pet",
                    "#/components/schemas/Dog",
                    "#/components/schemas/Pet",
                    "#/components/schemas/Dog/properties/type",
                  ],
                  "schema": {
                    "default": undefined,
                    "type": "string",
                    "x-refsStack": [
                      "#/components/schemas/Dog",
                      "#/components/schemas/Pet",
                    ],
                  },
                  "title": "",
                  "type": "string",
                  "typePrefix": "",
                  "writeOnly": false,
                  "x-enumDescriptions": undefined,
                },
              },
            ],
            "format": undefined,
            "isCircular": false,
            "isPrimitive": false,
            "maxItems": undefined,
            "minItems": undefined,
            "options": RedocNormalizedOptions {
              "allowedMdComponents": {},
              "disableSearch": false,
              "downloadDefinitionUrl": undefined,
              "downloadFileName": undefined,
              "downloadUrls": undefined,
              "enumSkipQuotes": false,
              "expandDefaultServerVariables": false,
              "expandResponses": {},
              "expandSingleSchemaField": false,
              "generatedSamplesMaxDepth": 10,
              "hideDownloadButtons": false,
              "hideFab": false,
              "hideHostname": false,
              "hidePropertiesPrefix": true,
              "hideRequestPayloadSample": false,
              "hideSchemaPattern": false,
              "hideSchemaTitles": false,
              "hideSecuritySection": false,
              "hideSingleRequestSampleTab": false,
              "ignoreNamedSchemas": Set {},
              "jsonSamplesExpandLevel": 2,
              "maxDisplayedEnumValues": undefined,
              "menuToggle": true,
              "minCharacterLengthToInitSearch": 3,
              "nativeScrollbars": false,
              "nonce": undefined,
              "onlyRequiredInSamples": false,
              "pathInMiddlePanel": false,
              "payloadSampleIdx": 0,
              "sanitize": false,
              "schemaDefinitionsTagName": undefined,
              "schemasExpansionLevel": 0,
              "scrollYOffset": [Function],
              "showExtensions": false,
              "showObjectSchemaExamples": false,
              "showSecuritySchemeType": false,
              "showWebhookVerb": false,
              "sideNavStyle": "summary-only",
              "simpleOneOfTypeLabel": false,
              "sortEnumValuesAlphabetically": false,
              "sortOperationsAlphabetically": false,
              "sortPropsAlphabetically": false,
              "sortRequiredPropsFirst": false,
              "sortTagsAlphabetically": false,
              "theme": {
                "breakpoints": {
                  "large": "105rem",
                  "medium": "75rem",
                  "small": "50rem",
                },
                "codeBlock": {
                  "backgroundColor": "#11171a",
                },
                "colors": {
                  "border": {
                    "dark": "rgba(0,0,0, 0.1)",
                    "light": "#ffffff",
                  },
                  "error": {
                    "contrastText": "#fff",
                    "dark": "#7a1210",
                    "light": "#eb6d6b",
                    "main": "#d41f1c",
                  },
                  "gray": {
                    "100": "#F5F5F5",
                    "50": "#FAFAFA",
                  },
                  "http": {
                    "basic": "#707070",
                    "delete": "#cc3333",
                    "get": "#2F8132",
                    "head": "#A23DAD",
                    "link": "#07818F",
                    "options": "#947014",
                    "patch": "#bf581d",
                    "post": "#186FAF",
                    "put": "#95507c",
                  },
                  "primary": {
                    "contrastText": "#fff",
                    "dark": "#1a1a51",
                    "light": "#6868cf",
                    "main": "#32329f",
                  },
                  "responses": {
                    "error": {
                      "backgroundColor": "rgba(212,31,28,0.07)",
                      "color": "#d41f1c",
                      "tabTextColor": "#d41f1c",
                    },
                    "info": {
                      "backgroundColor": "rgba(135,206,235,0.1)",
                      "color": "#87ceeb",
                      "tabTextColor": "#87ceeb",
                    },
                    "redirect": {
                      "backgroundColor": "rgba(255,165,0,0.1)",
                      "color": "#ffa500",
                      "tabTextColor": "#ffa500",
                    },
                    "success": {
                      "backgroundColor": "rgba(29,129,39,0.07)",
                      "color": "#1d8127",
                      "tabTextColor": "#1d8127",
                    },
                  },
                  "success": {
                    "contrastText": "#fff",
                    "dark": "#0a2e0e",
                    "light": "#86e490",
                    "main": "#1d8127",
                  },
                  "text": {
                    "primary": "#333333",
                    "secondary": "#666",
                  },
                  "tonalOffset": 0.2,
                  "warning": {
                    "contrastText": "#ffffff",
                    "dark": "#996300",
                    "light": "#ffc966",
                    "main": "#ffa500",
                  },
                },
                "extensionsHook": undefined,
                "fab": {
                  "backgroundColor": "#f2f2f2",
                  "color": "#0065FB",
                },
                "logo": {
                  "gutter": "2px",
                  "maxHeight": "260px",
                  "maxWidth": "260px",
                },
                "rightPanel": {
                  "backgroundColor": "#263238",
                  "servers": {
                    "overlay": {
                      "backgroundColor": "#fafafa",
                      "textColor": "#263238",
                    },
                    "url": {
                      "backgroundColor": "#fff",
                    },
                  },
                  "textColor": "#ffffff",
                  "width": "40%",
                },
                "schema": {
                  "arrow": {
                    "color": "#666",
                    "size": "1.1em",
                  },
                  "defaultDetailsWidth": "75%",
                  "labelsTextSize": "0.9em",
                  "linesColor": "#7c7cbb",
                  "nestedBackground": "#fafafa",
                  "nestingSpacing": "1em",
                  "requireLabelColor": "#d41f1c",
                  "typeNameColor": "#666",
                  "typeTitleColor": "#666",
                },
                "sidebar": {
                  "activeTextColor": "#32329f",
                  "arrow": {
                    "color": "#333333",
                    "size": "1.5em",
                  },
                  "backgroundColor": "#fafafa",
                  "groupItems": {
                    "activeBackgroundColor": "#e1e1e1",
                    "activeTextColor": "#32329f",
                    "textTransform": "uppercase",
                  },
                  "level1Items": {
                    "activeBackgroundColor": "#ededed",
                    "activeTextColor": "#32329f",
                    "textTransform": "none",
                  },
                  "textColor": "#333333",
                  "width": "260px",
                },
                "spacing": {
                  "sectionHorizontal": 40,
                  "sectionVertical": 40,
                  "unit": 5,
                },
                "typography": {
                  "code": {
                    "backgroundColor": "rgba(38, 50, 56, 0.05)",
                    "color": "#e53935",
                    "fontFamily": "Courier, monospace",
                    "fontSize": "13px",
                    "fontWeight": "400",
                    "lineHeight": "1.5em",
                    "wrap": false,
                  },
                  "fontFamily": "Roboto, sans-serif",
                  "fontSize": "14px",
                  "fontWeightBold": "600",
                  "fontWeightLight": "300",
                  "fontWeightRegular": "400",
                  "headings": {
                    "fontFamily": "Montserrat, sans-serif",
                    "fontWeight": "400",
                    "lineHeight": "1.6em",
                  },
                  "lineHeight": "1.5em",
                  "links": {
                    "color": "#32329f",
                    "hover": "#6868cf",
                    "hoverTextDecoration": "auto",
                    "textDecoration": "auto",
                    "visited": "#32329f",
                  },
                  "optimizeSpeed": true,
                  "smoothing": "antialiased",
                },
              },
              "unstable_ignoreMimeParameters": false,
            },
            "pattern": undefined,
            "pointer": "#/components/schemas/Dog",
            "rawSchema": {
              "allOf": [
                {
                  "$ref": "#/components/schemas/Pet",
                },
              ],
              "properties": {
                "packSize": {
                  "type": "number",
                },
              },
              "type": "object",
            },
            "readOnly": false,
            "refsStack": [
              "#/components/schemas/Dog",
            ],
            "schema": {
              "allOf": undefined,
              "description": undefined,
              "discriminator": {
                "propertyName": "type",
              },
              "properties": {
                "packSize": {
                  "type": "number",
                },
                "type": {
                  "type": "string",
                  "x-refsStack": [
                    "#/components/schemas/Dog",
                    "#/components/schemas/Pet",
                  ],
                },
              },
              "readOnly": undefined,
              "required": [
                "type",
              ],
              "title": "Dog",
              "type": "object",
              "writeOnly": undefined,
              "x-circular-ref": undefined,
              "x-parentRefs": [
                "#/components/schemas/Pet",
              ],
            },
            "title": "Dog",
            "type": "object",
            "typePrefix": "",
            "writeOnly": false,
            "x-enumDescriptions": undefined,
          },
          SchemaModel {
            "activeOneOf": 0,
            "const": "",
            "constraints": [],
            "contentEncoding": undefined,
            "contentMediaType": undefined,
            "default": undefined,
            "deprecated": false,
            "description": "",
            "displayFormat": undefined,
            "displayType": "object",
            "enum": [],
            "example": undefined,
            "examples": undefined,
            "externalDocs": undefined,
            "fields": [
              FieldModel {
                "const": "",
                "deprecated": false,
                "description": "",
                "example": undefined,
                "expanded": undefined,
                "explode": false,
                "in": undefined,
                "kind": "field",
                "name": "type",
                "required": true,
                "schema": SchemaModel {
                  "activeOneOf": 0,
                  "const": "",
                  "constraints": [],
                  "contentEncoding": undefined,
                  "contentMediaType": undefined,
                  "default": undefined,
                  "deprecated": false,
                  "description": "",
                  "displayFormat": undefined,
                  "displayType": "string",
                  "enum": [],
                  "example": undefined,
                  "examples": undefined,
                  "externalDocs": undefined,
                  "format": undefined,
                  "isCircular": false,
                  "isPrimitive": true,
                  "maxItems": undefined,
                  "minItems": undefined,
                  "options": RedocNormalizedOptions {
                    "allowedMdComponents": {},
                    "disableSearch": false,
                    "downloadDefinitionUrl": undefined,
                    "downloadFileName": undefined,
                    "downloadUrls": undefined,
                    "enumSkipQuotes": false,
                    "expandDefaultServerVariables": false,
                    "expandResponses": {},
                    "expandSingleSchemaField": false,
                    "generatedSamplesMaxDepth": 10,
                    "hideDownloadButtons": false,
                    "hideFab": false,
                    "hideHostname": false,
                    "hidePropertiesPrefix": true,
                    "hideRequestPayloadSample": false,
                    "hideSchemaPattern": false,
                    "hideSchemaTitles": false,
                    "hideSecuritySection": false,
                    "hideSingleRequestSampleTab": false,
                    "ignoreNamedSchemas": Set {},
                    "jsonSamplesExpandLevel": 2,
                    "maxDisplayedEnumValues": undefined,
                    "menuToggle": true,
                    "minCharacterLengthToInitSearch": 3,
                    "nativeScrollbars": false,
                    "nonce": undefined,
                    "onlyRequiredInSamples": false,
                    "pathInMiddlePanel": false,
                    "payloadSampleIdx": 0,
                    "sanitize": false,
                    "schemaDefinitionsTagName": undefined,
                    "schemasExpansionLevel": 0,
                    "scrollYOffset": [Function],
                    "showExtensions": false,
                    "showObjectSchemaExamples": false,
                    "showSecuritySchemeType": false,
                    "showWebhookVerb": false,
                    "sideNavStyle": "summary-only",
                    "simpleOneOfTypeLabel": false,
                    "sortEnumValuesAlphabetically": false,
                    "sortOperationsAlphabetically": false,
                    "sortPropsAlphabetically": false,
                    "sortRequiredPropsFirst": false,
                    "sortTagsAlphabetically": false,
                    "theme": {
                      "breakpoints": {
                        "large": "105rem",
                        "medium": "75rem",
                        "small": "50rem",
                      },
                      "codeBlock": {
                        "backgroundColor": "#11171a",
                      },
                      "colors": {
                        "border": {
                          "dark": "rgba(0,0,0, 0.1)",
                          "light": "#ffffff",
                        },
                        "error": {
                          "contrastText": "#fff",
                          "dark": "#7a1210",
                          "light": "#eb6d6b",
                          "main": "#d41f1c",
                        },
                        "gray": {
                          "100": "#F5F5F5",
                          "50": "#FAFAFA",
                        },
                        "http": {
                          "basic": "#707070",
                          "delete": "#cc3333",
                          "get": "#2F8132",
                          "head": "#A23DAD",
                          "link": "#07818F",
                          "options": "#947014",
                          "patch": "#bf581d",
                          "post": "#186FAF",
                          "put": "#95507c",
                        },
                        "primary": {
                          "contrastText": "#fff",
                          "dark": "#1a1a51",
                          "light": "#6868cf",
                          "main": "#32329f",
                        },
                        "responses": {
                          "error": {
                            "backgroundColor": "rgba(212,31,28,0.07)",
                            "color": "#d41f1c",
                            "tabTextColor": "#d41f1c",
                          },
                          "info": {
                            "backgroundColor": "rgba(135,206,235,0.1)",
                            "color": "#87ceeb",
                            "tabTextColor": "#87ceeb",
                          },
                          "redirect": {
                            "backgroundColor": "rgba(255,165,0,0.1)",
                            "color": "#ffa500",
                            "tabTextColor": "#ffa500",
                          },
                          "success": {
                            "backgroundColor": "rgba(29,129,39,0.07)",
                            "color": "#1d8127",
                            "tabTextColor": "#1d8127",
                          },
                        },
                        "success": {
                          "contrastText": "#fff",
                          "dark": "#0a2e0e",
                          "light": "#86e490",
                          "main": "#1d8127",
                        },
                        "text": {
                          "primary": "#333333",
                          "secondary": "#666",
                        },
                        "tonalOffset": 0.2,
                        "warning": {
                          "contrastText": "#ffffff",
                          "dark": "#996300",
                          "light": "#ffc966",
                          "main": "#ffa500",
                        },
                      },
                      "extensionsHook": undefined,
                      "fab": {
                        "backgroundColor": "#f2f2f2",
                        "color": "#0065FB",
                      },
                      "logo": {
                        "gutter": "2px",
                        "maxHeight": "260px",
                        "maxWidth": "260px",
                      },
                      "rightPanel": {
                        "backgroundColor": "#263238",
                        "servers": {
                          "overlay": {
                            "backgroundColor": "#fafafa",
                            "textColor": "#263238",
                          },
                          "url": {
                            "backgroundColor": "#fff",
                          },
                        },
                        "textColor": "#ffffff",
                        "width": "40%",
                      },
                      "schema": {
                        "arrow": {
                          "color": "#666",
                          "size": "1.1em",
                        },
                        "defaultDetailsWidth": "75%",
                        "labelsTextSize": "0.9em",
                        "linesColor": "#7c7cbb",
                        "nestedBackground": "#fafafa",
                        "nestingSpacing": "1em",
                        "requireLabelColor": "#d41f1c",
                        "typeNameColor": "#666",
                        "typeTitleColor": "#666",
                      },
                      "sidebar": {
                        "activeTextColor": "#32329f",
                        "arrow": {
                          "color": "#333333",
                          "size": "1.5em",
                        },
                        "backgroundColor": "#fafafa",
                        "groupItems": {
                          "activeBackgroundColor": "#e1e1e1",
                          "activeTextColor": "#32329f",
                          "textTransform": "uppercase",
                        },
                        "level1Items": {
                          "activeBackgroundColor": "#ededed",
                          "activeTextColor": "#32329f",
                          "textTransform": "none",
                        },
                        "textColor": "#333333",
                        "width": "260px",
                      },
                      "spacing": {
                        "sectionHorizontal": 40,
                        "sectionVertical": 40,
                        "unit": 5,
                      },
                      "typography": {
                        "code": {
                          "backgroundColor": "rgba(38, 50, 56, 0.05)",
                          "color": "#e53935",
                          "fontFamily": "Courier, monospace",
                          "fontSize": "13px",
                          "fontWeight": "400",
                          "lineHeight": "1.5em",
                          "wrap": false,
                        },
                        "fontFamily": "Roboto, sans-serif",
                        "fontSize": "14px",
                        "fontWeightBold": "600",
                        "fontWeightLight": "300",
                        "fontWeightRegular": "400",
                        "headings": {
                          "fontFamily": "Montserrat, sans-serif",
                          "fontWeight": "400",
                          "lineHeight": "1.6em",
                        },
                        "lineHeight": "1.5em",
                        "links": {
                          "color": "#32329f",
                          "hover": "#6868cf",
                          "hoverTextDecoration": "auto",
                          "textDecoration": "auto",
                          "visited": "#32329f",
                        },
                        "optimizeSpeed": true,
                        "smoothing": "antialiased",
                      },
                    },
                    "unstable_ignoreMimeParameters": false,
                  },
                  "pattern": undefined,
                  "pointer": "#/components/schemas/Cat/properties/type",
                  "rawSchema": {
                    "default": undefined,
                    "type": "string",
                    "x-refsStack": [
                      "#/components/schemas/Cat",
                      "#/components/schemas/Pet",
                    ],
                  },
                  "readOnly": false,
                  "refsStack": [
                    "#/components/schemas/Cat",
                    "#/components/schemas/Cat",
                    "#/components/schemas/Pet",
                    "#/components/schemas/Cat",
                    "#/components/schemas/Pet",
                    "#/components/schemas/Cat/properties/type",
                  ],
                  "schema": {
                    "default": undefined,
                    "type": "string",
                    "x-refsStack": [
                      "#/components/schemas/Cat",
                      "#/components/schemas/Pet",
                    ],
                  },
                  "title": "",
                  "type": "string",
                  "typePrefix": "",
                  "writeOnly": false,
                  "x-enumDescriptions": undefined,
                },
              },
              FieldModel {
                "const": "",
                "deprecated": false,
                "description": "",
                "example": undefined,
                "expanded": undefined,
                "explode": false,
                "in": undefined,
                "kind": "field",
                "name": "packSize",
                "required": false,
                "schema": SchemaModel {
                  "activeOneOf": 0,
                  "const": "",
                  "constraints": [],
                  "contentEncoding": undefined,
                  "contentMediaType": undefined,
                  "default": undefined,
                  "deprecated": false,
                  "description": "",
                  "displayFormat": undefined,
                  "displayType": "number",
                  "enum": [],
                  "example": undefined,
                  "examples": undefined,
                  "externalDocs": undefined,
                  "format": undefined,
                  "isCircular": false,
                  "isPrimitive": true,
                  "maxItems": undefined,
                  "minItems": undefined,
                  "options": RedocNormalizedOptions {
                    "allowedMdComponents": {},
                    "disableSearch": false,
                    "downloadDefinitionUrl": undefined,
                    "downloadFileName": undefined,
                    "downloadUrls": undefined,
                    "enumSkipQuotes": false,
                    "expandDefaultServerVariables": false,
                    "expandResponses": {},
                    "expandSingleSchemaField": false,
                    "generatedSamplesMaxDepth": 10,
                    "hideDownloadButtons": false,
                    "hideFab": false,
                    "hideHostname": false,
                    "hidePropertiesPrefix": true,
                    "hideRequestPayloadSample": false,
                    "hideSchemaPattern": false,
                    "hideSchemaTitles": false,
                    "hideSecuritySection": false,
                    "hideSingleRequestSampleTab": false,
                    "ignoreNamedSchemas": Set {},
                    "jsonSamplesExpandLevel": 2,
                    "maxDisplayedEnumValues": undefined,
                    "menuToggle": true,
                    "minCharacterLengthToInitSearch": 3,
                    "nativeScrollbars": false,
                    "nonce": undefined,
                    "onlyRequiredInSamples": false,
                    "pathInMiddlePanel": false,
                    "payloadSampleIdx": 0,
                    "sanitize": false,
                    "schemaDefinitionsTagName": undefined,
                    "schemasExpansionLevel": 0,
                    "scrollYOffset": [Function],
                    "showExtensions": false,
                    "showObjectSchemaExamples": false,
                    "showSecuritySchemeType": false,
                    "showWebhookVerb": false,
                    "sideNavStyle": "summary-only",
                    "simpleOneOfTypeLabel": false,
                    "sortEnumValuesAlphabetically": false,
                    "sortOperationsAlphabetically": false,
                    "sortPropsAlphabetically": false,
                    "sortRequiredPropsFirst": false,
                    "sortTagsAlphabetically": false,
                    "theme": {
                      "breakpoints": {
                        "large": "105rem",
                        "medium": "75rem",
                        "small": "50rem",
                      },
                      "codeBlock": {
                        "backgroundColor": "#11171a",
                      },
                      "colors": {
                        "border": {
                          "dark": "rgba(0,0,0, 0.1)",
                          "light": "#ffffff",
                        },
                        "error": {
                          "contrastText": "#fff",
                          "dark": "#7a1210",
                          "light": "#eb6d6b",
                          "main": "#d41f1c",
                        },
                        "gray": {
                          "100": "#F5F5F5",
                          "50": "#FAFAFA",
                        },
                        "http": {
                          "basic": "#707070",
                          "delete": "#cc3333",
                          "get": "#2F8132",
                          "head": "#A23DAD",
                          "link": "#07818F",
                          "options": "#947014",
                          "patch": "#bf581d",
                          "post": "#186FAF",
                          "put": "#95507c",
                        },
                        "primary": {
                          "contrastText": "#fff",
                          "dark": "#1a1a51",
                          "light": "#6868cf",
                          "main": "#32329f",
                        },
                        "responses": {
                          "error": {
                            "backgroundColor": "rgba(212,31,28,0.07)",
                            "color": "#d41f1c",
                            "tabTextColor": "#d41f1c",
                          },
                          "info": {
                            "backgroundColor": "rgba(135,206,235,0.1)",
                            "color": "#87ceeb",
                            "tabTextColor": "#87ceeb",
                          },
                          "redirect": {
                            "backgroundColor": "rgba(255,165,0,0.1)",
                            "color": "#ffa500",
                            "tabTextColor": "#ffa500",
                          },
                          "success": {
                            "backgroundColor": "rgba(29,129,39,0.07)",
                            "color": "#1d8127",
                            "tabTextColor": "#1d8127",
                          },
                        },
                        "success": {
                          "contrastText": "#fff",
                          "dark": "#0a2e0e",
                          "light": "#86e490",
                          "main": "#1d8127",
                        },
                        "text": {
                          "primary": "#333333",
                          "secondary": "#666",
                        },
                        "tonalOffset": 0.2,
                        "warning": {
                          "contrastText": "#ffffff",
                          "dark": "#996300",
                          "light": "#ffc966",
                          "main": "#ffa500",
                        },
                      },
                      "extensionsHook": undefined,
                      "fab": {
                        "backgroundColor": "#f2f2f2",
                        "color": "#0065FB",
                      },
                      "logo": {
                        "gutter": "2px",
                        "maxHeight": "260px",
                        "maxWidth": "260px",
                      },
                      "rightPanel": {
                        "backgroundColor": "#263238",
                        "servers": {
                          "overlay": {
                            "backgroundColor": "#fafafa",
                            "textColor": "#263238",
                          },
                          "url": {
                            "backgroundColor": "#fff",
                          },
                        },
                        "textColor": "#ffffff",
                        "width": "40%",
                      },
                      "schema": {
                        "arrow": {
                          "color": "#666",
                          "size": "1.1em",
                        },
                        "defaultDetailsWidth": "75%",
                        "labelsTextSize": "0.9em",
                        "linesColor": "#7c7cbb",
                        "nestedBackground": "#fafafa",
                        "nestingSpacing": "1em",
                        "requireLabelColor": "#d41f1c",
                        "typeNameColor": "#666",
                        "typeTitleColor": "#666",
                      },
                      "sidebar": {
                        "activeTextColor": "#32329f",
                        "arrow": {
                          "color": "#333333",
                          "size": "1.5em",
                        },
                        "backgroundColor": "#fafafa",
                        "groupItems": {
                          "activeBackgroundColor": "#e1e1e1",
                          "activeTextColor": "#32329f",
                          "textTransform": "uppercase",
                        },
                        "level1Items": {
                          "activeBackgroundColor": "#ededed",
                          "activeTextColor": "#32329f",
                          "textTransform": "none",
                        },
                        "textColor": "#333333",
                        "width": "260px",
                      },
                      "spacing": {
                        "sectionHorizontal": 40,
                        "sectionVertical": 40,
                        "unit": 5,
                      },
                      "typography": {
                        "code": {
                          "backgroundColor": "rgba(38, 50, 56, 0.05)",
                          "color": "#e53935",
                          "fontFamily": "Courier, monospace",
                          "fontSize": "13px",
                          "fontWeight": "400",
                          "lineHeight": "1.5em",
                          "wrap": false,
                        },
                        "fontFamily": "Roboto, sans-serif",
                        "fontSize": "14px",
                        "fontWeightBold": "600",
                        "fontWeightLight": "300",
                        "fontWeightRegular": "400",
                        "headings": {
                          "fontFamily": "Montserrat, sans-serif",
                          "fontWeight": "400",
                          "lineHeight": "1.6em",
                        },
                        "lineHeight": "1.5em",
                        "links": {
                          "color": "#32329f",
                          "hover": "#6868cf",
                          "hoverTextDecoration": "auto",
                          "textDecoration": "auto",
                          "visited": "#32329f",
                        },
                        "optimizeSpeed": true,
                        "smoothing": "antialiased",
                      },
                    },
                    "unstable_ignoreMimeParameters": false,
                  },
                  "pattern": undefined,
                  "pointer": "#/components/schemas/Cat/properties/packSize",
                  "rawSchema": {
                    "default": undefined,
                    "type": "number",
                    "x-refsStack": [
                      "#/components/schemas/Cat",
                    ],
                  },
                  "readOnly": false,
                  "refsStack": [
                    "#/components/schemas/Cat",
                    "#/components/schemas/Cat",
                    "#/components/schemas/Cat",
                    "#/components/schemas/Cat/properties/packSize",
                  ],
                  "schema": {
                    "default": undefined,
                    "type": "number",
                    "x-refsStack": [
                      "#/components/schemas/Cat",
                    ],
                  },
                  "title": "",
                  "type": "number",
                  "typePrefix": "",
                  "writeOnly": false,
                  "x-enumDescriptions": undefined,
                },
              },
            ],
            "format": undefined,
            "isCircular": false,
            "isPrimitive": false,
            "maxItems": undefined,
            "minItems": undefined,
            "options": RedocNormalizedOptions {
              "allowedMdComponents": {},
              "disableSearch": false,
              "downloadDefinitionUrl": undefined,
              "downloadFileName": undefined,
              "downloadUrls": undefined,
              "enumSkipQuotes": false,
              "expandDefaultServerVariables": false,
              "expandResponses": {},
              "expandSingleSchemaField": false,
              "generatedSamplesMaxDepth": 10,
              "hideDownloadButtons": false,
              "hideFab": false,
              "hideHostname": false,
              "hidePropertiesPrefix": true,
              "hideRequestPayloadSample": false,
              "hideSchemaPattern": false,
              "hideSchemaTitles": false,
              "hideSecuritySection": false,
              "hideSingleRequestSampleTab": false,
              "ignoreNamedSchemas": Set {},
              "jsonSamplesExpandLevel": 2,
              "maxDisplayedEnumValues": undefined,
              "menuToggle": true,
              "minCharacterLengthToInitSearch": 3,
              "nativeScrollbars": false,
              "nonce": undefined,
              "onlyRequiredInSamples": false,
              "pathInMiddlePanel": false,
              "payloadSampleIdx": 0,
              "sanitize": false,
              "schemaDefinitionsTagName": undefined,
              "schemasExpansionLevel": 0,
              "scrollYOffset": [Function],
              "showExtensions": false,
              "showObjectSchemaExamples": false,
              "showSecuritySchemeType": false,
              "showWebhookVerb": false,
              "sideNavStyle": "summary-only",
              "simpleOneOfTypeLabel": false,
              "sortEnumValuesAlphabetically": false,
              "sortOperationsAlphabetically": false,
              "sortPropsAlphabetically": false,
              "sortRequiredPropsFirst": false,
              "sortTagsAlphabetically": false,
              "theme": {
                "breakpoints": {
                  "large": "105rem",
                  "medium": "75rem",
                  "small": "50rem",
                },
                "codeBlock": {
                  "backgroundColor": "#11171a",
                },
                "colors": {
                  "border": {
                    "dark": "rgba(0,0,0, 0.1)",
                    "light": "#ffffff",
                  },
                  "error": {
                    "contrastText": "#fff",
                    "dark": "#7a1210",
                    "light": "#eb6d6b",
                    "main": "#d41f1c",
                  },
                  "gray": {
                    "100": "#F5F5F5",
                    "50": "#FAFAFA",
                  },
                  "http": {
                    "basic": "#707070",
                    "delete": "#cc3333",
                    "get": "#2F8132",
                    "head": "#A23DAD",
                    "link": "#07818F",
                    "options": "#947014",
                    "patch": "#bf581d",
                    "post": "#186FAF",
                    "put": "#95507c",
                  },
                  "primary": {
                    "contrastText": "#fff",
                    "dark": "#1a1a51",
                    "light": "#6868cf",
                    "main": "#32329f",
                  },
                  "responses": {
                    "error": {
                      "backgroundColor": "rgba(212,31,28,0.07)",
                      "color": "#d41f1c",
                      "tabTextColor": "#d41f1c",
                    },
                    "info": {
                      "backgroundColor": "rgba(135,206,235,0.1)",
                      "color": "#87ceeb",
                      "tabTextColor": "#87ceeb",
                    },
                    "redirect": {
                      "backgroundColor": "rgba(255,165,0,0.1)",
                      "color": "#ffa500",
                      "tabTextColor": "#ffa500",
                    },
                    "success": {
                      "backgroundColor": "rgba(29,129,39,0.07)",
                      "color": "#1d8127",
                      "tabTextColor": "#1d8127",
                    },
                  },
                  "success": {
                    "contrastText": "#fff",
                    "dark": "#0a2e0e",
                    "light": "#86e490",
                    "main": "#1d8127",
                  },
                  "text": {
                    "primary": "#333333",
                    "secondary": "#666",
                  },
                  "tonalOffset": 0.2,
                  "warning": {
                    "contrastText": "#ffffff",
                    "dark": "#996300",
                    "light": "#ffc966",
                    "main": "#ffa500",
                  },
                },
                "extensionsHook": undefined,
                "fab": {
                  "backgroundColor": "#f2f2f2",
                  "color": "#0065FB",
                },
                "logo": {
                  "gutter": "2px",
                  "maxHeight": "260px",
                  "maxWidth": "260px",
                },
                "rightPanel": {
                  "backgroundColor": "#263238",
                  "servers": {
                    "overlay": {
                      "backgroundColor": "#fafafa",
                      "textColor": "#263238",
                    },
                    "url": {
                      "backgroundColor": "#fff",
                    },
                  },
                  "textColor": "#ffffff",
                  "width": "40%",
                },
                "schema": {
                  "arrow": {
                    "color": "#666",
                    "size": "1.1em",
                  },
                  "defaultDetailsWidth": "75%",
                  "labelsTextSize": "0.9em",
                  "linesColor": "#7c7cbb",
                  "nestedBackground": "#fafafa",
                  "nestingSpacing": "1em",
                  "requireLabelColor": "#d41f1c",
                  "typeNameColor": "#666",
                  "typeTitleColor": "#666",
                },
                "sidebar": {
                  "activeTextColor": "#32329f",
                  "arrow": {
                    "color": "#333333",
                    "size": "1.5em",
                  },
                  "backgroundColor": "#fafafa",
                  "groupItems": {
                    "activeBackgroundColor": "#e1e1e1",
                    "activeTextColor": "#32329f",
                    "textTransform": "uppercase",
                  },
                  "level1Items": {
                    "activeBackgroundColor": "#ededed",
                    "activeTextColor": "#32329f",
                    "textTransform": "none",
                  },
                  "textColor": "#333333",
                  "width": "260px",
                },
                "spacing": {
                  "sectionHorizontal": 40,
                  "sectionVertical": 40,
                  "unit": 5,
                },
                "typography": {
                  "code": {
                    "backgroundColor": "rgba(38, 50, 56, 0.05)",
                    "color": "#e53935",
                    "fontFamily": "Courier, monospace",
                    "fontSize": "13px",
                    "fontWeight": "400",
                    "lineHeight": "1.5em",
                    "wrap": false,
                  },
                  "fontFamily": "Roboto, sans-serif",
                  "fontSize": "14px",
                  "fontWeightBold": "600",
                  "fontWeightLight": "300",
                  "fontWeightRegular": "400",
                  "headings": {
                    "fontFamily": "Montserrat, sans-serif",
                    "fontWeight": "400",
                    "lineHeight": "1.6em",
                  },
                  "lineHeight": "1.5em",
                  "links": {
                    "color": "#32329f",
                    "hover": "#6868cf",
                    "hoverTextDecoration": "auto",
                    "textDecoration": "auto",
                    "visited": "#32329f",
                  },
                  "optimizeSpeed": true,
                  "smoothing": "antialiased",
                },
              },
              "unstable_ignoreMimeParameters": false,
            },
            "pattern": undefined,
            "pointer": "#/components/schemas/Cat",
            "rawSchema": {
              "allOf": [
                {
                  "$ref": "#/components/schemas/Pet",
                },
                {
                  "properties": {
                    "packSize": {
                      "type": "number",
                    },
                  },
                },
              ],
              "type": "object",
            },
            "readOnly": false,
            "refsStack": [
              "#/components/schemas/Cat",
            ],
            "schema": {
              "allOf": undefined,
              "description": undefined,
              "discriminator": {
                "propertyName": "type",
              },
              "properties": {
                "packSize": {
                  "type": "number",
                  "x-refsStack": [
                    "#/components/schemas/Cat",
                  ],
                },
                "type": {
                  "type": "string",
                  "x-refsStack": [
                    "#/components/schemas/Cat",
                    "#/components/schemas/Pet",
                  ],
                },
              },
              "readOnly": undefined,
              "required": [
                "type",
              ],
              "title": "Cat",
              "type": "object",
              "writeOnly": undefined,
              "x-circular-ref": undefined,
              "x-parentRefs": [
                "#/components/schemas/Pet",
              ],
            },
            "title": "Cat",
            "type": "object",
            "typePrefix": "",
            "writeOnly": false,
            "x-enumDescriptions": undefined,
          },
        ],
        "options": RedocNormalizedOptions {
          "allowedMdComponents": {},
          "disableSearch": false,
          "downloadDefinitionUrl": undefined,
          "downloadFileName": undefined,
          "downloadUrls": undefined,
          "enumSkipQuotes": false,
          "expandDefaultServerVariables": false,
          "expandResponses": {},
          "expandSingleSchemaField": false,
          "generatedSamplesMaxDepth": 10,
          "hideDownloadButtons": false,
          "hideFab": false,
          "hideHostname": false,
          "hidePropertiesPrefix": true,
          "hideRequestPayloadSample": false,
          "hideSchemaPattern": false,
          "hideSchemaTitles": false,
          "hideSecuritySection": false,
          "hideSingleRequestSampleTab": false,
          "ignoreNamedSchemas": Set {},
          "jsonSamplesExpandLevel": 2,
          "maxDisplayedEnumValues": undefined,
          "menuToggle": true,
          "minCharacterLengthToInitSearch": 3,
          "nativeScrollbars": false,
          "nonce": undefined,
          "onlyRequiredInSamples": false,
          "pathInMiddlePanel": false,
          "payloadSampleIdx": 0,
          "sanitize": false,
          "schemaDefinitionsTagName": undefined,
          "schemasExpansionLevel": 0,
          "scrollYOffset": [Function],
          "showExtensions": false,
          "showObjectSchemaExamples": false,
          "showSecuritySchemeType": false,
          "showWebhookVerb": false,
          "sideNavStyle": "summary-only",
          "simpleOneOfTypeLabel": false,
          "sortEnumValuesAlphabetically": false,
          "sortOperationsAlphabetically": false,
          "sortPropsAlphabetically": false,
          "sortRequiredPropsFirst": false,
          "sortTagsAlphabetically": false,
          "theme": {
            "breakpoints": {
              "large": "105rem",
              "medium": "75rem",
              "small": "50rem",
            },
            "codeBlock": {
              "backgroundColor": "#11171a",
            },
            "colors": {
              "border": {
                "dark": "rgba(0,0,0, 0.1)",
                "light": "#ffffff",
              },
              "error": {
                "contrastText": "#fff",
                "dark": "#7a1210",
                "light": "#eb6d6b",
                "main": "#d41f1c",
              },
              "gray": {
                "100": "#F5F5F5",
                "50": "#FAFAFA",
              },
              "http": {
                "basic": "#707070",
                "delete": "#cc3333",
                "get": "#2F8132",
                "head": "#A23DAD",
                "link": "#07818F",
                "options": "#947014",
                "patch": "#bf581d",
                "post": "#186FAF",
                "put": "#95507c",
              },
              "primary": {
                "contrastText": "#fff",
                "dark": "#1a1a51",
                "light": "#6868cf",
                "main": "#32329f",
              },
              "responses": {
                "error": {
                  "backgroundColor": "rgba(212,31,28,0.07)",
                  "color": "#d41f1c",
                  "tabTextColor": "#d41f1c",
                },
                "info": {
                  "backgroundColor": "rgba(135,206,235,0.1)",
                  "color": "#87ceeb",
                  "tabTextColor": "#87ceeb",
                },
                "redirect": {
                  "backgroundColor": "rgba(255,165,0,0.1)",
                  "color": "#ffa500",
                  "tabTextColor": "#ffa500",
                },
                "success": {
                  "backgroundColor": "rgba(29,129,39,0.07)",
                  "color": "#1d8127",
                  "tabTextColor": "#1d8127",
                },
              },
              "success": {
                "contrastText": "#fff",
                "dark": "#0a2e0e",
                "light": "#86e490",
                "main": "#1d8127",
              },
              "text": {
                "primary": "#333333",
                "secondary": "#666",
              },
              "tonalOffset": 0.2,
              "warning": {
                "contrastText": "#ffffff",
                "dark": "#996300",
                "light": "#ffc966",
                "main": "#ffa500",
              },
            },
            "extensionsHook": undefined,
            "fab": {
              "backgroundColor": "#f2f2f2",
              "color": "#0065FB",
            },
            "logo": {
              "gutter": "2px",
              "maxHeight": "260px",
              "maxWidth": "260px",
            },
            "rightPanel": {
              "backgroundColor": "#263238",
              "servers": {
                "overlay": {
                  "backgroundColor": "#fafafa",
                  "textColor": "#263238",
                },
                "url": {
                  "backgroundColor": "#fff",
                },
              },
              "textColor": "#ffffff",
              "width": "40%",
            },
            "schema": {
              "arrow": {
                "color": "#666",
                "size": "1.1em",
              },
              "defaultDetailsWidth": "75%",
              "labelsTextSize": "0.9em",
              "linesColor": "#7c7cbb",
              "nestedBackground": "#fafafa",
              "nestingSpacing": "1em",
              "requireLabelColor": "#d41f1c",
              "typeNameColor": "#666",
              "typeTitleColor": "#666",
            },
            "sidebar": {
              "activeTextColor": "#32329f",
              "arrow": {
                "color": "#333333",
                "size": "1.5em",
              },
              "backgroundColor": "#fafafa",
              "groupItems": {
                "activeBackgroundColor": "#e1e1e1",
                "activeTextColor": "#32329f",
                "textTransform": "uppercase",
              },
              "level1Items": {
                "activeBackgroundColor": "#ededed",
                "activeTextColor": "#32329f",
                "textTransform": "none",
              },
              "textColor": "#333333",
              "width": "260px",
            },
            "spacing": {
              "sectionHorizontal": 40,
              "sectionVertical": 40,
              "unit": 5,
            },
            "typography": {
              "code": {
                "backgroundColor": "rgba(38, 50, 56, 0.05)",
                "color": "#e53935",
                "fontFamily": "Courier, monospace",
                "fontSize": "13px",
                "fontWeight": "400",
                "lineHeight": "1.5em",
                "wrap": false,
              },
              "fontFamily": "Roboto, sans-serif",
              "fontSize": "14px",
              "fontWeightBold": "600",
              "fontWeightLight": "300",
              "fontWeightRegular": "400",
              "headings": {
                "fontFamily": "Montserrat, sans-serif",
                "fontWeight": "400",
                "lineHeight": "1.6em",
              },
              "lineHeight": "1.5em",
              "links": {
                "color": "#32329f",
                "hover": "#6868cf",
                "hoverTextDecoration": "auto",
                "textDecoration": "auto",
                "visited": "#32329f",
              },
              "optimizeSpeed": true,
              "smoothing": "antialiased",
            },
          },
          "unstable_ignoreMimeParameters": false,
        },
        "pattern": undefined,
        "pointer": "#/components/schemas/Pet",
        "rawSchema": {
          "discriminator": {
            "propertyName": "type",
          },
          "properties": {
            "type": {
              "type": "string",
            },
          },
          "required": [
            "type",
          ],
          "type": "object",
        },
        "readOnly": false,
        "refsStack": [
          "#/components/schemas/Pet",
        ],
        "schema": {
          "discriminator": {
            "propertyName": "type",
          },
          "properties": {
            "type": {
              "type": "string",
            },
          },
          "required": [
            "type",
          ],
          "type": "object",
        },
        "title": "Pet",
        "type": "object",
        "typePrefix": "",
        "writeOnly": false,
        "x-enumDescriptions": undefined,
      },
    }
  }
  level={1}
  schema={
    SchemaModel {
      "activeOneOf": 0,
      "const": "",
      "constraints": [],
      "contentEncoding": undefined,
      "contentMediaType": undefined,
      "default": undefined,
      "deprecated": false,
      "description": "",
      "displayFormat": undefined,
      "displayType": "object",
      "enum": [],
      "example": undefined,
      "examples": undefined,
      "externalDocs": undefined,
      "fields": [
        FieldModel {
          "const": "",
          "deprecated": false,
          "description": "",
          "example": undefined,
          "expanded": undefined,
          "explode": false,
          "in": undefined,
          "kind": "field",
          "name": "packSize",
          "required": false,
          "schema": SchemaModel {
            "activeOneOf": 0,
            "const": "",
            "constraints": [],
            "contentEncoding": undefined,
            "contentMediaType": undefined,
            "default": undefined,
            "deprecated": false,
            "description": "",
            "displayFormat": undefined,
            "displayType": "number",
            "enum": [],
            "example": undefined,
            "examples": undefined,
            "externalDocs": undefined,
            "format": undefined,
            "isCircular": false,
            "isPrimitive": true,
            "maxItems": undefined,
            "minItems": undefined,
            "options": RedocNormalizedOptions {
              "allowedMdComponents": {},
              "disableSearch": false,
              "downloadDefinitionUrl": undefined,
              "downloadFileName": undefined,
              "downloadUrls": undefined,
              "enumSkipQuotes": false,
              "expandDefaultServerVariables": false,
              "expandResponses": {},
              "expandSingleSchemaField": false,
              "generatedSamplesMaxDepth": 10,
              "hideDownloadButtons": false,
              "hideFab": false,
              "hideHostname": false,
              "hidePropertiesPrefix": true,
              "hideRequestPayloadSample": false,
              "hideSchemaPattern": false,
              "hideSchemaTitles": false,
              "hideSecuritySection": false,
              "hideSingleRequestSampleTab": false,
              "ignoreNamedSchemas": Set {},
              "jsonSamplesExpandLevel": 2,
              "maxDisplayedEnumValues": undefined,
              "menuToggle": true,
              "minCharacterLengthToInitSearch": 3,
              "nativeScrollbars": false,
              "nonce": undefined,
              "onlyRequiredInSamples": false,
              "pathInMiddlePanel": false,
              "payloadSampleIdx": 0,
              "sanitize": false,
              "schemaDefinitionsTagName": undefined,
              "schemasExpansionLevel": 0,
              "scrollYOffset": [Function],
              "showExtensions": false,
              "showObjectSchemaExamples": false,
              "showSecuritySchemeType": false,
              "showWebhookVerb": false,
              "sideNavStyle": "summary-only",
              "simpleOneOfTypeLabel": false,
              "sortEnumValuesAlphabetically": false,
              "sortOperationsAlphabetically": false,
              "sortPropsAlphabetically": false,
              "sortRequiredPropsFirst": false,
              "sortTagsAlphabetically": false,
              "theme": {
                "breakpoints": {
                  "large": "105rem",
                  "medium": "75rem",
                  "small": "50rem",
                },
                "codeBlock": {
                  "backgroundColor": "#11171a",
                },
                "colors": {
                  "border": {
                    "dark": "rgba(0,0,0, 0.1)",
                    "light": "#ffffff",
                  },
                  "error": {
                    "contrastText": "#fff",
                    "dark": "#7a1210",
                    "light": "#eb6d6b",
                    "main": "#d41f1c",
                  },
                  "gray": {
                    "100": "#F5F5F5",
                    "50": "#FAFAFA",
                  },
                  "http": {
                    "basic": "#707070",
                    "delete": "#cc3333",
                    "get": "#2F8132",
                    "head": "#A23DAD",
                    "link": "#07818F",
                    "options": "#947014",
                    "patch": "#bf581d",
                    "post": "#186FAF",
                    "put": "#95507c",
                  },
                  "primary": {
                    "contrastText": "#fff",
                    "dark": "#1a1a51",
                    "light": "#6868cf",
                    "main": "#32329f",
                  },
                  "responses": {
                    "error": {
                      "backgroundColor": "rgba(212,31,28,0.07)",
                      "color": "#d41f1c",
                      "tabTextColor": "#d41f1c",
                    },
                    "info": {
                      "backgroundColor": "rgba(135,206,235,0.1)",
                      "color": "#87ceeb",
                      "tabTextColor": "#87ceeb",
                    },
                    "redirect": {
                      "backgroundColor": "rgba(255,165,0,0.1)",
                      "color": "#ffa500",
                      "tabTextColor": "#ffa500",
                    },
                    "success": {
                      "backgroundColor": "rgba(29,129,39,0.07)",
                      "color": "#1d8127",
                      "tabTextColor": "#1d8127",
                    },
                  },
                  "success": {
                    "contrastText": "#fff",
                    "dark": "#0a2e0e",
                    "light": "#86e490",
                    "main": "#1d8127",
                  },
                  "text": {
                    "primary": "#333333",
                    "secondary": "#666",
                  },
                  "tonalOffset": 0.2,
                  "warning": {
                    "contrastText": "#ffffff",
                    "dark": "#996300",
                    "light": "#ffc966",
                    "main": "#ffa500",
                  },
                },
                "extensionsHook": undefined,
                "fab": {
                  "backgroundColor": "#f2f2f2",
                  "color": "#0065FB",
                },
                "logo": {
                  "gutter": "2px",
                  "maxHeight": "260px",
                  "maxWidth": "260px",
                },
                "rightPanel": {
                  "backgroundColor": "#263238",
                  "servers": {
                    "overlay": {
                      "backgroundColor": "#fafafa",
                      "textColor": "#263238",
                    },
                    "url": {
                      "backgroundColor": "#fff",
                    },
                  },
                  "textColor": "#ffffff",
                  "width": "40%",
                },
                "schema": {
                  "arrow": {
                    "color": "#666",
                    "size": "1.1em",
                  },
                  "defaultDetailsWidth": "75%",
                  "labelsTextSize": "0.9em",
                  "linesColor": "#7c7cbb",
                  "nestedBackground": "#fafafa",
                  "nestingSpacing": "1em",
                  "requireLabelColor": "#d41f1c",
                  "typeNameColor": "#666",
                  "typeTitleColor": "#666",
                },
                "sidebar": {
                  "activeTextColor": "#32329f",
                  "arrow": {
                    "color": "#333333",
                    "size": "1.5em",
                  },
                  "backgroundColor": "#fafafa",
                  "groupItems": {
                    "activeBackgroundColor": "#e1e1e1",
                    "activeTextColor": "#32329f",
                    "textTransform": "uppercase",
                  },
                  "level1Items": {
                    "activeBackgroundColor": "#ededed",
                    "activeTextColor": "#32329f",
                    "textTransform": "none",
                  },
                  "textColor": "#333333",
                  "width": "260px",
                },
                "spacing": {
                  "sectionHorizontal": 40,
                  "sectionVertical": 40,
                  "unit": 5,
                },
                "typography": {
                  "code": {
                    "backgroundColor": "rgba(38, 50, 56, 0.05)",
                    "color": "#e53935",
                    "fontFamily": "Courier, monospace",
                    "fontSize": "13px",
                    "fontWeight": "400",
                    "lineHeight": "1.5em",
                    "wrap": false,
                  },
                  "fontFamily": "Roboto, sans-serif",
                  "fontSize": "14px",
                  "fontWeightBold": "600",
                  "fontWeightLight": "300",
                  "fontWeightRegular": "400",
                  "headings": {
                    "fontFamily": "Montserrat, sans-serif",
                    "fontWeight": "400",
                    "lineHeight": "1.6em",
                  },
                  "lineHeight": "1.5em",
                  "links": {
                    "color": "#32329f",
                    "hover": "#6868cf",
                    "hoverTextDecoration": "auto",
                    "textDecoration": "auto",
                    "visited": "#32329f",
                  },
                  "optimizeSpeed": true,
                  "smoothing": "antialiased",
                },
              },
              "unstable_ignoreMimeParameters": false,
            },
            "pattern": undefined,
            "pointer": "#/components/schemas/Dog/properties/packSize",
            "rawSchema": {
              "default": undefined,
              "type": "number",
            },
            "readOnly": false,
            "refsStack": [
              "#/components/schemas/Dog",
              "#/components/schemas/Dog/properties/packSize",
            ],
            "schema": {
              "default": undefined,
              "type": "number",
            },
            "title": "",
            "type": "number",
            "typePrefix": "",
            "writeOnly": false,
            "x-enumDescriptions": undefined,
          },
        },
        FieldModel {
          "const": "",
          "deprecated": false,
          "description": "",
          "example": undefined,
          "expanded": undefined,
          "explode": false,
          "in": undefined,
          "kind": "field",
          "name": "type",
          "required": true,
          "schema": SchemaModel {
            "activeOneOf": 0,
            "const": "",
            "constraints": [],
            "contentEncoding": undefined,
            "contentMediaType": undefined,
            "default": undefined,
            "deprecated": false,
            "description": "",
            "displayFormat": undefined,
            "displayType": "string",
            "enum": [],
            "example": undefined,
            "examples": undefined,
            "externalDocs": undefined,
            "format": undefined,
            "isCircular": false,
            "isPrimitive": true,
            "maxItems": undefined,
            "minItems": undefined,
            "options": RedocNormalizedOptions {
              "allowedMdComponents": {},
              "disableSearch": false,
              "downloadDefinitionUrl": undefined,
              "downloadFileName": undefined,
              "downloadUrls": undefined,
              "enumSkipQuotes": false,
              "expandDefaultServerVariables": false,
              "expandResponses": {},
              "expandSingleSchemaField": false,
              "generatedSamplesMaxDepth": 10,
              "hideDownloadButtons": false,
              "hideFab": false,
              "hideHostname": false,
              "hidePropertiesPrefix": true,
              "hideRequestPayloadSample": false,
              "hideSchemaPattern": false,
              "hideSchemaTitles": false,
              "hideSecuritySection": false,
              "hideSingleRequestSampleTab": false,
              "ignoreNamedSchemas": Set {},
              "jsonSamplesExpandLevel": 2,
              "maxDisplayedEnumValues": undefined,
              "menuToggle": true,
              "minCharacterLengthToInitSearch": 3,
              "nativeScrollbars": false,
              "nonce": undefined,
              "onlyRequiredInSamples": false,
              "pathInMiddlePanel": false,
              "payloadSampleIdx": 0,
              "sanitize": false,
              "schemaDefinitionsTagName": undefined,
              "schemasExpansionLevel": 0,
              "scrollYOffset": [Function],
              "showExtensions": false,
              "showObjectSchemaExamples": false,
              "showSecuritySchemeType": false,
              "showWebhookVerb": false,
              "sideNavStyle": "summary-only",
              "simpleOneOfTypeLabel": false,
              "sortEnumValuesAlphabetically": false,
              "sortOperationsAlphabetically": false,
              "sortPropsAlphabetically": false,
              "sortRequiredPropsFirst": false,
              "sortTagsAlphabetically": false,
              "theme": {
                "breakpoints": {
                  "large": "105rem",
                  "medium": "75rem",
                  "small": "50rem",
                },
                "codeBlock": {
                  "backgroundColor": "#11171a",
                },
                "colors": {
                  "border": {
                    "dark": "rgba(0,0,0, 0.1)",
                    "light": "#ffffff",
                  },
                  "error": {
                    "contrastText": "#fff",
                    "dark": "#7a1210",
                    "light": "#eb6d6b",
                    "main": "#d41f1c",
                  },
                  "gray": {
                    "100": "#F5F5F5",
                    "50": "#FAFAFA",
                  },
                  "http": {
                    "basic": "#707070",
                    "delete": "#cc3333",
                    "get": "#2F8132",
                    "head": "#A23DAD",
                    "link": "#07818F",
                    "options": "#947014",
                    "patch": "#bf581d",
                    "post": "#186FAF",
                    "put": "#95507c",
                  },
                  "primary": {
                    "contrastText": "#fff",
                    "dark": "#1a1a51",
                    "light": "#6868cf",
                    "main": "#32329f",
                  },
                  "responses": {
                    "error": {
                      "backgroundColor": "rgba(212,31,28,0.07)",
                      "color": "#d41f1c",
                      "tabTextColor": "#d41f1c",
                    },
                    "info": {
                      "backgroundColor": "rgba(135,206,235,0.1)",
                      "color": "#87ceeb",
                      "tabTextColor": "#87ceeb",
                    },
                    "redirect": {
                      "backgroundColor": "rgba(255,165,0,0.1)",
                      "color": "#ffa500",
                      "tabTextColor": "#ffa500",
                    },
                    "success": {
                      "backgroundColor": "rgba(29,129,39,0.07)",
                      "color": "#1d8127",
                      "tabTextColor": "#1d8127",
                    },
                  },
                  "success": {
                    "contrastText": "#fff",
                    "dark": "#0a2e0e",
                    "light": "#86e490",
                    "main": "#1d8127",
                  },
                  "text": {
                    "primary": "#333333",
                    "secondary": "#666",
                  },
                  "tonalOffset": 0.2,
                  "warning": {
                    "contrastText": "#ffffff",
                    "dark": "#996300",
                    "light": "#ffc966",
                    "main": "#ffa500",
                  },
                },
                "extensionsHook": undefined,
                "fab": {
                  "backgroundColor": "#f2f2f2",
                  "color": "#0065FB",
                },
                "logo": {
                  "gutter": "2px",
                  "maxHeight": "260px",
                  "maxWidth": "260px",
                },
                "rightPanel": {
                  "backgroundColor": "#263238",
                  "servers": {
                    "overlay": {
                      "backgroundColor": "#fafafa",
                      "textColor": "#263238",
                    },
                    "url": {
                      "backgroundColor": "#fff",
                    },
                  },
                  "textColor": "#ffffff",
                  "width": "40%",
                },
                "schema": {
                  "arrow": {
                    "color": "#666",
                    "size": "1.1em",
                  },
                  "defaultDetailsWidth": "75%",
                  "labelsTextSize": "0.9em",
                  "linesColor": "#7c7cbb",
                  "nestedBackground": "#fafafa",
                  "nestingSpacing": "1em",
                  "requireLabelColor": "#d41f1c",
                  "typeNameColor": "#666",
                  "typeTitleColor": "#666",
                },
                "sidebar": {
                  "activeTextColor": "#32329f",
                  "arrow": {
                    "color": "#333333",
                    "size": "1.5em",
                  },
                  "backgroundColor": "#fafafa",
                  "groupItems": {
                    "activeBackgroundColor": "#e1e1e1",
                    "activeTextColor": "#32329f",
                    "textTransform": "uppercase",
                  },
                  "level1Items": {
                    "activeBackgroundColor": "#ededed",
                    "activeTextColor": "#32329f",
                    "textTransform": "none",
                  },
                  "textColor": "#333333",
                  "width": "260px",
                },
                "spacing": {
                  "sectionHorizontal": 40,
                  "sectionVertical": 40,
                  "unit": 5,
                },
                "typography": {
                  "code": {
                    "backgroundColor": "rgba(38, 50, 56, 0.05)",
                    "color": "#e53935",
                    "fontFamily": "Courier, monospace",
                    "fontSize": "13px",
                    "fontWeight": "400",
                    "lineHeight": "1.5em",
                    "wrap": false,
                  },
                  "fontFamily": "Roboto, sans-serif",
                  "fontSize": "14px",
                  "fontWeightBold": "600",
                  "fontWeightLight": "300",
                  "fontWeightRegular": "400",
                  "headings": {
                    "fontFamily": "Montserrat, sans-serif",
                    "fontWeight": "400",
                    "lineHeight": "1.6em",
                  },
                  "lineHeight": "1.5em",
                  "links": {
                    "color": "#32329f",
                    "hover": "#6868cf",
                    "hoverTextDecoration": "auto",
                    "textDecoration": "auto",
                    "visited": "#32329f",
                  },
                  "optimizeSpeed": true,
                  "smoothing": "antialiased",
                },
              },
              "unstable_ignoreMimeParameters": false,
            },
            "pattern": undefined,
            "pointer": "#/components/schemas/Dog/properties/type",
            "rawSchema": {
              "default": undefined,
              "type": "string",
              "x-refsStack": [
                "#/components/schemas/Dog",
                "#/components/schemas/Pet",
              ],
            },
            "readOnly": false,
            "refsStack": [
              "#/components/schemas/Dog",
              "#/components/schemas/Dog",
              "#/components/schemas/Pet",
              "#/components/schemas/Dog",
              "#/components/schemas/Pet",
              "#/components/schemas/Dog/properties/type",
            ],
            "schema": {
              "default": undefined,
              "type": "string",
              "x-refsStack": [
                "#/components/schemas/Dog",
                "#/components/schemas/Pet",
              ],
            },
            "title": "",
            "type": "string",
            "typePrefix": "",
            "writeOnly": false,
            "x-enumDescriptions": undefined,
          },
        },
      ],
      "format": undefined,
      "isCircular": false,
      "isPrimitive": false,
      "maxItems": undefined,
      "minItems": undefined,
      "options": RedocNormalizedOptions {
        "allowedMdComponents": {},
        "disableSearch": false,
        "downloadDefinitionUrl": undefined,
        "downloadFileName": undefined,
        "downloadUrls": undefined,
        "enumSkipQuotes": false,
        "expandDefaultServerVariables": false,
        "expandResponses": {},
        "expandSingleSchemaField": false,
        "generatedSamplesMaxDepth": 10,
        "hideDownloadButtons": false,
        "hideFab": false,
        "hideHostname": false,
        "hidePropertiesPrefix": true,
        "hideRequestPayloadSample": false,
        "hideSchemaPattern": false,
        "hideSchemaTitles": false,
        "hideSecuritySection": false,
        "hideSingleRequestSampleTab": false,
        "ignoreNamedSchemas": Set {},
        "jsonSamplesExpandLevel": 2,
        "maxDisplayedEnumValues": undefined,
        "menuToggle": true,
        "minCharacterLengthToInitSearch": 3,
        "nativeScrollbars": false,
        "nonce": undefined,
        "onlyRequiredInSamples": false,
        "pathInMiddlePanel": false,
        "payloadSampleIdx": 0,
        "sanitize": false,
        "schemaDefinitionsTagName": undefined,
        "schemasExpansionLevel": 0,
        "scrollYOffset": [Function],
        "showExtensions": false,
        "showObjectSchemaExamples": false,
        "showSecuritySchemeType": false,
        "showWebhookVerb": false,
        "sideNavStyle": "summary-only",
        "simpleOneOfTypeLabel": false,
        "sortEnumValuesAlphabetically": false,
        "sortOperationsAlphabetically": false,
        "sortPropsAlphabetically": false,
        "sortRequiredPropsFirst": false,
        "sortTagsAlphabetically": false,
        "theme": {
          "breakpoints": {
            "large": "105rem",
            "medium": "75rem",
            "small": "50rem",
          },
          "codeBlock": {
            "backgroundColor": "#11171a",
          },
          "colors": {
            "border": {
              "dark": "rgba(0,0,0, 0.1)",
              "light": "#ffffff",
            },
            "error": {
              "contrastText": "#fff",
              "dark": "#7a1210",
              "light": "#eb6d6b",
              "main": "#d41f1c",
            },
            "gray": {
              "100": "#F5F5F5",
              "50": "#FAFAFA",
            },
            "http": {
              "basic": "#707070",
              "delete": "#cc3333",
              "get": "#2F8132",
              "head": "#A23DAD",
              "link": "#07818F",
              "options": "#947014",
              "patch": "#bf581d",
              "post": "#186FAF",
              "put": "#95507c",
            },
            "primary": {
              "contrastText": "#fff",
              "dark": "#1a1a51",
              "light": "#6868cf",
              "main": "#32329f",
            },
            "responses": {
              "error": {
                "backgroundColor": "rgba(212,31,28,0.07)",
                "color": "#d41f1c",
                "tabTextColor": "#d41f1c",
              },
              "info": {
                "backgroundColor": "rgba(135,206,235,0.1)",
                "color": "#87ceeb",
                "tabTextColor": "#87ceeb",
              },
              "redirect": {
                "backgroundColor": "rgba(255,165,0,0.1)",
                "color": "#ffa500",
                "tabTextColor": "#ffa500",
              },
              "success": {
                "backgroundColor": "rgba(29,129,39,0.07)",
                "color": "#1d8127",
                "tabTextColor": "#1d8127",
              },
            },
            "success": {
              "contrastText": "#fff",
              "dark": "#0a2e0e",
              "light": "#86e490",
              "main": "#1d8127",
            },
            "text": {
              "primary": "#333333",
              "secondary": "#666",
            },
            "tonalOffset": 0.2,
            "warning": {
              "contrastText": "#ffffff",
              "dark": "#996300",
              "light": "#ffc966",
              "main": "#ffa500",
            },
          },
          "extensionsHook": undefined,
          "fab": {
            "backgroundColor": "#f2f2f2",
            "color": "#0065FB",
          },
          "logo": {
            "gutter": "2px",
            "maxHeight": "260px",
            "maxWidth": "260px",
          },
          "rightPanel": {
            "backgroundColor": "#263238",
            "servers": {
              "overlay": {
                "backgroundColor": "#fafafa",
                "textColor": "#263238",
              },
              "url": {
                "backgroundColor": "#fff",
              },
            },
            "textColor": "#ffffff",
            "width": "40%",
          },
          "schema": {
            "arrow": {
              "color": "#666",
              "size": "1.1em",
            },
            "defaultDetailsWidth": "75%",
            "labelsTextSize": "0.9em",
            "linesColor": "#7c7cbb",
            "nestedBackground": "#fafafa",
            "nestingSpacing": "1em",
            "requireLabelColor": "#d41f1c",
            "typeNameColor": "#666",
            "typeTitleColor": "#666",
          },
          "sidebar": {
            "activeTextColor": "#32329f",
            "arrow": {
              "color": "#333333",
              "size": "1.5em",
            },
            "backgroundColor": "#fafafa",
            "groupItems": {
              "activeBackgroundColor": "#e1e1e1",
              "activeTextColor": "#32329f",
              "textTransform": "uppercase",
            },
            "level1Items": {
              "activeBackgroundColor": "#ededed",
              "activeTextColor": "#32329f",
              "textTransform": "none",
            },
            "textColor": "#333333",
            "width": "260px",
          },
          "spacing": {
            "sectionHorizontal": 40,
            "sectionVertical": 40,
            "unit": 5,
          },
          "typography": {
            "code": {
              "backgroundColor": "rgba(38, 50, 56, 0.05)",
              "color": "#e53935",
              "fontFamily": "Courier, monospace",
              "fontSize": "13px",
              "fontWeight": "400",
              "lineHeight": "1.5em",
              "wrap": false,
            },
            "fontFamily": "Roboto, sans-serif",
            "fontSize": "14px",
            "fontWeightBold": "600",
            "fontWeightLight": "300",
            "fontWeightRegular": "400",
            "headings": {
              "fontFamily": "Montserrat, sans-serif",
              "fontWeight": "400",
              "lineHeight": "1.6em",
            },
            "lineHeight": "1.5em",
            "links": {
              "color": "#32329f",
              "hover": "#6868cf",
              "hoverTextDecoration": "auto",
              "textDecoration": "auto",
              "visited": "#32329f",
            },
            "optimizeSpeed": true,
            "smoothing": "antialiased",
          },
        },
        "unstable_ignoreMimeParameters": false,
      },
      "pattern": undefined,
      "pointer": "#/components/schemas/Dog",
      "rawSchema": {
        "allOf": [
          {
            "$ref": "#/components/schemas/Pet",
          },
        ],
        "properties": {
          "packSize": {
            "type": "number",
          },
        },
        "type": "object",
      },
      "readOnly": false,
      "refsStack": [
        "#/components/schemas/Dog",
      ],
      "schema": {
        "allOf": undefined,
        "description": undefined,
        "discriminator": {
          "propertyName": "type",
        },
        "properties": {
          "packSize": {
            "type": "number",
          },
          "type": {
            "type": "string",
            "x-refsStack": [
              "#/components/schemas/Dog",
              "#/components/schemas/Pet",
            ],
          },
        },
        "readOnly": undefined,
        "required": [
          "type",
        ],
        "title": "Dog",
        "type": "object",
        "writeOnly": undefined,
        "x-circular-ref": undefined,
        "x-parentRefs": [
          "#/components/schemas/Pet",
        ],
      },
      "title": "Dog",
      "type": "object",
      "typePrefix": "",
      "writeOnly": false,
      "x-enumDescriptions": undefined,
    }
  }
/>
`;

exports[`Components SchemaView discriminator should correctly render discriminator dropdown 1`] = `
<styled.table>
  <tbody>
    <Field
      expandByDefault={false}
      field={
        FieldModel {
          "const": "",
          "deprecated": false,
          "description": "",
          "example": undefined,
          "expanded": undefined,
          "explode": false,
          "in": undefined,
          "kind": "field",
          "name": "packSize",
          "required": false,
          "schema": SchemaModel {
            "activeOneOf": 0,
            "const": "",
            "constraints": [],
            "contentEncoding": undefined,
            "contentMediaType": undefined,
            "default": undefined,
            "deprecated": false,
            "description": "",
            "displayFormat": undefined,
            "displayType": "number",
            "enum": [],
            "example": undefined,
            "examples": undefined,
            "externalDocs": undefined,
            "format": undefined,
            "isCircular": false,
            "isPrimitive": true,
            "maxItems": undefined,
            "minItems": undefined,
            "options": "<<<filtered>>>",
            "pattern": undefined,
            "pointer": "#/components/schemas/Dog/properties/packSize",
            "rawSchema": {
              "default": undefined,
              "type": "number",
            },
            "readOnly": false,
            "refsStack": [
              "#/components/schemas/Dog",
              "#/components/schemas/Dog/properties/packSize",
            ],
            "schema": {
              "default": undefined,
              "type": "number",
            },
            "title": "",
            "type": "number",
            "typePrefix": "",
            "writeOnly": false,
            "x-enumDescriptions": undefined,
          },
        }
      }
      fieldParentsName={[]}
      isLast={false}
      key="packSize"
      showExamples={false}
    />
    <Field
      expandByDefault={false}
      field={
        FieldModel {
          "const": "",
          "deprecated": false,
          "description": "",
          "example": undefined,
          "expanded": undefined,
          "explode": false,
          "in": undefined,
          "kind": "field",
          "name": "type",
          "required": true,
          "schema": SchemaModel {
            "activeOneOf": 0,
            "const": "",
            "constraints": [],
            "contentEncoding": undefined,
            "contentMediaType": undefined,
            "default": undefined,
            "deprecated": false,
            "description": "",
            "displayFormat": undefined,
            "displayType": "string",
            "enum": [],
            "example": undefined,
            "examples": undefined,
            "externalDocs": undefined,
            "format": undefined,
            "isCircular": false,
            "isPrimitive": true,
            "maxItems": undefined,
            "minItems": undefined,
            "options": "<<<filtered>>>",
            "pattern": undefined,
            "pointer": "#/components/schemas/Dog/properties/type",
            "rawSchema": {
              "default": undefined,
              "type": "string",
              "x-refsStack": [
                "#/components/schemas/Dog",
                "#/components/schemas/Pet",
              ],
            },
            "readOnly": false,
            "refsStack": [
              "#/components/schemas/Dog",
              "#/components/schemas/Dog",
              "#/components/schemas/Pet",
              "#/components/schemas/Dog",
              "#/components/schemas/Pet",
              "#/components/schemas/Dog/properties/type",
            ],
            "schema": {
              "default": undefined,
              "type": "string",
              "x-refsStack": [
                "#/components/schemas/Dog",
                "#/components/schemas/Pet",
              ],
            },
            "title": "",
            "type": "string",
            "typePrefix": "",
            "writeOnly": false,
            "x-enumDescriptions": undefined,
          },
        }
      }
      fieldParentsName={[]}
      isLast={true}
      key="type"
      renderDiscriminatorSwitch={[Function]}
      showExamples={false}
    />
  </tbody>
</styled.table>
`;
