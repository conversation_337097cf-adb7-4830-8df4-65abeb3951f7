{"extends": ["tslint:latest", "tslint-react"], "rules": {"array-type": false, "interface-name": false, "object-literal-sort-keys": false, "jsx-no-multiline-js": false, "jsx-wrap-multiline": false, "max-classes-per-file": false, "forin": false, "prefer-conditional-expression": false, "no-var-requires": false, "no-object-literal-type-assertion": false, "no-console": false, "jsx-curly-spacing": false, "max-line-length": false, "quotemark": [true, "single", "avoid-template", "jsx-double"], "variable-name": [true, "ban-keywords", "check-format", "allow-leading-underscore", "allow-pascal-case"], "arrow-parens": [true, "ban-single-arg-parens"], "no-submodule-imports": [true, "prismjs", "perfect-scrollbar", "react-dom", "core-js", "memoize-one"], "object-literal-key-quotes": [true, "as-needed"], "no-unused-expression": [true, "allow-tagged-template"], "semicolon": [true, "always", "ignore-bound-class-methods"], "member-access": [true, "no-public"]}}