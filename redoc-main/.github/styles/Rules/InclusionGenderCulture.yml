extends: substitution
message: 'Use inclusive language. Consider "%s" instead of "%s".'
link: https://intranet.redoc.ly/contributing/documentation-style-guide/#grammar-and-syntax
level: error
ignorecase: true
swap:
  he: they
  his: their
  she: they
  hers: their
  blacklist(?:ed|ing|s)?: blocklist
  whitelist(?:ed|ing|s)?: allowlist
  master: primary, main
  slave: replica
  he/she: they
  s/he: they
