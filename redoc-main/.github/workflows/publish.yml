name: Publish

on:
  push:
    branches: [main]

jobs:
  bundle:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - name: Cache node modules
        uses: actions/cache@v3
        with:
          path: ~/.npm # npm cache files are stored in `~/.npm` on Linux/macOS
          key: npm-${{ hashFiles('package-lock.json') }}
          restore-keys: |
            npm-${{ hashFiles('package-lock.json') }}
            npm-
      - run: npm ci
      - run: npm run bundle
      - name: Store bundle artifact
        uses: actions/upload-artifact@v4
        with:
          name: bundles
          path: bundles
          retention-days: 1
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - run: npm ci
      - run: npm test
  e2e-tests:
    needs: [bundle]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - run: npm ci
      - name: Download bundled artifact
        uses: actions/download-artifact@v4
        with:
          name: bundles
          path: bundles
      - run: npm run e2e
  check-version:
    name: Check Version
    runs-on: ubuntu-latest
    needs: [bundle, unit-tests, e2e-tests]
    outputs:
      changed: ${{ steps.check.outputs.changed }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
      - name: Set up Node.js
        uses: actions/setup-node@v3
      - name: Check if version has been updated
        id: check
        uses: EndBug/version-check@v2.0.1
        with:
          file-url: https://cdn.jsdelivr.net/npm/redoc/package.json
          static-checking: localIsNew
  publish:
    name: Publish to NPM
    needs: [check-version]
    if: needs.check-version.outputs.changed == 'true'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/setup-node@v3
        with:
          node-version: '14.x'
          registry-url: 'https://registry.npmjs.org'
      - uses: actions/checkout@v3
      - name: Download bundled artifacts
        uses: actions/download-artifact@v4
        with:
          name: bundles
          path: bundles
      - name: Cache node modules
        uses: actions/cache@v3
        with:
          path: ~/.npm # npm cache files are stored in `~/.npm` on Linux/macOS
          key: npm-${{ hashFiles('package-lock.json') }}
          restore-keys: |
            npm-${{ hashFiles('package-lock.json') }}
            npm-
      - name: Before deploy
        run: npm ci && npm run declarations
      - name: Publish to NPM
        run: npm publish
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}

  publish-cdn:
    name: Publish to CDN
    needs: [check-version]
    if: needs.check-version.outputs.changed == 'true'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
      - name: Configure AWS
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1
      - name: Download all artifact
        uses: actions/download-artifact@v4
      - name: Publish to S3
        run: npm run publish-cdn

  invalidate-cache:
    name: Clear cache
    runs-on: ubuntu-latest
    needs: [check-version, publish, publish-cdn]
    if: needs.check-version.outputs.changed == 'true'
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
      - name: Configure AWS
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1
      - name: Invalidate cache
        run: ./scripts/invalidate-cache.sh
        shell: bash
        env:
          DISTRIBUTION: ${{ secrets.DISTRIBUTION }}
