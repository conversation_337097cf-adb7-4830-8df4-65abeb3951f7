{"compilerOptions": {"experimentalDecorators": true, "module": "commonjs", "moduleResolution": "node", "target": "es2015", "noImplicitAny": false, "noUnusedLocals": true, "noUnusedParameters": true, "strictNullChecks": true, "sourceMap": true, "pretty": true, "lib": ["es2015", "es2016", "es2017", "dom"], "jsx": "react", "types": ["cypress"]}, "compileOnSave": false, "include": ["integration/*.ts", "../node_modules/cypress"]}