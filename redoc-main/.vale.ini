# Vale configuration file.
# See: https://docs.errata.ai/vale/config

# The relative path to the folder containing linting rules (styles).
StylesPath = .github/styles

# Vocab define the exceptions to use in *all* `BasedOnStyles`.
# spelling-exceptions.txt triggers `Vale.Terms`
# reject.txt triggers `Vale.Avoid`
# See: https://docs.errata.ai/vale/vocab
Vocab = Rules

# Minimum alert level
# -------------------
# The minimum alert level in the output (suggestion, warning, or error).
# If integrated into CI, builds fail by default on error-level alerts, unless you run Vale with the --no-exit flag
MinAlertLevel = suggestion

# IgnoredScopes specifies inline-level HTML tags to ignore.
# These tags may occur in an active scope (unlike SkippedScopes, skipped entirely) but their content still won't raise any alerts.
# Default: ignore `code` and `tt`.
IgnoredScopes = code, tt, img, url, a, body.id
# SkippedScopes specifies block-level HTML tags to ignore. Ignore any content in these scopes.
# Default: ignore `script`, `style`, `pre`, and `figure`.
# For AsciiDoc: by default, listingblock, and literalblock.
SkippedScopes = script, style, pre, figure, code, tt, listingblock, literalblock

# Rules for matching file types. See: https://docs.errata.ai/vale/scoping

[formats]
properties = md
mdx = md

# Rules for .MD, .MDX
[*.{md,mdx}]

BasedOnStyles = Rules
# Ignore code surrounded by backticks or plus sign, parameters defaults, URLs.
TokenIgnores = (\x60[^\n\x60]+\x60), ([^\n]+=[^\n]*), (\+[^\n]+\+), (http[^\n]+\[)
Vale.Repetition = NO
Vale.SentenceSpacing = NO
Vale.Spelling = NO

# /End of rules for .MD, .MDX


# Process .ini files
[*.ini]
