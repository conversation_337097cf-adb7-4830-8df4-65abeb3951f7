{"name": "redoc", "version": "2.5.0", "description": "ReDoc", "repository": {"type": "git", "url": "git://github.com/Redocly/redoc"}, "browserslist": ["defaults"], "engines": {"node": ">=6.9", "npm": ">=3.0.0"}, "author": "<PERSON> Hotsiy <<EMAIL>>", "license": "MIT", "keywords": ["OpenAPI", "OpenAPI Specification", "Swagger", "JSON-Schema", "API", "REST", "documentation", "React.js"], "main": "bundles/redoc.lib.js", "browser": "bundles/redoc.browser.lib.js", "types": "typings/index.d.ts", "scripts": {"start": "webpack serve --mode=development --env playground --hot --config demo/webpack.config.ts", "start:prod": "webpack serve --env playground --mode=production --config demo/webpack.config.ts", "start:benchmark": "webpack serve --mode=production --env.bench --config demo/webpack.config.ts", "test": "npm run unit && npm run license-check", "unit": "jest --coverage", "test:update-snapshot": "jest --updateSnapshot", "e2e": "cypress run", "e2e-ci": "cypress run --record", "bundlesize": "size-limit", "ts-check": "tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>", "cy:open": "cypress open", "bundle:clean": "rimraf bundles", "bundle:standalone": "webpack --env production --env standalone --mode=production", "bundle:lib": "webpack --mode=production && npm run declarations", "bundle:browser": "webpack --env production --env browser --mode=production", "bundle": "npm run bundle:clean && npm run bundle:lib && npm run bundle:browser && npm run bundle:standalone", "declarations": "tsc --emitDeclarationOnly -p tsconfig.lib.json && cp -R src/types typings/", "stats": "webpack --env production --env standalone --json --profile --mode=production > stats.json", "prettier": "prettier --write \"src/**/*.{ts,tsx}\"", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s -r 1", "lint": "eslint --fix 'src/**/*.{js,ts,tsx}' --cache", "benchmark": "node ./benchmark/benchmark.js", "start:demo": "webpack serve --hot --config demo/webpack.config.ts --mode=development", "build:demo": "webpack --mode=production --config demo/webpack.config.ts", "publish-cdn": "scripts/publish-cdn.sh", "deploy:demo": "aws s3 sync demo/dist s3://production-redoc-demo --acl=public-read", "license-check": "license-checker --production --onlyAllow 'MIT;ISC;Apache-2.0;BSD;BSD-2-Clause;BSD-3-Clause;CC-BY-4.0;CC0-1.0;Python-2.0 ' --summary", "docker:build": "docker build -f config/docker/Dockerfile -t redoc .", "prepare": "husky install", "pre-commit": "pretty-quick --staged && npm run lint"}, "devDependencies": {"@cfaester/enzyme-adapter-react-18": "^0.8.0", "@cypress/webpack-preprocessor": "^5.17.1", "@size-limit/file": "^11.1.4", "@types/chai": "^4.2.18", "@types/dompurify": "^2.2.2", "@types/enzyme": "^3.10.5", "@types/enzyme-to-json": "^1.5.3", "@types/jest": "^29.5.6", "@types/json-pointer": "^1.0.30", "@types/lunr": "^2.3.3", "@types/mark.js": "^8.11.5", "@types/marked": "^4.0.3", "@types/node": "^15.6.1", "@types/prismjs": "^1.16.5", "@types/prop-types": "^15.7.3", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/styled-components": "^5.1.1", "@types/tapable": "^2.2.2", "@types/webpack": "^5.28.0", "@types/webpack-env": "^1.18.0", "@types/yargs": "^17.0.0", "@typescript-eslint/eslint-plugin": "^5.55.0", "@typescript-eslint/parser": "^5.55.0", "beautify-benchmark": "^0.2.4", "conventional-changelog-cli": "^3.0.0", "copy-webpack-plugin": "^9.0.0", "core-js": "^3.13.1", "css-loader": "^5.2.6", "cypress": "^13.8.1", "enzyme": "^3.11.0", "enzyme-to-json": "^3.6.2", "esbuild-loader": "^4.3.0", "eslint": "^7.27.0", "eslint-plugin-import": "^2.23.4", "eslint-plugin-react": "^7.34.2", "eslint-plugin-react-hooks": "^4.6.2", "fork-ts-checker-webpack-plugin": "^6.2.10", "html-webpack-plugin": "^5.3.1", "husky": "^7.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "js-yaml": "^4.1.0", "license-checker": "^25.0.1", "lodash.noop": "^3.0.1", "mobx": "^6.10.2", "outdent": "^0.8.0", "prettier": "^2.3.2", "pretty-quick": "^3.0.0", "raf": "^3.4.1", "react": "^18.0.0", "react-dom": "^18.0.0", "rimraf": "^3.0.2", "shelljs": "^0.8.4", "size-limit": "^11.1.4", "style-loader": "^3.3.1", "styled-components": "^5.3.0", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "tslib": "^2.4.0", "typescript": "^4.9.0", "unfetch": "^4.2.0", "url": "^0.11.1", "webpack": "^5.94.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.2.1", "webpack-node-externals": "^3.0.0", "workerize-loader": "github:redocly/workerize-loader#webpack-5-dist"}, "peerDependencies": {"core-js": "^3.1.4", "mobx": "^6.0.4", "react": "^16.8.4 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^16.8.4 || ^17.0.0 || ^18.0.0 || ^19.0.0", "styled-components": "^4.1.1 || ^5.1.1 || ^6.0.5"}, "dependencies": {"@redocly/openapi-core": "^1.4.0", "classnames": "^2.3.2", "decko": "^1.2.0", "dompurify": "^3.2.4", "eventemitter3": "^5.0.1", "json-pointer": "^0.6.2", "lunr": "^2.3.9", "mark.js": "^8.11.1", "marked": "^4.3.0", "mobx-react": "^9.1.1", "openapi-sampler": "^1.5.0", "path-browserify": "^1.0.1", "perfect-scrollbar": "^1.5.5", "polished": "^4.2.2", "prismjs": "^1.29.0", "prop-types": "^15.8.1", "react-tabs": "^6.0.2", "slugify": "~1.4.7", "stickyfill": "^1.1.1", "swagger2openapi": "^7.0.8", "url-template": "^2.0.8"}, "size-limit": [{"path": "./bundles/redoc.standalone.js", "limit": "350 kB"}, {"path": "./bundles/redoc.lib.js", "limit": "100 kB"}, {"path": "./bundles/redoc.browser.lib.js", "limit": "100 kB"}], "jest": {"testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/src/setupTests.ts"], "preset": "ts-jest", "collectCoverageFrom": ["src/**/*.{ts,tsx}"], "coverageReporters": ["json", "lcov", "text-summary"], "coveragePathIgnorePatterns": ["\\.d\\.ts$", "/benchmark/", "/node_modules/", "src/services/__tests__/models/helpers.ts"], "modulePathIgnorePatterns": ["/benchmark/", "src/services/__tests__/models/helpers.ts"], "snapshotSerializers": ["enzyme-to-json/serializer"], "moduleNameMapper": {"\\.(css|less)$": "<rootDir>/src/empty.js"}}, "prettier": {"singleQuote": true, "trailingComma": "all", "printWidth": 100, "arrowParens": "avoid"}}