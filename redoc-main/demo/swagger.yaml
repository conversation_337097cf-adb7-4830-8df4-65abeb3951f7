swagger: '2.0'
schemes:
  - http
  - https
host: petstore.swagger.io
basePath: /v2
info:
  description: |
    This is a sample server Petstore server.
    You can find out more about Swagger at
    [http://swagger.io](http://swagger.io) or on [irc.freenode.net, #swagger](http://swagger.io/irc/).
    For this sample, you can use the api key `special-key` to test the authorization filters.
    # Introduction
    This API is documented in **OpenAPI format** and is based on
    [Petstore sample](http://petstore.swagger.io/) provided by [swagger.io](http://swagger.io) team.
    It was **extended** to illustrate features of [generator-openapi-repo](https://github.com/Rebilly/generator-openapi-repo)
    tool and [ReDoc](https://github.com/Redocly/redoc) documentation. In addition to standard
    OpenAPI syntax we use a few [vendor extensions](https://github.com/Redocly/redoc/blob/main/docs/redoc-vendor-extensions.md).
    # OpenAPI Specification
    This API is documented in **OpenAPI format** and is based on
    [Petstore sample](http://petstore.swagger.io/) provided by [swagger.io](http://swagger.io) team.
    It was **extended** to illustrate features of [generator-openapi-repo](https://github.com/Rebilly/generator-openapi-repo)
    tool and [ReDoc](https://github.com/Redocly/redoc) documentation. In addition to standard
    OpenAPI syntax we use a few [vendor extensions](https://github.com/Redocly/redoc/blob/main/docs/redoc-vendor-extensions.md).
    # Cross-Origin Resource Sharing
    This API features Cross-Origin Resource Sharing (CORS) implemented in compliance with  [W3C spec](https://www.w3.org/TR/cors/).
    And that allows cross-domain communication from the browser.
    All responses have a wildcard same-origin which makes them completely public and accessible to everyone, including any code on any site.
    # Authentication
    Petstore offers two forms of authentication:
      - API Key
      - OAuth2

    OAuth2 - an open protocol to allow secure authorization in a simple
    and standard method from web, mobile and desktop applications.
    <!-- ReDoc-Inject: <security-definitions> -->
  version: 1.0.0
  title: Swagger Petstore
  termsOfService: 'http://swagger.io/terms/'
  contact:
    email: <EMAIL>
    url: https://github.com/Redocly/redoc
  x-logo:
    url: 'https://redocly.github.io/redoc/petstore-logo.png'
    altText: Petstore logo
  license:
    name: Apache 2.0
    url: 'http://www.apache.org/licenses/LICENSE-2.0.html'
externalDocs:
  description: Find out how to create Github repo for your OpenAPI spec.
  url: 'https://github.com/Rebilly/generator-openapi-repo'
tags:
  - name: pet
    description: Everything about your Pets
  - name: store
    description: Access to Petstore orders
  - name: user
    description: Operations about user
x-tagGroups:
  - name: General
    tags:
      - pet
      - store
  - name: User Management
    tags:
      - user
securityDefinitions:
  petstore_auth:
    description: |
      Get access to data while protecting your account credentials.
      OAuth2 is also a safer and more secure way to give you access.
    type: oauth2
    authorizationUrl: 'http://petstore.swagger.io/api/oauth/dialog'
    flow: implicit
    scopes:
      'write:pets': modify pets in your account
      'read:pets': read your pets
  api_key:
    description: |
      For this sample, you can use the api key `special-key` to test the authorization filters.
    type: apiKey
    name: api_key
    in: header
x-servers:
  - url: //petstore.swagger.io/v2
    description: Default server
  - url: //petstore.swagger.io/sandbox
    description: Sandbox server
paths:
  /pet:
    post:
      tags:
        - pet
      summary: Add a new pet to the store
      description: Add new pet to the store inventory.
      operationId: addPet
      consumes:
        - application/json
        - application/xml
      produces:
        - application/xml
        - application/json
      parameters:
        - in: body
          name: body
          description: Pet object that needs to be added to the store
          required: true
          schema:
            $ref: '#/definitions/Pet'
      responses:
        '405':
          description: Invalid input
      security:
        - petstore_auth:
            - 'write:pets'
            - 'read:pets'
      x-code-samples:
        - lang: 'C#'
          source: |
            PetStore.v1.Pet pet = new PetStore.v1.Pet();
            pet.setApiKey("your api key");
            pet.petType = PetStore.v1.Pet.TYPE_DOG;
            pet.name = "Rex";
            // set other fields
            PetStoreResponse response = pet.create();
            if (response.statusCode == HttpStatusCode.Created)
            {
              // Successfully created
            }
            else
            {
              // Something wrong -- check response for errors
              Console.WriteLine(response.getRawResponse());
            }
        - lang: PHP
          source: "$form = new \\PetStore\\Entities\\Pet();\n$form->setPetType(\"Dog\");\n$form->setName(\"Rex\");\n// set other fields\ntry {\n    $pet = $client->pets()->create($form);\n} catch (UnprocessableEntityException $e) {\n    var_dump($e->getErrors());\n}\n"
    put:
      tags:
        - pet
      summary: Update an existing pet
      description: ''
      operationId: updatePet
      consumes:
        - application/json
        - application/xml
      produces:
        - application/xml
        - application/json
      parameters:
        - in: body
          name: body
          description: Pet object that needs to be added to the store
          required: true
          schema:
            $ref: '#/definitions/Pet'
      responses:
        '400':
          description: Invalid ID supplied
        '404':
          description: Pet not found
        '405':
          description: Validation exception
      security:
        - petstore_auth:
            - 'write:pets'
            - 'read:pets'
      x-code-samples:
        - lang: PHP
          source: "$form = new \\PetStore\\Entities\\Pet();\n$form->setPetId(1);\n$form->setPetType(\"Dog\");\n$form->setName(\"Rex\");\n// set other fields\ntry {\n    $pet = $client->pets()->update($form);\n} catch (UnprocessableEntityException $e) {\n    var_dump($e->getErrors());\n}\n"
  '/pet/{petId}':
    get:
      tags:
        - pet
      summary: Find pet by ID
      description: Returns a single pet
      operationId: getPetById
      produces:
        - application/xml
        - application/json
      parameters:
        - name: petId
          in: path
          description: ID of pet to return
          required: true
          type: integer
          format: int64
      responses:
        '200':
          description: successful operation
          schema:
            $ref: '#/definitions/Pet'
        '400':
          description: Invalid ID supplied
        '404':
          description: Pet not found
      security:
        - api_key: []
    post:
      tags:
        - pet
      summary: Updates a pet in the store with form data
      description: ''
      operationId: updatePetWithForm
      consumes:
        - application/x-www-form-urlencoded
      produces:
        - application/xml
        - application/json
      parameters:
        - name: petId
          in: path
          description: ID of pet that needs to be updated
          required: true
          type: integer
          format: int64
        - name: name
          in: formData
          description: Updated name of the pet
          required: false
          type: string
        - name: status
          in: formData
          description: Updated status of the pet
          required: false
          type: string
      responses:
        '405':
          description: Invalid input
      security:
        - petstore_auth:
            - 'write:pets'
            - 'read:pets'
    delete:
      tags:
        - pet
      summary: Deletes a pet
      description: ''
      operationId: deletePet
      produces:
        - application/xml
        - application/json
      parameters:
        - name: api_key
          in: header
          required: false
          type: string
          x-example: Bearer <TOKEN>
        - name: petId
          in: path
          description: Pet id to delete
          required: true
          type: integer
          format: int64
      responses:
        '400':
          description: Invalid pet value
      security:
        - petstore_auth:
            - 'write:pets'
            - 'read:pets'
  '/pet/{petId}/uploadImage':
    post:
      tags:
        - pet
      summary: uploads an image
      description: ''
      operationId: uploadFile
      consumes:
        - multipart/form-data
      produces:
        - application/json
      parameters:
        - name: petId
          in: path
          description: ID of pet to update
          required: true
          type: integer
          format: int64
        - name: additionalMetadata
          in: formData
          description: Additional data to pass to server
          required: false
          type: string
        - name: file
          in: formData
          description: file to upload
          required: false
          type: file
      responses:
        '200':
          description: successful operation
          schema:
            $ref: '#/definitions/ApiResponse'
      security:
        - petstore_auth:
            - 'write:pets'
            - 'read:pets'
  /pet/findByStatus:
    get:
      tags:
        - pet
      summary: Finds Pets by status
      description: Multiple status values can be provided with comma separated strings
      operationId: findPetsByStatus
      produces:
        - application/xml
        - application/json
      parameters:
        - name: status
          in: query
          description: Status values that need to be considered for filter
          required: true
          type: array
          items:
            type: string
            enum:
              - available
              - pending
              - sold
            default: available
          collectionFormat: csv
      responses:
        '200':
          description: successful operation
          schema:
            type: array
            items:
              $ref: '#/definitions/Pet'
        '400':
          description: Invalid status value
      security:
        - petstore_auth:
            - 'write:pets'
            - 'read:pets'
  /pet/findByTags:
    get:
      tags:
        - pet
      summary: Finds Pets by tags
      description: 'Multiple tags can be provided with comma separated strings. Use tag1, tag2, tag3 for testing.'
      operationId: findPetsByTags
      deprecated: true
      produces:
        - application/xml
        - application/json
      parameters:
        - name: tags
          in: query
          description: Tags to filter by
          required: true
          type: array
          items:
            type: string
          collectionFormat: csv
      responses:
        '200':
          description: successful operation
          schema:
            type: array
            items:
              $ref: '#/definitions/Pet'
        '400':
          description: Invalid tag value
      security:
        - petstore_auth:
            - 'write:pets'
            - 'read:pets'
  /store/inventory:
    get:
      tags:
        - store
      summary: Returns pet inventories by status
      description: Returns a map of status codes to quantities
      operationId: getInventory
      produces:
        - application/json
      parameters: []
      responses:
        '200':
          description: successful operation
          schema:
            type: object
            additionalProperties:
              type: integer
              format: int32
      security:
        - api_key: []
  /store/order:
    post:
      tags:
        - store
      summary: Place an order for a pet
      description: ''
      operationId: placeOrder
      produces:
        - application/xml
        - application/json
      parameters:
        - in: body
          name: body
          description: order placed for purchasing the pet
          required: true
          schema:
            $ref: '#/definitions/Order'
      responses:
        '200':
          description: successful operation
          schema:
            $ref: '#/definitions/Order'
        '400':
          description: Invalid Order
  '/store/order/{orderId}':
    get:
      tags:
        - store
      summary: Find purchase order by ID
      description: 'For valid response try integer IDs with value <= 5 or > 10. Other values will generated exceptions'
      operationId: getOrderById
      produces:
        - application/xml
        - application/json
      parameters:
        - name: orderId
          in: path
          description: ID of pet that needs to be fetched
          required: true
          type: integer
          maximum: 5
          minimum: 1
          format: int64
      responses:
        '200':
          description: successful operation
          schema:
            $ref: '#/definitions/Order'
        '400':
          description: Invalid ID supplied
        '404':
          description: Order not found
    delete:
      tags:
        - store
      summary: Delete purchase order by ID
      description: For valid response try integer IDs with value < 1000. Anything above 1000 or nonintegers will generate API errors
      operationId: deleteOrder
      produces:
        - application/xml
        - application/json
      parameters:
        - name: orderId
          in: path
          description: ID of the order that needs to be deleted
          required: true
          type: string
          minimum: 1
      responses:
        '400':
          description: Invalid ID supplied
        '404':
          description: Order not found
  /user:
    post:
      tags:
        - user
      summary: Create user
      description: This can only be done by the logged in user.
      operationId: createUser
      produces:
        - application/xml
        - application/json
      parameters:
        - in: body
          name: body
          description: Created user object
          required: true
          schema:
            $ref: '#/definitions/User'
      responses:
        default:
          description: successful operation
  '/user/{username}':
    get:
      tags:
        - user
      summary: Get user by user name
      description: ''
      operationId: getUserByName
      produces:
        - application/xml
        - application/json
      parameters:
        - name: username
          in: path
          description: 'The name that needs to be fetched. Use user1 for testing. '
          required: true
          type: string
      responses:
        '200':
          description: successful operation
          schema:
            $ref: '#/definitions/User'
        '400':
          description: Invalid username supplied
        '404':
          description: User not found
    put:
      tags:
        - user
      summary: Updated user
      description: This can only be done by the logged in user.
      operationId: updateUser
      produces:
        - application/xml
        - application/json
      parameters:
        - name: username
          in: path
          description: name that need to be deleted
          required: true
          type: string
        - in: body
          name: body
          description: Updated user object
          required: true
          schema:
            $ref: '#/definitions/User'
      responses:
        '400':
          description: Invalid user supplied
        '404':
          description: User not found
    delete:
      tags:
        - user
      summary: Delete user
      description: This can only be done by the logged in user.
      operationId: deleteUser
      produces:
        - application/xml
        - application/json
      parameters:
        - name: username
          in: path
          description: The name that needs to be deleted
          required: true
          type: string
      responses:
        '400':
          description: Invalid username supplied
        '404':
          description: User not found
  /user/createWithArray:
    post:
      tags:
        - user
      summary: Creates list of users with given input array
      description: ''
      operationId: createUsersWithArrayInput
      produces:
        - application/xml
        - application/json
      parameters:
        - in: body
          name: body
          description: List of user object
          required: true
          schema:
            type: array
            items:
              $ref: '#/definitions/User'
      responses:
        default:
          description: successful operation
  /user/createWithList:
    post:
      tags:
        - user
      summary: Creates list of users with given input array
      description: ''
      operationId: createUsersWithListInput
      produces:
        - application/xml
        - application/json
      parameters:
        - in: body
          name: body
          description: List of user object
          required: true
          schema:
            type: array
            items:
              $ref: '#/definitions/User'
      responses:
        default:
          description: successful operation
  /user/login:
    get:
      tags:
        - user
      summary: Logs user into the system
      description: ''
      operationId: loginUser
      produces:
        - application/xml
        - application/json
      parameters:
        - name: username
          in: query
          description: The user name for login
          required: true
          type: string
        - name: password
          in: query
          description: The password for login in clear text
          required: true
          type: string
      responses:
        '200':
          description: successful operation
          schema:
            type: string
          examples:
            application/json: OK
            application/xml: <message> OK </message>
            text/plain: OK
          headers:
            X-Rate-Limit:
              type: integer
              format: int32
              description: calls per hour allowed by the user
            X-Expires-After:
              type: string
              format: date-time
              description: date in UTC when token expires
        '400':
          description: Invalid username/password supplied
  /user/logout:
    get:
      tags:
        - user
      summary: Logs out current logged in user session
      description: ''
      operationId: logoutUser
      produces:
        - application/xml
        - application/json
      parameters: []
      responses:
        default:
          description: successful operation
definitions:
  ApiResponse:
    type: object
    properties:
      code:
        type: integer
        format: int32
      type:
        type: string
      message:
        type: string
  Cat:
    description: A representation of a cat
    allOf:
      - $ref: '#/definitions/Pet'
      - type: object
        properties:
          huntingSkill:
            type: string
            description: The measured skill for hunting
            default: lazy
            enum:
              - clueless
              - lazy
              - adventurous
              - aggressive
        required:
          - huntingSkill
  Category:
    type: object
    properties:
      id:
        description: Category ID
        allOf:
          - $ref: '#/definitions/Id'
      name:
        description: Category name
        type: string
        minLength: 1
      sub:
        description: Test Sub Category
        type: object
        properties:
          prop1:
            type: string
            description: Dumb Property
    xml:
      name: Category
  Dog:
    description: A representation of a dog
    allOf:
      - $ref: '#/definitions/Pet'
      - type: object
        properties:
          packSize:
            type: integer
            format: int32
            description: The size of the pack the dog is from
            default: 1
            minimum: 1
        required:
          - packSize
  HoneyBee:
    description: A representation of a honey bee
    allOf:
      - $ref: '#/definitions/Pet'
      - type: object
        properties:
          honeyPerDay:
            type: number
            description: Average amount of honey produced per day in ounces
            example: 3.14
        required:
          - honeyPerDay
  Id:
    type: integer
    format: int64
  Order:
    type: object
    properties:
      id:
        description: Order ID
        allOf:
          - $ref: '#/definitions/Id'
      petId:
        description: Pet ID
        allOf:
          - $ref: '#/definitions/Id'
      quantity:
        type: integer
        format: int32
        minimum: 1
        default: 1
      shipDate:
        description: Estimated ship date
        type: string
        format: date-time
      status:
        type: string
        description: Order Status
        enum:
          - placed
          - approved
          - delivered
      complete:
        description: Indicates whenever order was completed or not
        type: boolean
        default: false
    xml:
      name: Order
  Pet:
    type: object
    required:
      - name
      - photoUrls
    discriminator: petType
    properties:
      id:
        description: Pet ID
        allOf:
          - $ref: '#/definitions/Id'
      category:
        description: Categories this pet belongs to
        allOf:
          - $ref: '#/definitions/Category'
      name:
        description: The name given to a pet
        type: string
        example: Guru
      photoUrls:
        description: The list of URL to a cute photos featuring pet
        type: array
        default: []
        xml:
          name: photoUrl
          wrapped: true
        items:
          type: string
          format: url
      tags:
        description: Tags attached to the pet
        type: array
        xml:
          name: tag
          wrapped: true
        items:
          $ref: '#/definitions/Tag'
      status:
        type: string
        description: Pet status in the store
        enum:
          - available
          - pending
          - sold
      petType:
        description: Type of a pet
        type: string
    xml:
      name: Pet
  Tag:
    type: object
    properties:
      id:
        description: Tag ID
        allOf:
          - $ref: '#/definitions/Id'
      name:
        description: Tag name
        type: string
        minLength: 1
    xml:
      name: Tag
  User:
    type: object
    properties:
      id:
        description: User ID
        $ref: '#/definitions/Id'
      username:
        description: User supplied username
        type: string
        minLength: 4
        example: John78
      firstName:
        description: User first name
        type: string
        minLength: 1
        example: John
      lastName:
        description: User last name
        type: string
        minLength: 1
        example: Smith
      email:
        description: User email address
        type: string
        format: email
        example: <EMAIL>
      password:
        type: string
        description: 'User password, MUST contain a mix of upper and lower case letters, as well as digits'
        format: password
        minLength: 8
        pattern: '(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])'
        example: drowssaP123
      phone:
        description: User phone number in international format
        type: string
        pattern: "^\\+(?:[0-9]-?){6,14}[0-9]$"
        example: ******-555-0192
        x-nullable: true
      userStatus:
        description: User status
        type: integer
        format: int32
    xml:
      name: User
