$("select[name='action']").change(function() {
    let selectedText = $(this).find(":selected").val();
    console.log(selectedText);
    let label = $("label").has("select[name='file_format']");

    if (selectedText == 'export_admin_action') {
        $('#changelist-form select[name="file_format"]').parent().addClass('ml-2').show();
    } else {
        $('#changelist-form select[name="file_format"]').parent().removeClass('ml-2').hide();
    }
});
