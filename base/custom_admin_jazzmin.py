JAZZMIN_SETTINGS_CONFIGURATION = {
    "site_title": "Zuumm Admin",
    "site_header": "Zuumm Admin",
    "site_brand": None,

    # Add the custom logo - using PNG for better compatibility
    "site_logo": "images/admin_logo_new.png",
    "site_logo_classes": "img-fluid",
    # "site_brand": None,
    # "site_header": "",
    "login_logo": "images/admin_logo_new.png",
    "login_logo_dark": None,
    
    "welcome_sign": "Welcome to Zuumm Admin",
    "copyright": "Zuumm Admin",
    
    # Logo styling
    "site_icon": None,  # Remove site icon to avoid conflicts
    
    # Navigation and UI settings
    "topmenu_links": [
        {"name": "Home", "url": "admin:index", "permissions": ["auth.view_user"]},
        {"model": "accounts.User"},
    ],
    "order_with_respect_to": [
        "packages.destination",
        "packages.category",
        "packages.activity", 
        "packages.package",
        "dynamic_packages.hotel",
        "dynamic_packages.room",
        "dynamic_packages.hotelcontact",
        "dynamic_packages.facility",
        "dynamic_packages.hoteladdress",
        "dynamic_packages.city",
        "dynamic_packages.state",
        "dynamic_packages.country",
    ],

    "show_sidebar": True,
    "navigation_expanded": True,
    "icons": {
        "auth": "fas fa-users-cog",
        "accounts.user": "fas fa-user",
        "accounts.superadminuser": "fas fa-user-shield",
        "accounts.partneradminuser": "fas fa-user-tie",
        "accounts.partner": "fas fa-building",
        "modules.frequentlyaskedquestion": "fas fa-question-circle",
        "modules.termandcondition": "fas fa-file-contract",
        # Package-related icons
        "packages": "fas fa-boxes",
        "packages.package": "fas fa-box",
        "packages.category": "fas fa-tags",
        "packages.destination": "fas fa-map-marker-alt",
        "packages.activity": "fas fa-running",
        "packages.packageuploader": "fas fa-cloud-upload-alt",
        # Booking-related icons
        "bookings": "fas fa-receipt",
        "bookings.voucher": "fas fa-receipt",
        "bookings.daywiseitinerary": "fas fa-calendar-day",
        # TripJack Hotel-related icons
        "dynamic_packages": "fas fa-hotel",
        "dynamic_packages.hotel": "fas fa-hotel",
        "dynamic_packages.room": "fas fa-bed",
        "dynamic_packages.hotelcontact": "fas fa-phone",
        "dynamic_packages.facility": "fas fa-concierge-bell",
        "dynamic_packages.hoteladdress": "fas fa-map-marked-alt",
        "dynamic_packages.city": "fas fa-city",
        "dynamic_packages.state": "fas fa-map",
        "dynamic_packages.country": "fas fa-flag",
    },
    "default_icon_parents": "fas fa-chevron-circle-right",
    "default_icon_children": "fas fa-circle",
    
    # UI Builder and customization
    "show_ui_builder": False,
    "changeform_format": "horizontal_tabs",
    "related_modal_active": False,
    
    # Custom CSS and JS
    "custom_css": "jazzmin_css_v1.css",
    "custom_js": "import_export/custom_action_formats.js",

    # Hide unwanted models
    "hide_models": [
        "auth.Group", 
        "token_blacklist.BlacklistedToken",
        "token_blacklist.OutstandingToken"
    ]
}