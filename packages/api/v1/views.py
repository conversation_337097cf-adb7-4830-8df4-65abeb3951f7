from rest_framework.views import APIView
from rest_framework.generics import ListAPIView, RetrieveAPIView
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
from django.db.models import Min, Q, Case, When, <PERSON>oleanField
from django_filters.rest_framework import DjangoFilterBackend
from django.shortcuts import get_object_or_404
from django.db import transaction
from accounts.models import Tag
from packages.models import Category, Destination, Activity, Package, CustomPackage
from packages.api.v1.serializers import (
    CategoryLandingPageSerializer,
    DestinationLandingPageSerializer,
    CategoryExplorePageSerializer,
    PopularActivityExplorePageSerializer,
    DestinationDashboardSerializer,
    VisaOnArrivalDestinationDashboardSerializer,
    PopularDestinationDashboardSerializer,
    PackageDashboardSerializer,
    DestinationActivityDashboardSerializer,
    PackageBasicDetailsSerializer,
    PackageItenaryDetailsSerializer,
    PackageDestinationFaqSerializer,
    ActivityMediaSerializer,
    CustomPackageCreateSerializer,
)
from packages.filters import (
    CategoryFilter,
    DashboardDestinationFilter,
    PopularDestinationDashboardFilter,
    DashboardPackageFilter,
)
from packages.utils.package_pdf_helper import PackagePDFHelper
from packages.utils.custom_package_helper import CustomPackageHelper
from packages.choices import PackageTypeChoices
import logging

logger = logging.getLogger(__name__)


class PackageView(APIView):
    """
    API view for getting packages
    """
    pass


class CategoryLandingPageView(ListAPIView):
    """
    API view for getting categories with media based on partner domain
    
    URL: /api/packages/v1/landing-page/categories/
    """
    serializer_class = CategoryLandingPageSerializer
    permission_classes = [AllowAny]

    def get_queryset(self):
        """
        Get categories for the partner resolved by middleware
        Returns empty queryset if no partner domain resolved
        """
        # Get partner from request.domain (set by PartnerMiddleware)
        partner = getattr(self.request, 'domain', None)
        
        if not partner:
            # Return empty queryset if no domain resolved (don't expose internal info)
            return Category.objects.none()
        
        # Return categories for the resolved partner, ordered by title
        return Category.objects.filter(
            partner=partner, 
            is_active=True
        ).prefetch_related('media').order_by('title')

    def list(self, request, *args, **kwargs):
        """
        Override list method to provide consistent API response format
        """
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        
        return Response({
            'status': 'success',
            'message': 'Categories retrieved successfully',
            'data': {
                'categories': serializer.data,
                'count': len(serializer.data)
            }
        }, status=status.HTTP_200_OK)


class DestinationLandingPageView(ListAPIView):
    """
    API view for getting destinations with media and starting price based on partner domain
    
    URL: /api/packages/v1/landing-page/destinations/
    """
    serializer_class = DestinationLandingPageSerializer
    permission_classes = [AllowAny]

    def get_queryset(self):
        """
        Get destinations for the partner resolved by middleware with annotated starting price
        Returns empty queryset if no partner domain resolved
        """
        # Get partner from request.domain (set by PartnerMiddleware)
        partner = getattr(self.request, 'domain', None)
        
        if not partner:
            # Return empty queryset if no domain resolved (don't expose internal info)
            return Destination.objects.none()
        
        # Base queryset for the resolved partner with annotated starting price
        queryset = Destination.objects.filter(
            partner=partner, 
            is_active=True
        ).prefetch_related('media').annotate(
            # Annotate with minimum price from published and active packages
            starting_price=Min(
                'packages__price',
                filter=Q(
                    packages__is_published=True,
                    packages__is_active=True
                )
            )
        )
        
        return queryset.order_by('title')

    def list(self, request, *args, **kwargs):
        """
        Override list method to provide consistent API response format
        """
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)

        return Response({
            'status': 'success',
            'message': 'Destinations retrieved successfully',
            'data': {
                'destinations': serializer.data,
                'count': len(serializer.data),
            }
        }, status=status.HTTP_200_OK)


class CategoryExplorePageView(ListAPIView):
    """
    NEW API view for getting categories in explore page with media based on partner domain
    Supports search functionality for title and description
    
    URL: /api/packages/v1/explore-page/categories/
    Query Parameters:
        - search: Search in title and description (optional)
    
    Examples:
        GET /api/packages/v1/explore-page/categories/
        GET /api/packages/v1/explore-page/categories/?search=honeymoon
        GET /api/packages/v1/explore-page/categories/?search=family
    """
    serializer_class = CategoryExplorePageSerializer
    permission_classes = [AllowAny]
    filter_backends = [DjangoFilterBackend]
    filterset_class = CategoryFilter

    def get_queryset(self):
        """
        Get categories for the partner resolved by middleware
        Returns empty queryset if no partner domain resolved
        """
        # Get partner from request.domain (set by PartnerMiddleware)
        partner = getattr(self.request, 'domain', None)
        
        if not partner:
            # Return empty queryset if no domain resolved (don't expose internal info)
            return Category.objects.none()
        
        # Base queryset for the resolved partner
        return Category.objects.filter(
            partner=partner, 
            is_active=True
        ).prefetch_related('media').order_by('explore_order', 'title')

    def list(self, request, *args, **kwargs):
        """
        Override list method to provide consistent API response format with search info
        """
        queryset = self.filter_queryset(self.get_queryset())
        serializer = self.get_serializer(queryset, many=True)
        
        response_data = {
            'status': 'success',
            'message': 'Categories retrieved successfully',
            'data': {
                'categories': serializer.data,
                'count': len(serializer.data)
            }
        }
        return Response(response_data, status=status.HTTP_200_OK)


class PopularActivityExplorePageView(ListAPIView):
    """
    API view for getting popular activities with media based on partner domain
    
    URL: /api/packages/v1/explore-page/popular-activities/
    """
    serializer_class = PopularActivityExplorePageSerializer
    permission_classes = [AllowAny]

    def get_queryset(self):
        """
        Get popular activities for the partner resolved by middleware
        """
        partner = getattr(self.request, 'domain', None)
        
        if not partner:
            # Return empty queryset if no domain resolved (don't expose internal info)
            return Activity.objects.none()
        
        # Base queryset for the resolved partner
        return Activity.objects.filter(
            partner=partner,
            is_active=True,
            is_featured=True
        ).prefetch_related('media').order_by('explore_order', 'title')[:5]
    
    def list(self, request, *args, **kwargs):
        """
        Override list method to provide consistent API response format
        """
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)

        return Response({
            'status': 'success',
            'message': 'Popular activities retrieved successfully',
            'data': {
                'activities': serializer.data,
                'count': len(serializer.data)
            }
        }, status=status.HTTP_200_OK)


class TrendingDestinationExplorePageView(ListAPIView):
    """
    API view for getting trending destinations with media based on partner domain
    
    URL: /api/packages/v1/explore-page/trending-destinations/
    """
    serializer_class = DestinationLandingPageSerializer
    permission_classes = [AllowAny]

    def get_queryset(self):
        """
        Get trending destinations for the partner resolved by middleware
        """
        partner = getattr(self.request, 'domain', None)
        
        if not partner:
            # Return empty queryset if no domain resolved (don't expose internal info)
            return Destination.objects.none()
        
        # Base queryset for the resolved partner
        return Destination.objects.filter(
            partner=partner,
            is_active=True,
            is_trending=True
        ).prefetch_related('media').order_by('explore_order', 'title')[:8]
    
    def list(self, request, *args, **kwargs):
        """
        Override list method to provide consistent API response format
        """
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)

        return Response({
            'status': 'success',
            'message': 'Trending destinations retrieved successfully',
            'data': {
                'destinations': serializer.data,
                'count': len(serializer.data)
            }
        }, status=status.HTTP_200_OK)


class DashboardDropdownView(APIView):
    """
    API view for getting dropdown options for destinations dashboard
    
    URL: /api/packages/v1/dashboard/dropdowns/
    """
    permission_classes = [AllowAny]

    def get(self, request, *args, **kwargs):
        """
        Get dropdown options for destinations dashboard
        """
        dropdown = request.query_params.get('dropdown')
        if not dropdown:
            return Response({
                'status': 'error',
                'message': 'Dropdown not found'
            }, status=status.HTTP_400_BAD_REQUEST)

        partner = getattr(request, 'domain', None)
        if not partner:
            return Response({
                'status': 'error',
                'message': 'Partner not found'
            }, status=status.HTTP_404_NOT_FOUND)
        
        if dropdown == 'categories':
            dataset = Category.objects.filter(
                partner=partner,
                is_active=True
            ).values('external_id', 'title')
        elif dropdown == 'activities':
            dataset = Activity.objects.filter(
                partner=partner,
                is_active=True
            ).values('external_id', 'title')
        elif dropdown == 'destinations':
            dataset = Destination.objects.filter(
                partner=partner,
                is_active=True
            ).values('external_id', 'title')
        elif dropdown == 'tags':
            dataset = Tag.objects.filter(
                is_active=True
            ).values('external_id', 'name')
        else:
            return Response({
                'status': 'error',
                'message': 'Invalid dropdown'
            }, status=status.HTTP_400_BAD_REQUEST)

        return Response({
            'status': 'success',
            'message': 'Dropdown options retrieved successfully',
            'data': {
                'options': list(dataset)
            }
        }, status=status.HTTP_200_OK)


class DestinationDashboardView(ListAPIView):
    """
    Comprehensive API view for destinations dashboard with filtering and sorting
    """
    serializer_class = DestinationDashboardSerializer
    permission_classes = [AllowAny]
    filter_backends = [DjangoFilterBackend]
    filterset_class = DashboardDestinationFilter

    def get_queryset(self):
        """
        Get destinations for the partner resolved by middleware with annotated starting price
        Uses reverse relation with annotate for starting price calculation
        """
        # Get partner from request.domain (set by PartnerMiddleware)
        partner = getattr(self.request, 'domain', None)
        
        if not partner:
            # Return empty queryset if no domain resolved (don't expose internal info)
            return Destination.objects.none()
        
        # Base queryset for the resolved partner with annotated starting price using reverse relations
        queryset = Destination.objects.filter(
            partner=partner, 
            is_active=True
        ).prefetch_related('media', 'packages').annotate(
            # Annotate with minimum price from published and active packages using reverse relation
            starting_price=Min(
                'packages__price',
                filter=Q(
                    packages__is_published=True,
                    packages__is_active=True
                )
            )
        )

        # Apply default ordering if no ordering is specified
        ordering = self.request.query_params.get('ordering')
        if not ordering:
            queryset = queryset.order_by('explore_order', 'title')

        return queryset

    def list(self, request, *args, **kwargs):
        """
        Override list method to provide consistent API response format with filtering info
        """
        queryset = self.filter_queryset(self.get_queryset())
        serializer = self.get_serializer(queryset, many=True)
        
        response_data = {
            'status': 'success',
            'message': 'Destinations retrieved successfully',
            'data': {
                'destinations': serializer.data,
                'count': len(serializer.data),
            }
        }
        
        return Response(response_data, status=status.HTTP_200_OK)


class PopularDestinationDashboardView(ListAPIView):
    """
    API view for getting popular destinations with media based on partner domain
    
    URL: /api/packages/v1/dashboard/popular-destinations/
    """
    serializer_class = PopularDestinationDashboardSerializer
    permission_classes = [AllowAny]
    filter_backends = [DjangoFilterBackend]
    filterset_class = PopularDestinationDashboardFilter

    def get_queryset(self):
        """
        Get popular destinations for the partner resolved by middleware with proper partner filtering
        """
        # Get partner from request.domain (set by PartnerMiddleware)
        partner = getattr(self.request, 'domain', None)
        
        if not partner:
            # Return empty queryset if no domain resolved (don't expose internal info)
            return Destination.objects.none()

        # Base queryset for the resolved partner
        return Destination.objects.filter(
            partner=partner,
            is_active=True
        ).prefetch_related('media')
    
    def list(self, request, *args, **kwargs):
        """
        Override list method to provide consistent API response format with context filtering
        """
        context = request.query_params.get('context')
        if not context:
            return Response({
                'status': 'error',
                'message': 'Context parameter is required. Valid values: international, domestic'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Get base queryset and apply context filtering
        queryset = self.get_queryset()
        
        if context == 'international':
            queryset = queryset.filter(is_international=True)
        elif context == 'domestic':
            queryset = queryset.filter(is_international=False)
        else:
            return Response({
                'status': 'error',
                'message': 'Invalid context. Valid values: international, domestic'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Apply filters (including categories filter)
        queryset = self.filter_queryset(queryset)
        serializer = self.get_serializer(queryset, many=True)
        
        return Response({
            'status': 'success',
            'message': 'Popular destinations retrieved successfully',
            'data': {
                'destinations': serializer.data,
                'count': len(serializer.data)
            }
        }, status=status.HTTP_200_OK)


class PackageDashboardView(ListAPIView):
    """
    Comprehensive API view for packages dashboard with filtering and sorting
    """
    serializer_class = PackageDashboardSerializer
    permission_classes = [AllowAny]
    filter_backends = [DjangoFilterBackend]
    filterset_class = DashboardPackageFilter

    def get_queryset(self):
        """
        Get packages for the partner resolved by middleware with annotated starting price
        Uses reverse relation with annotate for starting price calculation
        """
        # Get partner from request.domain (set by PartnerMiddleware)
        partner = getattr(self.request, 'domain', None)
        
        if not partner:
            # Return empty queryset if no domain resolved (don't expose internal info)
            return Package.objects.none()
        
        # Base queryset for the resolved partner with annotated starting price using reverse relations
        queryset = Package.objects.filter(
            partner=partner, 
            is_active=True,
            is_published=True
        )
        # Apply default ordering if no ordering is specified
        ordering = self.request.query_params.get('ordering')
        if not ordering:
            queryset = queryset.order_by('explore_order', 'title')

        return queryset

    def list(self, request, *args, **kwargs):
        """
        Override list method to provide consistent API response format with filtering info
        """
        queryset = self.filter_queryset(self.get_queryset())
        serializer = self.get_serializer(queryset, many=True)
        
        response_data = {
            'status': 'success',
            'message': 'Packages retrieved successfully',
            'data': {
                'count': len(serializer.data),
                'packages': serializer.data,
            }
        }
        
        return Response(response_data, status=status.HTTP_200_OK)


class DestinationActivityDashboardView(ListAPIView):
    """
    API view for getting activities for a specific destination
    """
    serializer_class = DestinationActivityDashboardSerializer
    permission_classes = [AllowAny]

    def get_queryset(self):
        """
        Get activities for a specific destination
        """
        partner = getattr(self.request, 'domain', None)
        destination_external_id = self.request.query_params.get('destination_id')
        if not partner or not destination_external_id:
            return Activity.objects.none()

        try:
            destination = Destination.objects.get(external_id=destination_external_id, partner=partner, is_active=True)
        except Destination.DoesNotExist:
            return Activity.objects.none()

        return Activity.objects.filter(
            destinations__destination=destination,
            partner=partner,
            is_active=True
        ).prefetch_related('media').order_by('explore_order', 'title').distinct()

    def list(self, request, *args, **kwargs):
        """
        Override list method to provide consistent API response format with filtering info
        """
        destination_external_id = request.query_params.get('destination_id')
        if not destination_external_id:
            return Response({
                'status': 'error',
                'message': 'Destination ID is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        queryset = self.filter_queryset(self.get_queryset())
        serializer = self.get_serializer(queryset, many=True)
        response_data = {
            'status': 'success',
            'message': 'Activities retrieved successfully',
            'data': {
                'count': len(serializer.data),
                'activities': serializer.data,
            }
        }
        return Response(response_data, status=status.HTTP_200_OK)


class VisaOnArrivalDestinationDashboardView(ListAPIView):
    """
    API view for getting visa on arrival destinations dashboard
    """
    serializer_class = VisaOnArrivalDestinationDashboardSerializer
    permission_classes = [AllowAny]

    def get_queryset(self):
        """
        Get visa on arrival destinations for the partner resolved by middleware
        Highly optimized version using Django's PostgreSQL array field operations
        """
        # Get partner from request.domain (set by PartnerMiddleware)
        partner = getattr(self.request, 'domain', None)
        category_external_id = self.request.query_params.get('category_external_id')
        
        if not partner or not category_external_id:
            # Return empty queryset if no domain resolved (don't expose internal info)
            return Destination.objects.none()

        try:
            category = Category.objects.get(external_id=category_external_id, partner=partner, is_active=True)
        except Category.DoesNotExist:
            return Destination.objects.none()


        # Build the complete condition that ensures we only check visa_type 
        # on packages that belong to the same partner and are published/active
        visa_on_arrival_condition = Q()
        
        # Create multiple Q conditions for different variations of "visa on arrival"
        # Each condition includes partner, published, and active checks
        visa_variations = [
            'visa on arrival',
            'Visa on Arrival', 
            'VISA ON ARRIVAL',
            'Visa On Arrival'
        ]
        
        for variation in visa_variations:
            visa_on_arrival_condition |= Q(
                packages__partner=partner,
                packages__is_published=True,
                packages__is_active=True,
                packages__visa_type__icontains=variation
            )

        return Destination.objects.filter(
            partner=partner,
            is_active=True,
            categories__category=category
        ).filter(
            visa_on_arrival_condition
        ).annotate(
            has_visa_on_arrival=Case(
                When(pk__isnull=False, then=True),
                default=False,
                output_field=BooleanField()
            )
        ).prefetch_related('media').distinct()

    def list(self, request, *args, **kwargs):
        """
        Override list method to provide consistent API response format with filtering info
        """
        category_external_id = request.query_params.get('category_external_id')
        if not category_external_id:
            return Response({
                'status': 'error',
                'message': 'Category ID is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        queryset = self.filter_queryset(self.get_queryset())
        serializer = self.get_serializer(queryset, many=True)
        response_data = {
            'status': 'success',
            'message': 'Visa on arrival destinations retrieved successfully',
            'data': {
                'count': len(serializer.data),
                'destinations': serializer.data,
            }
        }
        return Response(response_data, status=status.HTTP_200_OK)


class PackageBasicDetailsView(RetrieveAPIView):
    """
    API view for getting package basic details including gallery, highlights, inclusions/exclusions
    
    URL: /api/packages/v1/package/<external_id>/basic-details/
    """
    serializer_class = PackageBasicDetailsSerializer
    permission_classes = [AllowAny]
    lookup_field = 'external_id'

    def get_queryset(self):
        """
        Get packages for the partner resolved by middleware
        """
        # Get partner from request.domain (set by PartnerMiddleware)
        partner = getattr(self.request, 'domain', None)
        
        if not partner:
            # Return empty queryset if no domain resolved
            return Package.objects.none()
        
        # Return packages for the resolved partner with all necessary relations
        return Package.objects.filter(
            partner=partner,
            is_published=True,
            is_active=True
        ).select_related('destination').prefetch_related(
            'media',
            'package_categories__category',
            'package_activities__activity'
        )

    def retrieve(self, request, *args, **kwargs):
        """
        Override retrieve method to provide consistent API response format
        """
        try:
            instance = self.get_object()
            serializer = self.get_serializer(instance)
            
            return Response({
                'status': 'success',
                'message': 'Package basic details retrieved successfully',
                'data': serializer.data
            }, status=status.HTTP_200_OK)
            
        except Package.DoesNotExist:
            return Response({
                'status': 'error',
                'message': 'Package not found'
            }, status=status.HTTP_404_NOT_FOUND)


class PackageItineraryDetailsView(RetrieveAPIView):
    """
    API view for getting package itinerary details including day-wise itinerary, hotels, add-ons, and destination information
    
    URL: /api/packages/v1/package/<external_id>/itenary-details/
    """
    serializer_class = PackageItenaryDetailsSerializer
    permission_classes = [AllowAny]
    lookup_field = 'external_id'

    def get_queryset(self):
        """
        Get packages for the partner resolved by middleware
        """
        # Get partner from request.domain (set by PartnerMiddleware)
        partner = getattr(self.request, 'domain', None)
        
        if not partner:
            # Return empty queryset if no domain resolved
            return Package.objects.none()
        
        # Return packages for the resolved partner
        return Package.objects.filter(
            partner=partner,
            is_published=True,
            is_active=True
        ).select_related('destination')

    def retrieve(self, request, *args, **kwargs):
        """
        Override retrieve method to provide consistent API response format
        """
        try:
            instance = self.get_object()
            serializer = self.get_serializer(instance)
            
            return Response({
                'status': 'success',
                'message': 'Package itinerary details retrieved successfully',
                'data': serializer.data
            }, status=status.HTTP_200_OK)
            
        except Package.DoesNotExist:
            return Response({
                'status': 'error',
                'message': 'Package not found'
            }, status=status.HTTP_404_NOT_FOUND)


class PackageDestinationFaqView(RetrieveAPIView):
    """
    API view for getting package destination FAQs
    
    URL: /api/packages/v1/package/<external_id>/destination-faq/
    """
    serializer_class = PackageDestinationFaqSerializer
    permission_classes = [AllowAny]
    lookup_field = 'external_id'

    def get_queryset(self):
        """
        Get packages for the partner resolved by middleware
        """
        # Get partner from request.domain (set by PartnerMiddleware)
        partner = getattr(self.request, 'domain', None)
        
        if not partner:
            # Return empty queryset if no domain resolved
            return Package.objects.none()
        
        # Return packages for the resolved partner with destination and FAQs prefetched
        return Package.objects.filter(
            partner=partner,
            is_published=True,
            is_active=True
        ).select_related('destination').prefetch_related(
            'destination__faqs'
        )

    def retrieve(self, request, *args, **kwargs):
        """
        Override retrieve method to provide consistent API response format
        """
        try:
            instance = self.get_object()
            serializer = self.get_serializer(instance)
            
            return Response({
                'status': 'success',
                'message': 'Package destination FAQs retrieved successfully',
                'data': serializer.data
            }, status=status.HTTP_200_OK)
            
        except Package.DoesNotExist:
            return Response({
                'status': 'error',
                'message': 'Package not found'
            }, status=status.HTTP_404_NOT_FOUND)


class ActivityGalleryView(RetrieveAPIView):
    """
    API view for getting all images of an activity
    
    URL: /api/packages/v1/activity-gallery/<external_id>/
    """
    serializer_class = ActivityMediaSerializer
    permission_classes = [AllowAny]
    lookup_field = 'external_id'

    def get_queryset(self):
        """
        Get activities for the partner resolved by middleware
        """
        # Get partner from request.domain (set by PartnerMiddleware)
        partner = getattr(self.request, 'domain', None)
        
        if not partner:
            # Return empty queryset if no domain resolved
            return Activity.objects.none()
        
        # Return activities for the resolved partner
        return Activity.objects.filter(
            partner=partner,
            is_active=True
        ).prefetch_related('media')

    def retrieve(self, request, *args, **kwargs):
        """
        Override retrieve method to get activity media instead of activity data
        """
        try:
            activity = self.get_object()
            
            # Get all media for the activity
            media = activity.media.filter(is_active=True).order_by('-main_display', 'created_at')
            serializer = self.get_serializer(media, many=True)
            
            return Response({
                'status': 'success',
                'message': 'Activity gallery retrieved successfully',
                'data': {
                    'count': len(serializer.data),
                    'media': serializer.data,
                }
            }, status=status.HTTP_200_OK)
            
        except Activity.DoesNotExist:
            return Response({
                'status': 'error',
                'message': 'Activity not found'
            }, status=status.HTTP_404_NOT_FOUND)


class PackagePDFGenerationView(APIView):
    """
    API view for generating PDF for package description
    
    URL: /api/packages/v1/package/<external_id>/generate-pdf/
    
    This endpoint:
    1. Validates package exists and is active/published
    2. Initializes PDF helper with package object
    3. Processes PDF generation and S3 upload
    4. Returns S3 URL
    """
    permission_classes = [AllowAny]

    def get(self, request, external_id, *args, **kwargs):
        """
        Generate PDF for the specified package
        """
        try:
            # Get partner from request.domain (set by PartnerMiddleware)
            partner = getattr(request, 'domain', None)
            
            if not partner:
                return Response({
                    'status': 'error',
                    'message': 'Partner domain not resolved'
                }, status=status.HTTP_400_BAD_REQUEST)

            logger.info(f"PDF generation requested for package {external_id} by partner {partner.entity_name}")

            # Fetch and validate package in API itself
            try:
                package = Package.objects.select_related('destination', 'partner').prefetch_related(
                    'highlights',
                    'inclusions', 
                    'addons',
                    'media'
                ).get(
                    external_id=external_id,
                    partner=partner,
                    is_active=True,
                    is_published=True
                )
            except Package.DoesNotExist:
                logger.error(f"Package with external_id '{external_id}' not found or not available")
                return Response({
                    'status': 'error',
                    'message': 'Package not found or not available for PDF generation'
                }, status=status.HTTP_404_NOT_FOUND)

            # Initialize the PDF helper with package object
            pdf_helper = PackagePDFHelper(package)
            
            # Process the full PDF generation workflow
            result = pdf_helper.process_full_pdf_generation()
            
            if result['success']:
                logger.info(f"PDF generated successfully for package {external_id}")
                return Response({
                    'status': 'success',
                    'message': 'PDF generated successfully',
                    'data': {
                        'pdf_url': result['pdf_url'],
                        'file_size': result.get('file_size'),
                        'generated_at': result.get('generated_at')
                    }
                }, status=status.HTTP_200_OK)
            else:
                logger.error(f"PDF generation failed for package {external_id}: {result['error']}")
                return Response({
                    'status': 'error',
                    'message': result['error']
                }, status=result.get('status_code', status.HTTP_500_INTERNAL_SERVER_ERROR))
                
        except Exception as e:
            logger.error(f"Unexpected error during PDF generation for package {external_id}: {str(e)}", exc_info=True)
            return Response({
                'status': 'error',
                'message': 'An unexpected error occurred during PDF generation'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class CustomPackageView(APIView):
    """
    API view for creating custom packages via POST.
    """
    permission_classes = [AllowAny]

    def post(self, request, *args, **kwargs):
        """
        Create a new custom package.
        """
        partner = getattr(request, 'domain', None)
        if not partner:
            return Response({
                'status': 'error',
                'message': 'Partner domain not resolved'
            }, status=status.HTTP_400_BAD_REQUEST)

        serializer = CustomPackageCreateSerializer(data=request.data, context={'partner': partner})
        if serializer.is_valid():
            try:
                with transaction.atomic():
                    # Create the custom package using serializer's create method
                    package = serializer.save()
                    
                    return Response({
                        'status': 'success',
                        'message': 'Custom package created successfully',
                        'data': {
                            'id': package.id,
                            'external_id': package.external_id,
                            'title': package.title,
                            'package_no': package.package_no,
                            'type': package.type,
                            'is_published': package.is_published,
                            'created_at': package.created_at
                        }
                    }, status=status.HTTP_201_CREATED)
            except Exception as e:
                logger.error(f"Custom package creation failed: {str(e)}")
                return Response({
                    'status': 'error',
                    'message': f'Custom package creation failed: {str(e)}'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        return Response({
            'status': 'error',
            'message': 'Validation failed',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)
