from django.shortcuts import render
from django.http import JsonResponse
from django.views import View
from django.core.paginator import Paginator
from django.apps import apps
from django.db.models import Q
import logging

logger = logging.getLogger(__name__)

# Create your views here.

class InfiniteScrollDataView(View):
    """
    AJAX view to handle infinite scroll data requests for active records
    """
    
    def get(self, request):
        logger.info("InfiniteScrollDataView.get() called")
        
        try:
            # Get parameters
            model_name = request.GET.get('model')
            search_query = request.GET.get('search', '')
            page = int(request.GET.get('page', 1))
            search_field = request.GET.get('search_field', 'name')
            display_field = request.GET.get('display_field', 'name')
            page_size = 20  # Items per page
            
            logger.debug(f"Request parameters: model={model_name}, search='{search_query}', page={page}, search_field={search_field}, display_field={display_field}")
            
            if not model_name:
                logger.error("Model name not provided in request")
                return JsonResponse({'error': 'Model name required'}, status=400)
            
            # Get the model class
            try:
                app_label, model_class_name = model_name.split('.')
                model_class = apps.get_model(app_label, model_class_name)
                logger.debug(f"Successfully resolved model: {model_class}")
            except Exception as e:
                logger.error(f"Failed to resolve model {model_name}: {str(e)}")
                return JsonResponse({'error': f'Invalid model name: {model_name}'}, status=400)
            
            # Build the queryset - only filter by active status
            try:
                queryset = model_class.objects.filter(is_active=True)
                initial_count = queryset.count()
                logger.debug(f"Initial queryset count (active only): {initial_count}")
                
                # Apply search filter if provided
                if search_query:
                    filter_kwargs = {f'{search_field}__icontains': search_query}
                    queryset = queryset.filter(**filter_kwargs)
                    search_count = queryset.count()
                    logger.debug(f"After search filter '{search_query}': {search_count} items")
                
                # Order by the display field
                queryset = queryset.order_by(display_field)
                
                # Optimize query - only select needed fields
                queryset = queryset.only('id', display_field)
                logger.debug(f"Optimized queryset with only('id', '{display_field}')")
                
            except Exception as e:
                logger.error(f"Error building queryset: {str(e)}")
                return JsonResponse({'error': f'Error filtering data: {str(e)}'}, status=500)
            
            # Paginate
            try:
                paginator = Paginator(queryset, page_size)
                logger.debug(f"Pagination: total_pages={paginator.num_pages}, total_count={paginator.count}")
                
                page_obj = paginator.get_page(page)
                logger.debug(f"Page {page}: has_previous={page_obj.has_previous()}, has_next={page_obj.has_next()}, object_count={len(page_obj)}")
                
            except Exception as e:
                logger.error(f"Error during pagination: {str(e)}")
                return JsonResponse({'error': f'Pagination error: {str(e)}'}, status=500)
            
            # Prepare response data
            try:
                items = []
                for obj in page_obj:
                    display_value = getattr(obj, display_field, str(obj))
                    items.append({
                        'id': obj.pk,
                        'text': display_value
                    })
                
                logger.debug(f"Prepared {len(items)} items for response")
                
                if items:
                    logger.debug(f"First item: {items[0]}")
                    logger.debug(f"Last item: {items[-1]}")
                
            except Exception as e:
                logger.error(f"Error preparing response data: {str(e)}")
                return JsonResponse({'error': f'Error preparing data: {str(e)}'}, status=500)
            
            response_data = {
                'items': items,
                'has_next': page_obj.has_next(),
                'page': page,
                'total_pages': paginator.num_pages,
                'total_count': paginator.count
            }
            
            logger.info(f"Successfully returning {len(items)} items for page {page} (total: {paginator.count})")
            return JsonResponse(response_data)
            
        except Exception as e:
            logger.error(f"Unexpected error in InfiniteScrollDataView: {str(e)}", exc_info=True)
            return JsonResponse({'error': str(e)}, status=500)
