from packages.utils.serializer_utils import ItineraryDetailSerializer

def get_itinerary_data(itineraries):
    """
    Get detailed itinerary data for a package using comprehensive serializers
    
    Args:
        itineraries: QuerySet of Itinerary objects
        
    Returns:
        List of detailed itinerary data with nested day items, hotel_data, and activity_data
    """
    itinerary_data = None
    if itineraries:
        # Filter active itineraries and order by day_number
        active_itineraries = itineraries.filter(is_active=True).order_by('day_number')
        
        # Serialize the itineraries with nested data using our detailed serializer
        serializer = ItineraryDetailSerializer(active_itineraries, many=True)
        itinerary_data = serializer.data

    return itinerary_data
