import requests
import logging
from typing import Optional
from django.conf import settings
from django.core.exceptions import ValidationError
from base.static import Constants

logger = logging.getLogger(__name__)


class OpenAIActivityHelper:
    """
    OpenAI helper for Custom Activity highlights generation and reframing.
    """
    
    def __init__(self):
        """Initialize the OpenAI activity helper"""
        self.api_key = settings.OPENAI_API_KEY
        self.errors = []
        
        if not self.api_key:
            raise ValidationError("OpenAI API key is not configured")
        
        logger.info("OpenAI Activity Helper initialized")

    def generate_highlights(self, activity_title: str) -> Optional[str]:
        """
        Generate highlights for a custom activity based on its title
        
        Args:
            activity_title: The title of the custom activity
            
        Returns:
            Generated highlights as a string or None if generation fails
        """
        try:
            prompt = f"""
            Create engaging and attractive highlights for a travel activity with the title: "{activity_title}"

            Generate a single paragraph with exactly 3-4 sentences that would make travelers excited about this activity.
            Each sentence should be:
            - One concise, compelling sentence
            - Focused on unique experiences, benefits, or features
            - Written in an engaging, marketing-friendly tone
            - Connected to form a flowing paragraph

            Format the output as a single paragraph with sentences separated by spaces (not line breaks).
            Do not use bullet points, numbers, or multiple paragraphs.
            """
            
            response = self._call_openai_api(prompt)
            if response:
                logger.info(f"Successfully generated highlights for activity: {activity_title}")
                return response.strip()
            
        except Exception as e:
            error_msg = f"Failed to generate highlights for '{activity_title}': {str(e)}"
            self.errors.append(error_msg)
            logger.error(error_msg)
        
        return None

    def reframe_highlights(self, existing_highlights: str, activity_title: str) -> Optional[str]:
        """
        Reframe existing highlights to make them more engaging
        
        Args:
            existing_highlights: Current highlights text
            activity_title: The title of the custom activity for context
            
        Returns:
            Reframed highlights as a string or None if reframing fails
        """
        try:
            prompt = f"""
            Reframe and improve the following highlights for a travel activity titled "{activity_title}":

            Current highlights:
            {existing_highlights}

            Please rewrite these highlights as a single paragraph with 3-4 compelling sentences.
            Each sentence should be:
            - More vivid and descriptive
            - Focus on emotional benefits and unique experiences
            - Use active, exciting language
            - Maintain accuracy to the original meaning
            - Flow naturally together in a single paragraph

            Format the output as a single paragraph with sentences separated by spaces (not line breaks).
            Do not use bullet points, numbers, or multiple paragraphs.
            """
            
            response = self._call_openai_api(prompt)
            if response:
                logger.info(f"Successfully reframed highlights for activity: {activity_title}")
                return response.strip()
            
        except Exception as e:
            error_msg = f"Failed to reframe highlights for '{activity_title}': {str(e)}"
            self.errors.append(error_msg)
            logger.error(error_msg)
        
        return None

    def _call_openai_api(self, prompt: str) -> Optional[str]:
        """
        Make API call to OpenAI using the chat completions endpoint
        
        Args:
            prompt: The prompt to send to OpenAI
            
        Returns:
            API response content or None if call fails
        """
        try:
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json',
            }
            
            data = {
                'model': 'gpt-4o-mini',
                'messages': [
                    {
                        'role': 'system',
                        'content': 'You are a professional travel marketing copywriter specializing in creating compelling activity descriptions and highlights.'
                    },
                    {
                        'role': 'user',
                        'content': prompt
                    }
                ],
                'max_tokens': 500,
                'temperature': 0.7,
            }
            
            logger.info("Making OpenAI API call for activity highlights")
            
            response = requests.post(
                'https://api.openai.com/v1/chat/completions',
                headers=headers,
                json=data,
                timeout=30
            )
            
            if response.status_code == 200:
                response_data = response.json()
                
                if 'choices' in response_data and len(response_data['choices']) > 0:
                    content = response_data['choices'][0]['message']['content']
                    logger.info("OpenAI API call successful")
                    return content
                else:
                    logger.error("Unexpected OpenAI response structure")
                    return None
            else:
                error_response = response.text
                logger.error(f"OpenAI API error (HTTP {response.status_code}): {error_response}")
                return None
                
        except requests.RequestException as e:
            logger.error(f"OpenAI API request failed: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"OpenAI API call failed: {str(e)}")
            return None

    def has_errors(self) -> bool:
        """Check if there were any errors during processing"""
        return len(self.errors) > 0

    def get_errors(self) -> list:
        """Get list of error messages"""
        return self.errors.copy() 