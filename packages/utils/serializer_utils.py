from rest_framework import serializers
from packages.models import Itinerary, ItineraryDayItem, ItineraryHotel, ItineraryActivity
from dynamic_packages.models import Hotel, HotelAddress, HotelMedia, Facility
from packages.models import CustomActivity


class HotelImageSerializer(serializers.ModelSerializer):
    """Serializer for hotel images"""
    class Meta:
        model = HotelMedia
        fields = ['external_id', 'media']
    
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        if instance.media:
            representation['media'] = instance.media.name
        return representation


class HotelAddressSerializer(serializers.ModelSerializer):
    """Serializer for hotel address"""
    city = serializers.SerializerMethodField()
    state = serializers.SerializerMethodField()
    country = serializers.SerializerMethodField()

    class Meta:
        model = HotelAddress
        fields = ['external_id', 'address_line', 'postal_code', 'city', 'state', 'country']

    def get_city(self, obj):
        """Return city name"""
        return obj.city.name if obj.city else None

    def get_state(self, obj):
        """Return state name"""
        return obj.state.name if obj.state else None

    def get_country(self, obj):
        """Return country name"""
        return obj.country.name if obj.country else None


class HotelFacilitySerializer(serializers.ModelSerializer):
    """Serializer for hotel facilities"""
    class Meta:
        model = Facility
        fields = ['amenities']


class ItineraryHotelDataSerializer(serializers.ModelSerializer):
    """Serializer for hotel data in itinerary day items"""
    hotel = serializers.SerializerMethodField()
    hotel_name = serializers.SerializerMethodField()
    check_in_time = serializers.TimeField()
    check_out_time = serializers.TimeField()
    meta_information = serializers.JSONField()

    images = serializers.SerializerMethodField()
    address = serializers.SerializerMethodField()
    description = serializers.SerializerMethodField()
    amenities = serializers.SerializerMethodField()
    rating = serializers.SerializerMethodField()
    
    class Meta:
        model = ItineraryHotel
        fields = [
            'hotel', 'hotel_name', 'check_in_time', 'check_out_time', 'meta_information',
            'images', 'address', 'description', 'amenities', 'rating'
        ]
    
    def get_hotel(self, obj):
        """Return hotel ID or None"""
        return obj.hotel.id if obj.hotel else None
    
    def get_hotel_name(self, obj):
        """Return hotel name"""
        return obj.hotel.name if obj.hotel else None

    def get_images(self, obj):
        """Return images for the hotel"""
        hotel = obj.hotel
        hotel_images = hotel.media.all()
        return HotelImageSerializer(hotel_images, many=True).data

    def get_address(self, obj):
        """Return address for the hotel"""
        hotel_address = obj.hotel.address
        return HotelAddressSerializer(hotel_address).data

    def get_description(self, obj):
        """Return description for the hotel"""
        return obj.hotel.description

    def get_amenities(self, obj):
        """Return amenities for the hotel"""
        hotel = obj.hotel
        amenities = hotel.facilities.all()
        return HotelFacilitySerializer(amenities, many=True).data

    def get_rating(self, obj):
        """Return rating for the hotel"""
        return obj.hotel.rating


class ItineraryActivityDataSerializer(serializers.ModelSerializer):
    """Serializer for activity data in itinerary day items"""
    activity = serializers.SerializerMethodField()
    start_time = serializers.TimeField()
    end_time = serializers.TimeField()
    meta_information = serializers.JSONField()
    
    class Meta:
        model = ItineraryActivity
        fields = ['activity', 'start_time', 'end_time', 'meta_information']
    
    def get_activity(self, obj):
        """Return activity ID or None"""
        return obj.activity.id if obj.activity else None


class ItineraryDayItemDetailSerializer(serializers.ModelSerializer):
    """Detailed serializer for individual day items within an itinerary"""
    type = serializers.CharField()
    title = serializers.CharField()
    description = serializers.CharField()
    order = serializers.IntegerField()
    duration = serializers.CharField()
    inclusions = serializers.JSONField()
    meta_information = serializers.JSONField()
    hotel_data = serializers.SerializerMethodField()
    activity_data = serializers.SerializerMethodField()
    
    class Meta:
        model = ItineraryDayItem
        fields = [
            'type', 'title', 'description', 'order', 'duration', 
            'inclusions', 'meta_information', 'hotel_data', 'activity_data'
        ]
    
    def get_hotel_data(self, obj):
        """Return hotel data if this is a hotel type day item"""
        if obj.type == 'Hotel' and obj.hotel:
            return ItineraryHotelDataSerializer(obj.hotel).data
        return None
    
    def get_activity_data(self, obj):
        """Return activity data if this is an activity type day item"""
        if obj.type == 'Activity' and obj.activity:
            return ItineraryActivityDataSerializer(obj.activity).data
        return None


class ItineraryDetailSerializer(serializers.ModelSerializer):
    """Detailed serializer for itinerary with nested day items"""
    day_number = serializers.IntegerField()
    date = serializers.DateField()
    day_title = serializers.CharField()
    description = serializers.CharField()
    order = serializers.IntegerField()
    inclusions = serializers.JSONField()
    meta_information = serializers.JSONField()
    day_items = serializers.SerializerMethodField()
    
    class Meta:
        model = Itinerary
        fields = [
            'day_number', 'date', 'day_title', 'description', 'order',
            'inclusions', 'meta_information', 'day_items'
        ]
    
    def get_day_items(self, obj):
        """Return ordered day items for this itinerary"""
        day_items = obj.day_items.filter(is_active=True).order_by('order')
        return ItineraryDayItemDetailSerializer(day_items, many=True).data


def get_itinerary_data(itineraries):
    """
    Get detailed itinerary data for a package with complete nested structure
    
    Args:
        itineraries: QuerySet of Itinerary objects
        
    Returns:
        List of detailed itinerary data with nested day items
    """
    if not itineraries:
        return []
    
    # Filter active itineraries and order by day_number
    active_itineraries = itineraries.filter(is_active=True).order_by('day_number')
    
    # Serialize the itineraries with nested data
    serializer = ItineraryDetailSerializer(active_itineraries, many=True)
    
    return serializer.data


# Alternative simple function for backward compatibility
def get_simple_itinerary_data(itineraries):
    """
    Get simplified itinerary data (for backward compatibility)
    
    Args:
        itineraries: QuerySet of Itinerary objects
        
    Returns:
        List of basic itinerary data
    """
    if not itineraries:
        return []
    
    itinerary_data = []
    for itinerary in itineraries.filter(is_active=True).order_by('day_number'):
        day_items = []
        for day_item in itinerary.day_items.filter(is_active=True).order_by('order'):
            day_items.append({
                'type': day_item.type,
                'title': day_item.title,
                'description': day_item.description,
                'order': day_item.order,
                'duration': day_item.duration,
                'inclusions': day_item.inclusions or [],
                'meta_information': day_item.meta_information or {}
            })
        
        itinerary_data.append({
            'day_number': itinerary.day_number,
            'date': itinerary.date.isoformat() if itinerary.date else None,
            'day_title': itinerary.day_title,
            'description': itinerary.description,
            'order': itinerary.order,
            'inclusions': itinerary.inclusions or [],
            'meta_information': itinerary.meta_information or {},
            'day_items': day_items
        })
    
    return itinerary_data
