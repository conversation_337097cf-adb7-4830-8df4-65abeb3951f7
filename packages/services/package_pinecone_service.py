"""
Package Pinecone Service.

This service is used to manage the packages data in the Pinecone index.
"""
from typing import Dict
from packages.models import Package
from packages.services.abstract_pinecone_service import AbstractPineconeService


PACKAGE_PINECONE_INDEX_NAME = "rag-packages-openai-v1-index"


class PackagePineconeService(AbstractPineconeService[Package]):
    """
    A specialized Pinecone service for managing packages, inheriting from
    AbstractPineconeService.
    """

    def __init__(self):
        """Initializes the PackagePineconeService."""
        super().__init__(
            name="PackagePineconeService", index_name=PACKAGE_PINECONE_INDEX_NAME
        )

    def _prepare_text_for_embedding(self, data: Package) -> str:
        """
        Creates a single, semantically rich string from package data for embedding.
        """
        title = data.title or ""
        about = data.about_this_tour or ""
        highlights = " ".join([h.value for h in data.highlights.all()])
        inclusions = " ".join([i.value for i in data.inclusions.all()])
        destination = data.destination.title if data.destination else ""

        parts = [
            f"Title: {title}",
            f"Destination: {destination}",
            f"About: {about}",
            f"Highlights: {highlights}",
            f"Inclusions: {inclusions}",
        ]

        return ". ".join(filter(None, parts))

    def _extract_metadata(self, data: Package) -> Dict:
        """
        Extracts structured metadata from the package data for filtering and display.
        """

        metadata = {
            "id": data.id,
            "title": data.title,
            "package_no": data.package_no,
            "destination": data.destination.title if data.destination else None,
            "price": float(data.price) if data.price is not None else None,
            "duration_in_nights": int(data.duration_in_nights) if data.duration_in_nights is not None else None,
            "duration_in_days": int(data.duration_in_days) if data.duration_in_days is not None else None,
            "rating": float(data.rating) if data.rating is not None else None,
            "best_time_to_visit_months": ",".join(data.best_time_to_visit_months) if data.best_time_to_visit_months else None,
        }

        return {k: v for k, v in metadata.items() if v is not None}

    def upsert_package(self, package: Package, namespace: str):
        """Upserts a single package to the Pinecone index."""
        return self.upsert(package, namespace)


package_pinecone_service = PackagePineconeService()
