"""
Activity Pinecone Service.

This service is used to manage the activities data in the Pinecone index.
"""
from typing import Dict
from packages.models import CustomActivity
from packages.services.abstract_pinecone_service import AbstractPineconeService


ACTIVITY_PINECONE_INDEX_NAME = "rag-activities-openai-v1-index"


class ActivityPineconeService(AbstractPineconeService[CustomActivity]):
    """
    A specialized Pinecone service for managing activities, inheriting from
    BasePineconeService.
    """

    def __init__(self):
        """Initializes the ActivityPineconeService."""
        super().__init__(
            name="ActivityPineconeService", index_name=ACTIVITY_PINECONE_INDEX_NAME
        )

    def _prepare_text_for_embedding(self, data: CustomActivity) -> str:
        """
        Creates a single, semantically rich string from activity data for embedding.
        """
        title = data.title or ""
        abstract = data.abstract or ""
        description = data.description or ""
        highlights = data.highlights or ""
        activity_type = data.activity_type or ""
        cancellation_policy_text = data.cancellation_policy_text or ""

        parts = [
            f"Title: {title}",
            f"Summary: {abstract}",
            f"Description: {description}",
            f"Highlights: {highlights}",
            f"Type: {activity_type}",
            f"Cancellation Policy: {cancellation_policy_text}",
        ]

        return ". ".join(filter(None, parts))

    def _extract_metadata(self, data: CustomActivity) -> Dict:
        """
        Extracts structured metadata from the activity data for filtering and display.
        """

        metadata = {
            "id": data.id,
            "title": data.title,
            "tour_id": data.tour_id,
            "city": data.destination.title if data.destination else None,
            "price": float(data.price) if data.price is not None else None,
            "overall_rating": float(data.overall_rating) if data.overall_rating is not None else None,
            "number_of_ratings": int(data.number_of_ratings) if data.number_of_ratings is not None else None,
        }

        return {k: v for k, v in metadata.items() if v is not None}

    def upsert_activity(self, activity: CustomActivity, namespace: str):
        """Upserts a single activity to the Pinecone index."""
        return self.upsert(activity, namespace)


activity_pinecone_service = ActivityPineconeService()
