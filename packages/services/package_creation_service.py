"""
Unified Package Creation Service

This service provides a unified OOP-based approach for creating packages from different sources:
- Manual form data
- JSON files
- Document files (DOC/DOCX)

All sources go through the same validation and creation pipeline.
"""

from django.core.exceptions import ValidationError
from django.db import transaction
from typing import Dict, Any, Optional
import logging
import json
import ast

from packages.utils.validation_helpers import PackageJsonValidationHelper
# from packages.utils.deepseek_file_helper import DeepSeekFileHelper
from packages.utils.openai_file_helper import OpenAIFileHelper
from packages.models import (
    Package, 
    Category, 
    Destination, 
    Activity, 
    PackageCategory, 
    PackageActivity, 
    PackageHighlight, 
    PackageInclusion, 
    PackageAddon
)
from packages.choices import PackageTypeChoices
from accounts.models import Partner

logger = logging.getLogger(__name__)


class PackageCreationService:
    """
    Unified service for creating packages from various data sources.
    Follows OOP principles for maintainability and reusability.
    """
    
    def __init__(self, partner: Partner, source_type: str, file=None, file_type=None, updating_package=False, skip_m2m_processing=False):
        """
        Initialize PackageCreationService
        
        Args:
            partner: Partner instance
            source_type: Type of source data ('manual', 'json', 'doc', 'docx')
            file: File object (for file-based processing)
            file_type: Type of file (for file-based processing)
            updating_package: Whether this is updating an existing package
            skip_m2m_processing: Whether to skip M2M relationship processing (useful for admin forms)
        """
        print(f"[DEBUG] PackageCreationService.__init__ called with partner={partner}, source_type={source_type}, file_type={file_type}, updating_package={updating_package}, skip_m2m_processing={skip_m2m_processing}")
        logger.info(f"PackageCreationService initialized: partner={partner}, source_type={source_type}, file_type={file_type}, updating_package={updating_package}, skip_m2m_processing={skip_m2m_processing}")
        
        self.partner = partner
        self.source_type = source_type  # 'manual', 'json', 'doc', 'docx' 
        self.file = file
        self.file_type = file_type
        self.updating_package = updating_package
        self.skip_m2m_processing = skip_m2m_processing  # New flag to skip M2M processing
        
        # Processing data
        self.raw_json_data = {}  # Original raw data
        self.validated_json_data = {}  # Data after OpenAI processing and validation
        self.created_package = None
        
        # Error tracking
        self.errors = []
        self.warnings = []

        print(f"[DEBUG] PackageCreationService.__init__ completed successfully")
        logger.info(f"PackageCreationService initialization completed")

    def convert_form_data_to_raw_data(self, form_data):
        print(f"[DEBUG] convert_form_data_to_raw_data called with form_data keys: {list(form_data.keys()) if form_data else 'None'}")
        logger.info(f"Converting form data to raw data, form has {len(form_data)} fields" if form_data else "No form data provided")
        
        # Convert M2M relationships to lists (lowercase for consistency)
        categories = []
        if 'categories' in form_data and form_data['categories']:
            categories = [cat.title.lower() for cat in form_data['categories']]  # Convert to lowercase
            print(f"[DEBUG] Converted categories: {categories}")
        
        activities = []
        if 'activities' in form_data and form_data['activities']:
            activities = [act.title.lower() for act in form_data['activities']]  # Convert to lowercase
            print(f"[DEBUG] Converted activities: {activities}")
        
        # Create JSON structure based on the package format structure
        json_data = {
            # ========== REQUIRED BASIC FIELDS ==========
            'title': form_data.get('title', ''),
            'package_no': form_data.get('package_no', ''),
            'destination': form_data.get('destination').title.lower() if form_data.get('destination') else '',
            'owner': form_data.get('owner', ''),
            'type': form_data.get('type', ''),
            'duration': form_data.get('duration', ''),
            'price_per_person': form_data.get('price_per_person', ''),
            'about_this_tour': form_data.get('about_this_tour', ''),
            
            # ========== M2M RELATIONSHIP FIELDS ==========
            'category': categories,  # For AI processing (lowercase)
            'activities': activities,  # For AI processing (lowercase)
            
            # ========== REQUIRED ARRAY FIELDS ==========
            'highlights': form_data.get('highlights', []),
            'inclusions': form_data.get('inclusions', []),
            'exclusions': form_data.get('exclusions', []),
            
            # ========== OPTIONAL TEXT FIELDS ==========
            'itinerary': form_data.get('itinerary', ''),
            'visa_type': form_data.get('visa_type', []),
            'hotels': form_data.get('hotels', []),
            'popular_activities': form_data.get('popular_activities', []),
        }
        
        # ========== AI-ENHANCEABLE FIELDS - Only include if not empty ==========
        # This allows AI to generate content for empty fields
        ai_enhanceable_fields = {
            'best_time_to_visit': form_data.get('best_time_to_visit', ''),
            'rating_description': form_data.get('rating_description', ''),
            'destination_safety': form_data.get('destination_safety', ''),
            'popular_restaurants': form_data.get('popular_restaurants', []),
            'what_to_shop': form_data.get('what_to_shop', ''),
            'what_to_pack': form_data.get('what_to_pack', ''),
            'cultural_info': form_data.get('cultural_info', ''),
            'important_notes': form_data.get('important_notes', []),
            'addons': form_data.get('addons', []),  # Moved here so empty arrays get generated
            'currency_conversion_rate': form_data.get('currency_conversion_rate', ''),  # Moved here so OpenAI generates proper rates
        }
        
        # Only include AI-enhanceable fields if they have meaningful content
        for field_name, field_value in ai_enhanceable_fields.items():
            if field_value:  # Check if field has content
                if isinstance(field_value, str) and field_value.strip():
                    json_data[field_name] = field_value
                    print(f"[DEBUG] Including AI field '{field_name}' with user content: '{field_value[:50]}...'")
                elif isinstance(field_value, list) and len(field_value) > 0:
                    json_data[field_name] = field_value
                    print(f"[DEBUG] Including AI field '{field_name}' with user content: {len(field_value)} items")
                elif isinstance(field_value, (int, float)) and field_value != 0:
                    json_data[field_name] = field_value
                    print(f"[DEBUG] Including AI field '{field_name}' with user content: {field_value}")
            else:
                print(f"[DEBUG] Excluding empty AI field '{field_name}' - will be generated by AI")
        
        # ========== NON-AI FIELDS - Always include ==========
        json_data.update({
            'rating': float(form_data.get('rating', 4.5)) if form_data.get('rating') else 4.5,  # Default to 4.5 for better AI context
            'is_published': form_data.get('is_published', False),
            'is_active': form_data.get('is_active', True),
            'explore_order': form_data.get('explore_order', 0),  # Admin-configurable ordering field
        })
        
        print(f"[DEBUG] Form data converted to JSON structure with {len(json_data)} fields")
        print(f"[DEBUG] Key fields - title: '{json_data.get('title')}', package_no: '{json_data.get('package_no')}', destination: '{json_data.get('destination')}'")
        logger.info(f"Form data converted successfully: title='{json_data.get('title')}', package_no='{json_data.get('package_no')}'")
        
        self.raw_json_data = json_data
        return json_data

    def process_package(self):
        print(f"[DEBUG] process_package called with source_type={self.source_type}, updating_package={self.updating_package}")
        logger.info(f"Starting package processing: source_type={self.source_type}, updating_package={self.updating_package}")
        
        if self.file_type == 'json' or self.source_type == "manual":
            print(f"[DEBUG] JSON file type or manual form detected, running basic validation")
            # Step 1: Basic validation for critical fields
            self._basic_validation()
            print(f"[DEBUG] Basic validation completed successfully")
            
        if self.file_type == 'json':
            # Step 1.5: Fix addons/add_ons field naming before sending to OpenAI
            print(f"[DEBUG] Checking and fixing addons/add_ons field naming")
            self._fix_addons_field_naming()
            print(f"[DEBUG] Addons field naming check completed")
            
        # Step 2: Process with OpenAI (for consistency and validation)
        print(f"[DEBUG] Starting OpenAI processing")
        self.processed_json_data = self._process_with_openai()
        print(f"[DEBUG] OpenAI processing completed successfully")
        
        # Step 3: Validate the processed data
        print(f"[DEBUG] Starting data validation")
        self.validated_json_data = self._validate_data()
        print(f"[DEBUG] Data validation completed successfully")

        # If updating package, return validated data
        if self.updating_package:
            print(f"[DEBUG] Updating package mode - returning validated data")
            logger.info("Package update mode - returning validated data for update")
            return self.validated_json_data

        # Step 4: Create the package
        print(f"[DEBUG] Creating new package")
        self.created_package = self._create_package()
        print(f"[DEBUG] Package creation completed successfully with ID: {self.created_package.id if self.created_package else 'None'}")
        logger.info(f"Package processing completed successfully, package_id={self.created_package.id if self.created_package else 'None'}")

        return self.created_package

    def _fix_addons_field_naming(self):
        """
        Fix addons field naming - check for both 'addons' and 'add_ons' keys
        and normalize to 'addons' before sending to OpenAI
        """
        print(f"[DEBUG] _fix_addons_field_naming started")
        
        if not self.raw_json_data:
            print(f"[DEBUG] No raw_json_data to fix")
            return
        
        # Check if we have addons data in either key
        addons_value = None
        has_addons_key = 'addons' in self.raw_json_data
        has_add_ons_key = 'add_ons' in self.raw_json_data
        
        print(f"[DEBUG] has_addons_key: {has_addons_key}, has_add_ons_key: {has_add_ons_key}")
        
        if has_addons_key:
            addons_value = self.raw_json_data['addons']
            print(f"[DEBUG] Found 'addons' key with value: {addons_value}")
        elif has_add_ons_key:
            addons_value = self.raw_json_data['add_ons']
            print(f"[DEBUG] Found 'add_ons' key with value: {addons_value}")
            # Normalize the key name to 'addons'
            self.raw_json_data['addons'] = addons_value
            del self.raw_json_data['add_ons']
            print(f"[DEBUG] Converted 'add_ons' to 'addons' key")
        
        # Validate the addons value
        if addons_value:
            if isinstance(addons_value, str) and addons_value.strip():
                print(f"[DEBUG] Addons value is valid string: '{addons_value.strip()}'")
            elif isinstance(addons_value, list) and len(addons_value) > 0:
                print(f"[DEBUG] Addons value is valid list with {len(addons_value)} items: {addons_value}")
            else:
                print(f"[DEBUG] Addons value is present but invalid format: {addons_value} (type: {type(addons_value)})")
        else:
            print(f"[DEBUG] No valid addons value found")
        
        print(f"[DEBUG] _fix_addons_field_naming completed")

    def _basic_validation(self) -> None:
        """Perform basic validation of required fields and data types"""
        self.errors = []
        
        print("[DEBUG] Starting basic validation")
        
        # Required fields check
        required_fields = [
            'package_no', 'title', 'destination', 'type', 'duration', 
            'price_per_person', 'about_this_tour', 'highlights'
        ]
        
        for field in required_fields:
            if field not in self.raw_json_data:
                self.errors.append(f"Missing required field: {field}")
            elif field in ['highlights']:
                # Special validation for required arrays that MUST be non-empty
                value = self.raw_json_data[field]
                if not isinstance(value, list) or len(value) == 0:
                    self.errors.append(f"Required field '{field}' must be a non-empty list")
            elif not str(self.raw_json_data[field]).strip():
                self.errors.append(f"Required field '{field}' cannot be empty")
        
        # Basic type validation (inclusions and addons can be empty but must be lists if present)
        if 'highlights' in self.raw_json_data and not isinstance(self.raw_json_data['highlights'], list):
            self.errors.append("Highlights must be a list")
            
        if 'inclusions' in self.raw_json_data and not isinstance(self.raw_json_data['inclusions'], list):
            self.errors.append("Inclusions must be a list")
            
        if 'addons' in self.raw_json_data and not isinstance(self.raw_json_data['addons'], list):
            self.errors.append("Addons must be a list")
        
        # Additional validation: inclusions and addons can be empty (no restriction on length)
        print(f"[DEBUG] Inclusions validation: field present = {'inclusions' in self.raw_json_data}, "
              f"value = {self.raw_json_data.get('inclusions')}, "
              f"length = {len(self.raw_json_data.get('inclusions', []))}")
        
        # Price validation for database limits
        price_per_person = self.raw_json_data.get('price_per_person')
        if price_per_person:
            try:
                from decimal import Decimal
                import re
                
                # Extract numeric part from price_per_person
                price_str = str(price_per_person)
                numeric_part = re.sub(r'[^\d\.]', '', price_str.replace(',', ''))
                
                if numeric_part:
                    price_value = Decimal(numeric_part)
                    # Check database field limits (max_digits=10, decimal_places=2)
                    max_price = Decimal('99999999.99')
                    if price_value > max_price:
                        self.errors.append(
                            f"Price cannot exceed ₹99,999,999.99 due to database limitations. "
                            f"Current value: ₹{price_value:,.2f}. Please enter a smaller amount."
                        )
            except Exception as e:
                self.errors.append(f"Invalid price format: {str(e)}")
        
        # Check extracted price field as well (if already processed)
        price = self.raw_json_data.get('price')
        if price is not None:
            try:
                from decimal import Decimal
                price_value = Decimal(str(price))
                max_price = Decimal('99999999.99')
                if price_value > max_price:
                    self.errors.append(
                        f"Price value exceeds database limit of ₹99,999,999.99. "
                        f"Current value: ₹{price_value:,.2f}"
                    )
            except Exception as e:
                self.errors.append(f"Invalid price value: {str(e)}")
        
        if self.errors:
            print(f"[DEBUG] Basic validation failed with {len(self.errors)} errors: {self.errors}")
            raise ValidationError(self.errors)
        
        print("[DEBUG] Basic validation completed successfully")

    def _process_with_openai(self) -> Dict[str, Any]:
        """
        Process data with OpenAI for consistency and fixing, including icon mapping
        
        For DOCX files: First convert to JSON, then fix the JSON
        For JSON files: Directly fix the JSON
        
        Returns:
            Processed and fixed data with icon mappings
        """
        print(f"[DEBUG] _process_with_openai started with file_type={self.file_type}")
        logger.info(f"Starting OpenAI processing with file_type={self.file_type}")
        
        try:
            # Handle DOCX files with two-step process
            if self.file_type in ['doc', 'docx']:
                print(f"[DEBUG] Processing DOCX file - Step 1: Convert to JSON")
                
                # Step 1: Convert DOCX to text, then to JSON
                json_data = self._convert_docx_to_json()
                print(f"[DEBUG] DOCX converted to JSON, keys: {list(json_data.keys())}")
                
                # Step 2: Use the converted JSON for OpenAI processing
                self.raw_json_data = json_data
                print(f"[DEBUG] Updated raw_json_data with converted JSON")
            
            print(f"[DEBUG] Creating OpenAIFileHelper instance")
            print(f"[DEBUG] Raw data being sent to OpenAI has addons: {'addons' in self.raw_json_data}, value: {self.raw_json_data.get('addons')}")
            print(f"[DEBUG] Raw data being sent to OpenAI has inclusions: {'inclusions' in self.raw_json_data}, value: {self.raw_json_data.get('inclusions')}")
            print(f"[DEBUG] Raw data being sent to OpenAI has highlights: {'highlights' in self.raw_json_data}, value: {self.raw_json_data.get('highlights')}")
            
            # Preserve addons field before OpenAI processing
            preserved_addons = self.raw_json_data.get('addons')
            preserved_inclusions = self.raw_json_data.get('inclusions')
            preserved_highlights = self.raw_json_data.get('highlights')
            preserved_visa_type = self.raw_json_data.get('visa_type')  # Add visa_type preservation
            print(f"[DEBUG] Preserving addons field: {preserved_addons}")
            print(f"[DEBUG] Preserving inclusions field: {preserved_inclusions}")
            print(f"[DEBUG] Preserving highlights field: {preserved_highlights}")
            print(f"[DEBUG] Preserving visa_type field: {preserved_visa_type}")  # Add debug log
            
            # For JSON processing, OpenAI helper now handles icon class data internally
            openai_helper = OpenAIFileHelper(self.raw_json_data, 'json')  # Always 'json' after conversion
            print(f"[DEBUG] OpenAIFileHelper created, calling process()")
            processed_data = openai_helper.process()
            print(f"[DEBUG] OpenAI processing completed, checking for errors")
            print(f"[DEBUG] Processed data from OpenAI has addons: {'addons' in processed_data}, value: {processed_data.get('addons')}")
            print(f"[DEBUG] Processed data keys: {list(processed_data.keys())}")
            
            # ===== DEBUG: Check what OpenAI actually generated =====
            expected_ai_fields = [
                'rating_description', 'destination_safety', 'popular_restaurants',
                'what_to_shop', 'what_to_pack', 'cultural_info', 'important_notes'
            ]
            print(f"[DEBUG] ===== CHECKING OPENAI GENERATED FIELDS =====")
            for field in expected_ai_fields:
                if field in processed_data:
                    value = processed_data[field]
                    if isinstance(value, str):
                        print(f"[DEBUG] OpenAI generated {field}: '{value[:100]}...' (length: {len(value)})")
                    elif isinstance(value, list):
                        print(f"[DEBUG] OpenAI generated {field}: {len(value)} items - {value}")
                    else:
                        print(f"[DEBUG] OpenAI generated {field}: {value} (type: {type(value)})")
                else:
                    print(f"[DEBUG] ❌ OpenAI MISSING {field}")
            print(f"[DEBUG] ===== END OPENAI FIELD CHECK =====")
            
            # Check rating specifically
            if 'rating' in processed_data:
                rating_value = processed_data.get('rating')
                print(f"[DEBUG] OpenAI generated rating: {rating_value} (type: {type(rating_value)})")
            else:
                print(f"[DEBUG] ❌ OpenAI MISSING rating field")
            
            # Check currency_conversion_rate specifically  
            if 'currency_conversion_rate' in processed_data:
                ccr_value = processed_data.get('currency_conversion_rate')
                print(f"[DEBUG] OpenAI generated currency_conversion_rate: {ccr_value} (type: {type(ccr_value)})")
            else:
                print(f"[DEBUG] ❌ OpenAI MISSING currency_conversion_rate field")
            
            # FORCE restoration of addons field if it was lost or changed during OpenAI processing
            if preserved_addons and len(preserved_addons) > 0:  # Only preserve if addons has actual content
                print(f"[DEBUG] FORCING preservation of original addons: {preserved_addons}")
                
                # Convert preserved addons to icon-mapped format if they're simple strings
                if isinstance(preserved_addons, list):
                    formatted_addons = []
                    for addon in preserved_addons:
                        if isinstance(addon, str):
                            # Convert string to icon-mapped object
                            addon_value = addon.strip()
                            # Use the new intelligent icon mapping
                            icon_class = self._get_appropriate_icon_class(addon_value, 'addon')
                            
                            formatted_addons.append({
                                'value': addon_value,
                                'icon_class': icon_class
                            })
                            print(f"[DEBUG] Converted addon '{addon_value}' to icon format with class '{icon_class}'")
                        elif isinstance(addon, dict) and 'value' in addon:
                            # Already in correct format
                            formatted_addons.append(addon)
                            print(f"[DEBUG] Kept addon in existing format: {addon}")
                    
                    # Override whatever OpenAI returned
                    processed_data['addons'] = formatted_addons
                    print(f"[DEBUG] OVERRODE OpenAI addons with preserved formatted data: {formatted_addons}")
                else:
                    print(f"[DEBUG] Preserved addons is not a list, keeping as is: {preserved_addons}")
                    processed_data['addons'] = preserved_addons
            else:
                print(f"[DEBUG] User provided empty addons array or no addons, allowing OpenAI to generate addons")
            
            # FORCE restoration of inclusions field if it was provided (including empty arrays)
            if 'inclusions' in self.raw_json_data:
                print(f"[DEBUG] FORCING preservation of original inclusions: {preserved_inclusions}")
                if isinstance(preserved_inclusions, list):
                    if len(preserved_inclusions) == 0:
                        # User provided empty array, keep it empty
                        processed_data['inclusions'] = []
                        print(f"[DEBUG] OVERRODE OpenAI inclusions with preserved empty array")
                    else:
                        # User provided content, convert to icon-mapped format
                        formatted_inclusions = []
                        for inclusion in preserved_inclusions:
                            if isinstance(inclusion, str):
                                formatted_inclusions.append({
                                    'value': inclusion.strip(),
                                    'icon_class': self._get_appropriate_icon_class(inclusion.strip(), 'inclusion')
                                })
                                print(f"[DEBUG] Converted inclusion '{inclusion}' to icon format")
                            elif isinstance(inclusion, dict) and 'value' in inclusion:
                                formatted_inclusions.append(inclusion)
                                print(f"[DEBUG] Kept inclusion in existing format: {inclusion}")
                        processed_data['inclusions'] = formatted_inclusions
                        print(f"[DEBUG] OVERRODE OpenAI inclusions with preserved formatted data: {formatted_inclusions}")
                else:
                    processed_data['inclusions'] = preserved_inclusions
                    print(f"[DEBUG] OVERRODE OpenAI inclusions with non-list data: {preserved_inclusions}")
            
            # FORCE restoration of highlights field if it was provided (including empty arrays)
            if 'highlights' in self.raw_json_data:
                print(f"[DEBUG] FORCING preservation of original highlights: {preserved_highlights}")
                if isinstance(preserved_highlights, list):
                    if len(preserved_highlights) == 0:
                        # User provided empty array, keep it empty
                        processed_data['highlights'] = []
                        print(f"[DEBUG] OVERRODE OpenAI highlights with preserved empty array")
                    else:
                        # User provided content, convert to icon-mapped format
                        formatted_highlights = []
                        for highlight in preserved_highlights:
                            if isinstance(highlight, str):
                                formatted_highlights.append({
                                    'value': highlight.strip(),
                                    'icon_class': self._get_appropriate_icon_class(highlight.strip(), 'highlight')
                                })
                                print(f"[DEBUG] Converted highlight '{highlight}' to icon format")
                            elif isinstance(highlight, dict) and 'value' in highlight:
                                formatted_highlights.append(highlight)
                                print(f"[DEBUG] Kept highlight in existing format: {highlight}")
                        processed_data['highlights'] = formatted_highlights
                        print(f"[DEBUG] OVERRODE OpenAI highlights with preserved formatted data: {formatted_highlights}")
                else:
                    processed_data['highlights'] = preserved_highlights
                    print(f"[DEBUG] OVERRODE OpenAI highlights with non-list data: {preserved_highlights}")
            
            # FORCE preservation of visa_type field if it was provided (should remain as simple array, NOT icon format)
            if 'visa_type' in self.raw_json_data:
                print(f"[DEBUG] FORCING preservation of original visa_type: {preserved_visa_type}")
                if isinstance(preserved_visa_type, list):
                    # visa_type should remain as simple array, not be converted to icon format
                    processed_data['visa_type'] = preserved_visa_type
                    print(f"[DEBUG] OVERRODE OpenAI visa_type with preserved simple array: {preserved_visa_type}")
                else:
                    # Keep original non-list data if provided
                    processed_data['visa_type'] = preserved_visa_type
                    print(f"[DEBUG] OVERRODE OpenAI visa_type with non-list data: {preserved_visa_type}")
            
            # FORCE preservation of best_time_to_visit field if it has actual content
            preserved_best_time_to_visit = self.raw_json_data.get('best_time_to_visit', '')
            openai_generated_best_time = processed_data.get('best_time_to_visit', '')
            print(f"[DEBUG] OpenAI generated best_time_to_visit: '{openai_generated_best_time}'")
            print(f"[DEBUG] User provided best_time_to_visit: '{preserved_best_time_to_visit}'")

            if preserved_best_time_to_visit and preserved_best_time_to_visit.strip():
                # User provided meaningful content, preserve it
                processed_data['best_time_to_visit'] = preserved_best_time_to_visit
                print(f"[DEBUG] PRESERVED original best_time_to_visit: '{preserved_best_time_to_visit}'")
            else:
                # User provided empty/blank field OR field was missing - let OpenAI's generated value remain
                if 'best_time_to_visit' in self.raw_json_data and not preserved_best_time_to_visit.strip():
                    print(f"[DEBUG] best_time_to_visit was empty from user, allowing OpenAI generated value to remain: '{openai_generated_best_time}'")
                else:
                    print(f"[DEBUG] best_time_to_visit was missing from user input, allowing OpenAI generated value to remain: '{openai_generated_best_time}'")
                # Don't override processed_data - let OpenAI's generated value stay
            
            if openai_helper.has_errors():
                errors = openai_helper.get_errors()
                print(f"[DEBUG] OpenAI has errors: {errors}")
                logger.error(f"OpenAI processing failed with errors: {errors}")
                self.errors.extend(errors)
                # Just throw the first error directly without cleaning
                raise ValidationError(errors[0] if errors else "OpenAI processing failed")
            
            print(f"[DEBUG] OpenAI processing successful, returning processed data")
            print(f"[DEBUG] Final processed data has addons: {'addons' in processed_data}, value: {processed_data.get('addons')}")
            logger.info("OpenAI processing completed successfully")
            return processed_data
            
        except Exception as e:
            print(f"[DEBUG] Exception in OpenAI processing: {str(e)} (type: {type(e).__name__})")
            logger.error(f"OpenAI processing failed: {str(e)}")
            
            # Just throw the raw error - don't clean it up
            raise e

    def _convert_docx_to_json(self) -> Dict[str, Any]:
        """
        Convert DOCX content to JSON using text extraction and OpenAI
        
        Returns:
            Converted JSON data as dictionary
        """
        try:
            print(f"[DEBUG] _convert_docx_to_json started")
            
            # Extract text from DOCX file
            text_content = self._extract_text_from_docx()
            print(f"[DEBUG] Extracted {len(text_content)} characters from DOCX")
            
            # Create conversion prompt
            prompt = self._create_docx_conversion_prompt(text_content)
            
            # Call OpenAI for conversion
            from packages.utils.openai_file_helper import OpenAIFileHelper
            
            # Create a temporary OpenAI helper for text-to-JSON conversion
            temp_helper = OpenAIFileHelper(None, 'json')
            response = temp_helper._call_openai_api(prompt)
            json_data = temp_helper._extract_json_from_response(response)
            
            print(f"[DEBUG] Successfully converted DOCX to JSON with keys: {list(json_data.keys())}")
            return json_data
            
        except Exception as e:
            print(f"[DEBUG] Failed to convert DOCX to JSON: {str(e)}")
            logger.error(f"DOCX conversion failed: {str(e)}")
            raise ValidationError(f"Failed to convert document to JSON: {str(e)}")
    
    def _extract_text_from_docx(self) -> str:
        """
        Extract text content from DOCX files using python-docx library
        """
        try:
            print(f"[DEBUG] _extract_text_from_docx started")
            print(f"[DEBUG] Raw data type: {type(self.raw_json_data)}")
            print(f"[DEBUG] Raw data length: {len(self.raw_json_data) if self.raw_json_data else 0}")
            
            from docx import Document
            from io import BytesIO
            
            # Ensure we have bytes content for BytesIO
            if isinstance(self.raw_json_data, str):
                # If it's a string, it might be binary data incorrectly decoded
                # Try to encode it back to bytes using latin-1 to preserve binary data
                try:
                    file_content = self.raw_json_data.encode('latin-1')
                    print(f"[DEBUG] Converted string to bytes using latin-1: {len(file_content)} bytes")
                except UnicodeEncodeError as e:
                    print(f"[DEBUG] Failed to convert string to bytes: {e}")
                    raise ValidationError(f"DOCX file content is corrupted: {str(e)}")
            elif isinstance(self.raw_json_data, bytes):
                file_content = self.raw_json_data
                print(f"[DEBUG] Using raw bytes: {len(file_content)} bytes")
            else:
                raise ValidationError(f"Invalid raw data type for DOCX: {type(self.raw_json_data)}. Expected bytes.")
            
            # Validate that we have actual file content
            if not file_content or len(file_content) < 100:  # DOCX files should be at least 100 bytes
                raise ValidationError("DOCX file appears to be empty or too small to be valid")
            
            # Create a BytesIO object from the binary content
            doc_stream = BytesIO(file_content)
            
            try:
                document = Document(doc_stream)
                print(f"[DEBUG] DOCX document loaded successfully")
            except Exception as e:
                print(f"[DEBUG] Failed to load DOCX document: {e}")
                raise ValidationError(f"Failed to read DOCX file. File may be corrupted: {str(e)}")
            
            # Extract text from all paragraphs
            paragraphs_text = []
            for paragraph in document.paragraphs:
                if paragraph.text.strip():
                    paragraphs_text.append(paragraph.text.strip())
            
            full_text = '\n'.join(paragraphs_text)
            
            # Extract text from tables if any
            tables_text = []
            for table in document.tables:
                for row in table.rows:
                    for cell in row.cells:
                        if cell.text.strip():
                            tables_text.append(cell.text.strip())
            
            if tables_text:
                full_text += '\n' + '\n'.join(tables_text)
            
            # Log successful extraction
            print(f"[DEBUG] Successfully extracted {len(full_text)} characters from DOCX file")
            logger.info(f"Successfully extracted {len(full_text)} characters from DOCX file")
            
            if not full_text.strip():
                raise ValidationError("The DOCX file appears to be empty or contains no readable text")
            
            return full_text
            
        except ImportError:
            error_msg = "python-docx library is not available. Please install it: pip install python-docx"
            print(f"[DEBUG] ImportError: {error_msg}")
            raise ValidationError(error_msg)
        except Exception as e:
            error_msg = f"Failed to extract text from DOCX file: {str(e)}"
            print(f"[DEBUG] Exception in text extraction: {error_msg}")
            logger.error(error_msg)
            raise ValidationError(error_msg)
    
    def _create_docx_conversion_prompt(self, text_content: str) -> str:
        """
        Create prompt for converting DOCX text content to JSON
        """
        from packages.utils.package_format_helpers import get_sample_package_format
        sample_format = get_sample_package_format()
        
        return f"""
You are an AI travel package data processor. I will provide you with text content extracted from a DOCX document containing travel package information. Your job is to convert this text into a structured JSON format.

REQUIRED JSON FORMAT:
{sample_format}

DOCUMENT TEXT TO CONVERT:
{text_content}

CONVERSION RULES:
1. Extract all relevant travel package information from the text
2. Map the information to the appropriate JSON fields
3. Preserve exact wording from the document - DO NOT enhance or modify content
4. If a field is not mentioned in the document, set it to appropriate default (empty string, empty array, etc.)
5. For arrays (like inclusions, exclusions, highlights), split text content into individual items
6. For itinerary, create day-by-day breakdown if the information is available
7. Extract pricing information and currency if mentioned
8. Keep addons/add-ons exactly as written in the document

Return ONLY the JSON object, no additional text or explanation.
"""
    
    def _validate_data(self) -> Dict[str, Any]:
        """
        Validate and clean the processed data
        
        Returns:
            Validated and cleaned data
        """
        print(f"[DEBUG] _validate_data started")
        print(f"[DEBUG] Data being validated has addons: {'addons' in self.processed_json_data}, value: {self.processed_json_data.get('addons')}")
        print(f"[DEBUG] Data being validated keys: {list(self.processed_json_data.keys())}")
        logger.info("Starting data validation")
        
        try:
            print(f"[DEBUG] Creating PackageJsonValidationHelper")
            validation_helper = PackageJsonValidationHelper(self.processed_json_data)
            print(f"[DEBUG] ValidationHelper created, calling validate()")
            validation_helper.validate()
            print(f"[DEBUG] Validation completed, calling clean()")
            validated_json_data = validation_helper.clean()
            print(f"[DEBUG] Data cleaning completed, checking for errors")
            
            if validation_helper.has_errors():
                errors = validation_helper.get_errors()
                print(f"[DEBUG] Validation has errors: {errors}")
                logger.error(f"Data validation failed with errors: {errors}")
                self.errors.extend(errors)
                raise ValidationError("Validation failed")
            
            print(f"[DEBUG] Data validation successful")
            logger.info("Data validation completed successfully")
            return validated_json_data
            
        except ValidationError as e:
            print(f"[DEBUG] ValidationError in data validation: {str(e)}")
            logger.error(f"Data validation failed: {str(e)}")
            raise
        except Exception as e:
            print(f"[DEBUG] Unexpected error in data validation: {str(e)} (type: {type(e).__name__})")
            logger.error(f"Unexpected validation error: {str(e)}")
            raise ValidationError(f"Validation error: {str(e)}")
    
    def _create_package(self) -> Package:
        """
        Create the Package instance from validated data
        
        Returns:
            Created Package instance
        """
        print(f"[DEBUG] _create_package started")
        logger.info("Starting package creation from validated data")
        
        try:
            print(f"[DEBUG] Processing destination")
            # Handle destination - get or create following admin pattern
            destination_name = self.validated_json_data.get('destination', '').strip()
            print(f"[DEBUG] Destination name: '{destination_name}'")
            if destination_name:
                destination, created = Destination.objects.get_or_create(
                    title__iexact=destination_name.lower(),  # Case-insensitive lookup
                    partner=self.partner,
                    defaults={
                        'title': destination_name.lower(),  # Always store lowercase
                        'partner': self.partner,
                        'description': f'Destination for {destination_name}',
                        'is_active': True
                    }
                )
                print(f"[DEBUG] Destination {'created' if created else 'found'}: {destination.title} (ID: {destination.id})")
            else:
                # Create a default destination if none provided
                destination, created = Destination.objects.get_or_create(
                    title='unknown destination',
                    partner=self.partner,
                    defaults={
                        'title': 'unknown destination',
                        'partner': self.partner,
                        'description': 'Default destination for packages without specified destination',
                        'is_active': True
                    }
                )
                print(f"[DEBUG] Default destination {'created' if created else 'found'}: {destination.title} (ID: {destination.id})")
            
            print(f"[DEBUG] Creating Package instance")
            # Create the package
            package = Package(
                partner=self.partner,
                title=self.validated_json_data.get('title'),
                package_no=self.validated_json_data.get('package_no'),
                destination=destination,
                owner=self.validated_json_data.get('owner', ''),
                type=PackageTypeChoices.FIXED.value,  # Always set to FIXED for PackageAdmin/PackageUploaderAdmin
                duration=self.validated_json_data.get('duration'),
                duration_in_nights=self.validated_json_data.get('duration_in_nights', 0),
                duration_in_days=self.validated_json_data.get('duration_in_days', 0),
                currency=self.validated_json_data.get('currency', 'INR'),
                price=self.validated_json_data.get('price', 0),
                price_per_person=self.validated_json_data.get('price_per_person', ''),
                visa_type=self.validated_json_data.get('visa_type', []),
                best_time_to_visit_months=self.validated_json_data.get('best_time_to_visit_months', []),
                best_time_to_visit=self.validated_json_data.get('best_time_to_visit', ''),
                rating=self.validated_json_data.get('rating', 0),
                rating_description=self.validated_json_data.get('rating_description', ''),
                currency_conversion_rate=self.validated_json_data.get('currency_conversion_rate', 1.0),
                destination_safety=self.validated_json_data.get('destination_safety', ''),
                about_this_tour=self.validated_json_data.get('about_this_tour', ''),
                # highlights, inclusions, addons will be handled as separate models
                exclusions=self.validated_json_data.get('exclusions', []),
                itinerary=self.validated_json_data.get('itinerary', []),
                hotels=self.validated_json_data.get('hotels', []),
                popular_restaurants=self.validated_json_data.get('popular_restaurants', []),
                popular_activities=self.validated_json_data.get('popular_activities', []),
                cultural_info=self.validated_json_data.get('cultural_info', ''),
                what_to_shop=self.validated_json_data.get('what_to_shop', ''),
                what_to_pack=self.validated_json_data.get('what_to_pack', ''),
                important_notes=self.validated_json_data.get('important_notes', []),
                # addons will be handled as separate models
                is_published=self.validated_json_data.get('is_published', False),
                is_active=self.validated_json_data.get('is_active', True),
                explore_order=self.validated_json_data.get('explore_order', 0)
            )
            
            print(f"[DEBUG] Saving Package to database")
            package.save()
            print(f"[DEBUG] Package saved with ID: {package.id}")
            logger.info(f"Package saved to database with ID: {package.id}")
            
            print(f"[DEBUG] Processing highlights with icon mapping")
            # Handle highlights with icon mapping
            if 'highlights' in self.validated_json_data and isinstance(self.validated_json_data['highlights'], list):
                highlights_count = 0
                for highlight_data in self.validated_json_data['highlights']:
                    value = None
                    icon_class = 'activity'  # Default
                    
                    if isinstance(highlight_data, dict) and 'value' in highlight_data:
                        # New format with icon mapping (proper JSON object)
                        value = highlight_data['value'].strip() if highlight_data['value'] else ''
                        icon_class = highlight_data.get('icon_class', 'activity')
                        print(f"[DEBUG] Processing highlight as dict: '{value}' with icon '{icon_class}'")
                    elif isinstance(highlight_data, str):
                        # Check if it's a JSON string that needs parsing
                        try:
                            # First try to parse as-is (in case it's already proper JSON)
                            parsed_data = json.loads(highlight_data)
                            if isinstance(parsed_data, dict) and 'value' in parsed_data:
                                value = parsed_data['value'].strip() if parsed_data['value'] else ''
                                icon_class = parsed_data.get('icon_class', 'activity')
                                print(f"[DEBUG] Parsed highlight from JSON string: '{value}' with icon '{icon_class}'")
                            else:
                                # Not a JSON object, treat as plain string
                                value = highlight_data.strip()
                                print(f"[DEBUG] Processing highlight as plain string: '{value}'")
                        except json.JSONDecodeError:
                            # Try with Python dict parsing if it looks like a dict
                            try:
                                # Only try dict parsing if the string looks like a Python dict
                                if highlight_data.strip().startswith('{') and highlight_data.strip().endswith('}'):
                                    # This looks like a Python dict literal, try to convert it
                                    parsed_data = ast.literal_eval(highlight_data)
                                    if isinstance(parsed_data, dict) and 'value' in parsed_data:
                                        value = parsed_data['value'].strip() if parsed_data['value'] else ''
                                        icon_class = parsed_data.get('icon_class', 'activity')
                                        print(f"[DEBUG] Parsed highlight from Python dict string: '{value}' with icon '{icon_class}'")
                                    else:
                                        # Not a proper dict, treat as plain string
                                        value = highlight_data.strip()
                                        print(f"[DEBUG] Processing highlight as plain string (invalid dict): '{value}'")
                                else:
                                    # Not JSON-like, treat as plain string
                                    value = highlight_data.strip()
                                    print(f"[DEBUG] Processing highlight as plain string (not JSON): '{value}'")
                            except (ValueError, SyntaxError):
                                # Not valid Python dict either, treat as plain string
                                value = highlight_data.strip()
                                print(f"[DEBUG] Processing highlight as plain string (failed parsing): '{value}'")
                    
                    if value:
                        PackageHighlight.objects.create(
                            package=package,
                            value=value,
                            icon_class=icon_class
                        )
                        highlights_count += 1
                        print(f"[DEBUG] Created highlight: '{value}' with icon '{icon_class}'")
                print(f"[DEBUG] Processed {highlights_count} highlights")
            
            print(f"[DEBUG] Processing inclusions with icon mapping")
            # Handle inclusions with icon mapping
            if 'inclusions' in self.validated_json_data and isinstance(self.validated_json_data['inclusions'], list):
                inclusions_count = 0
                for inclusion_data in self.validated_json_data['inclusions']:
                    value = None
                    icon_class = 'breakfast'  # Default
                    
                    if isinstance(inclusion_data, dict) and 'value' in inclusion_data:
                        # New format with icon mapping (proper JSON object)
                        value = inclusion_data['value'].strip() if inclusion_data['value'] else ''
                        icon_class = inclusion_data.get('icon_class', 'breakfast')
                        print(f"[DEBUG] Processing inclusion as dict: '{value}' with icon '{icon_class}'")
                    elif isinstance(inclusion_data, str):
                        # Check if it's a JSON string that needs parsing
                        try:
                            # First try to parse as-is (in case it's already proper JSON)
                            parsed_data = json.loads(inclusion_data)
                            if isinstance(parsed_data, dict) and 'value' in parsed_data:
                                value = parsed_data['value'].strip() if parsed_data['value'] else ''
                                icon_class = parsed_data.get('icon_class', 'breakfast')
                                print(f"[DEBUG] Parsed inclusion from JSON string: '{value}' with icon '{icon_class}'")
                            else:
                                # Not a JSON object, treat as plain string
                                value = inclusion_data.strip()
                                print(f"[DEBUG] Processing inclusion as plain string: '{value}'")
                        except json.JSONDecodeError:
                            # Try with Python dict parsing if it looks like a dict
                            try:
                                # Only try dict parsing if the string looks like a Python dict
                                if inclusion_data.strip().startswith('{') and inclusion_data.strip().endswith('}'):
                                    # This looks like a Python dict literal, try to convert it
                                    parsed_data = ast.literal_eval(inclusion_data)
                                    if isinstance(parsed_data, dict) and 'value' in parsed_data:
                                        value = parsed_data['value'].strip() if parsed_data['value'] else ''
                                        icon_class = parsed_data.get('icon_class', 'breakfast')
                                        print(f"[DEBUG] Parsed inclusion from Python dict string: '{value}' with icon '{icon_class}'")
                                    else:
                                        # Not a proper dict, treat as plain string
                                        value = inclusion_data.strip()
                                        print(f"[DEBUG] Processing inclusion as plain string (invalid dict): '{value}'")
                                else:
                                    # Not JSON-like, treat as plain string
                                    value = inclusion_data.strip()
                                    print(f"[DEBUG] Processing inclusion as plain string (not JSON): '{value}'")
                            except (ValueError, SyntaxError):
                                # Not valid Python dict either, treat as plain string
                                value = inclusion_data.strip()
                                print(f"[DEBUG] Processing inclusion as plain string (failed parsing): '{value}'")
                    
                    if value:
                        PackageInclusion.objects.create(
                            package=package,
                            value=value,
                            icon_class=icon_class
                        )
                        inclusions_count += 1
                        print(f"[DEBUG] Created inclusion: '{value}' with icon '{icon_class}'")
                print(f"[DEBUG] Processed {inclusions_count} inclusions")
            
            print(f"[DEBUG] Processing addons with icon mapping")
            # Handle addons with icon mapping
            if 'addons' in self.validated_json_data and isinstance(self.validated_json_data['addons'], list):
                addons_count = 0
                for addon_data in self.validated_json_data['addons']:
                    value = None
                    icon_class = 'activity'  # Default
                    
                    if isinstance(addon_data, dict) and 'value' in addon_data:
                        # New format with icon mapping (proper JSON object)
                        value = addon_data['value'].strip() if addon_data['value'] else ''
                        icon_class = addon_data.get('icon_class', 'activity')
                        print(f"[DEBUG] Processing addon as dict: '{value}' with icon '{icon_class}'")
                    elif isinstance(addon_data, str):
                        # Check if it's a JSON string that needs parsing
                        try:
                            # First try to parse as-is (in case it's already proper JSON)
                            parsed_data = json.loads(addon_data)
                            if isinstance(parsed_data, dict) and 'value' in parsed_data:
                                value = parsed_data['value'].strip() if parsed_data['value'] else ''
                                icon_class = parsed_data.get('icon_class', 'activity')
                                print(f"[DEBUG] Parsed addon from JSON string: '{value}' with icon '{icon_class}'")
                            else:
                                # Not a JSON object, treat as plain string
                                value = addon_data.strip()
                                print(f"[DEBUG] Processing addon as plain string: '{value}'")
                        except json.JSONDecodeError:
                            # Try with Python dict parsing if it looks like a dict
                            try:
                                # Only try dict parsing if the string looks like a Python dict
                                if addon_data.strip().startswith('{') and addon_data.strip().endswith('}'):
                                    # This looks like a Python dict literal, try to convert it
                                    parsed_data = ast.literal_eval(addon_data)
                                    if isinstance(parsed_data, dict) and 'value' in parsed_data:
                                        value = parsed_data['value'].strip() if parsed_data['value'] else ''
                                        icon_class = parsed_data.get('icon_class', 'activity')
                                        print(f"[DEBUG] Parsed addon from Python dict string: '{value}' with icon '{icon_class}'")
                                    else:
                                        # Not a proper dict, treat as plain string
                                        value = addon_data.strip()
                                        print(f"[DEBUG] Processing addon as plain string (invalid dict): '{value}'")
                                else:
                                    # Not JSON-like, treat as plain string
                                    value = addon_data.strip()
                                    print(f"[DEBUG] Processing addon as plain string (not JSON): '{value}'")
                            except (ValueError, SyntaxError):
                                # Not valid Python dict either, treat as plain string
                                value = addon_data.strip()
                                print(f"[DEBUG] Processing addon as plain string (failed parsing): '{value}'")
                    
                    if value:
                        PackageAddon.objects.create(
                            package=package,
                            value=value,
                            icon_class=icon_class
                        )
                        addons_count += 1
                        print(f"[DEBUG] Created addon: '{value}' with icon '{icon_class}'")
                print(f"[DEBUG] Processed {addons_count} addons")
            
            print(f"[DEBUG] Clearing and updating categories")
            # Only process categories if they exist in the validated data AND M2M processing is not skipped
            if not self.skip_m2m_processing and 'category' in self.validated_json_data and isinstance(self.validated_json_data['category'], list):
                # Get existing categories for this package
                existing_categories = PackageCategory.objects.filter(package=package)
                existing_category_names = set(
                    existing_categories.values_list('category__title', flat=True)
                )
                
                # Get new categories from validated data
                new_category_names = set(
                    cat_name.strip().lower() 
                    for cat_name in self.validated_json_data['category'] 
                    if cat_name and cat_name.strip()
                )
                
                print(f"[DEBUG] Existing categories: {existing_category_names}")
                print(f"[DEBUG] New categories from form: {new_category_names}")
                
                # Only delete relationships for categories that are being replaced
                categories_to_delete = existing_categories.filter(
                    category__title__in=new_category_names
                )
                deleted_count = categories_to_delete.count()
                print(f"[DEBUG] Found {deleted_count} existing category relationships to replace")
                
                # Hard delete only the relationships being replaced
                for category_rel in categories_to_delete:
                    category_rel.hard_delete()
                print(f"[DEBUG] Hard deleted {deleted_count} category relationships that are being replaced")
                
                categories_count = 0
                for cat_name in self.validated_json_data['category']:
                    if cat_name and cat_name.strip():  # Only process non-empty category names
                        print(f"[DEBUG] Processing category: '{cat_name.strip()}'")
                        # Get or create category following admin pattern
                        category, created = Category.objects.get_or_create(
                            title__iexact=cat_name.strip().lower(),  # Case-insensitive lookup
                            partner=self.partner,
                            defaults={
                                'title': cat_name.strip().lower(),  # Always store lowercase
                                'partner': self.partner,
                                'description': f'Category for {cat_name.strip()}',
                                'is_active': True
                            }
                        )
                        print(f"[DEBUG] Category {'created' if created else 'found'}: {category.title} (ID: {category.id})")
                        # Create the relationship through the through model
                        relationship, rel_created = PackageCategory.objects.get_or_create(
                            package=package,
                            category=category
                        )
                        print(f"[DEBUG] Category relationship {'created' if rel_created else 'already exists'}")
                        categories_count += 1
                print(f"[DEBUG] Processed {categories_count} category relationships")
            elif self.skip_m2m_processing:
                print(f"[DEBUG] Skipping category processing - M2M processing disabled (admin inline handling)")
            else:
                print(f"[DEBUG] No categories in validated data - preserving existing categories")
            
            print(f"[DEBUG] Clearing and updating activities")
            # Only process activities if they exist in the validated data AND M2M processing is not skipped
            if not self.skip_m2m_processing and 'activities' in self.validated_json_data and isinstance(self.validated_json_data['activities'], list):
                # Get existing activities for this package
                existing_activities = PackageActivity.objects.filter(package=package)
                existing_activity_names = set(
                    existing_activities.values_list('activity__title', flat=True)
                )
                
                # Get new activities from validated data
                new_activity_names = set(
                    activity_name.strip().lower() 
                    for activity_name in self.validated_json_data['activities'] 
                    if activity_name and activity_name.strip()
                )
                
                print(f"[DEBUG] Existing activities: {existing_activity_names}")
                print(f"[DEBUG] New activities from form: {new_activity_names}")
                
                # Only delete relationships for activities that are being replaced
                activities_to_delete = existing_activities.filter(
                    activity__title__in=new_activity_names
                )
                deleted_count = activities_to_delete.count()
                print(f"[DEBUG] Found {deleted_count} existing activity relationships to replace")
                
                # Hard delete only the relationships being replaced
                for activity_rel in activities_to_delete:
                    activity_rel.hard_delete()
                print(f"[DEBUG] Hard deleted {deleted_count} activity relationships that are being replaced")
                
                activities_count = 0
                for activity_name in self.validated_json_data['activities']:
                    if activity_name and activity_name.strip():  # Only process non-empty activity names
                        print(f"[DEBUG] Processing activity: '{activity_name.strip()}'")
                        # Get or create activity following admin pattern
                        activity, created = Activity.objects.get_or_create(
                            title__iexact=activity_name.strip().lower(),  # Case-insensitive lookup
                            partner=self.partner,
                            defaults={
                                'title': activity_name.strip().lower(),  # Always store lowercase
                                'partner': self.partner,
                                'description': f'Activity for {activity_name.strip()}',
                                'is_active': True
                            }
                        )
                        print(f"[DEBUG] Activity {'created' if created else 'found'}: {activity.title} (ID: {activity.id})")
                        # Create the relationship through the through model
                        relationship, rel_created = PackageActivity.objects.get_or_create(
                            package=package,
                            activity=activity
                        )
                        print(f"[DEBUG] Activity relationship {'created' if rel_created else 'already exists'}")
                        activities_count += 1
                print(f"[DEBUG] Added {activities_count} activity relationships")
            elif self.skip_m2m_processing:
                print(f"[DEBUG] Skipping activity processing - M2M processing disabled (admin inline handling)")
            else:
                print(f"[DEBUG] No activities in validated data - preserving existing activities")
            
            print(f"[DEBUG] Package creation completed successfully")
            logger.info(f"Package created successfully: {package.title} ({package.package_no})")
            return package
            
        except Exception as e:
            print(f"[DEBUG] Exception in package creation: {str(e)} (type: {type(e).__name__})")
            logger.error(f"Package creation failed: {str(e)}")
            raise ValidationError(f"Failed to create package: {str(e)}")

    def update_package(self, package):
        print(f"[DEBUG] update_package started for package ID: {package.id}, title: '{package.title}'")
        logger.info(f"Starting package update for package ID: {package.id}")
        
        try:
            print(f"[DEBUG] Processing destination for update")
            # Handle destination - get or create following admin pattern
            destination_name = self.validated_json_data.get('destination', '').strip()
            print(f"[DEBUG] Destination name: '{destination_name}'")
            if destination_name:
                destination, created = Destination.objects.get_or_create(
                    title__iexact=destination_name.lower(),  # Case-insensitive lookup
                    partner=self.partner,
                    defaults={
                        'title': destination_name.lower(),  # Always store lowercase
                        'partner': self.partner,
                        'description': f'Destination for {destination_name}',
                        'is_active': True
                    }
                )
                print(f"[DEBUG] Destination {'created' if created else 'found'}: {destination.title} (ID: {destination.id})")
            else:
                # Keep existing destination if none provided
                destination = package.destination
                print(f"[DEBUG] Keeping existing destination: {destination.title if destination else 'None'}")
            
            print(f"[DEBUG] Updating package fields")
            # Update the package
            package.title = self.validated_json_data.get('title')
            package.package_no = self.validated_json_data.get('package_no')
            package.destination = destination
            package.owner = self.validated_json_data.get('owner', '')
            package.type = PackageTypeChoices.FIXED.value  # Always set to FIXED for PackageAdmin/PackageUploaderAdmin
            package.duration = self.validated_json_data.get('duration')
            package.duration_in_nights = self.validated_json_data.get('duration_in_nights', 0)
            package.duration_in_days = self.validated_json_data.get('duration_in_days', 0)
            package.currency = self.validated_json_data.get('currency', 'INR')
            package.price = self.validated_json_data.get('price', 0)
            package.price_per_person = self.validated_json_data.get('price_per_person', '')
            package.visa_type = self.validated_json_data.get('visa_type', [])
            package.best_time_to_visit_months = self.validated_json_data.get('best_time_to_visit_months', [])
            package.best_time_to_visit = self.validated_json_data.get('best_time_to_visit', '')
            package.rating = self.validated_json_data.get('rating', 0)
            package.rating_description = self.validated_json_data.get('rating_description', '')
            package.currency_conversion_rate = self.validated_json_data.get('currency_conversion_rate', 1.0)
            package.destination_safety = self.validated_json_data.get('destination_safety', '')
            package.about_this_tour = self.validated_json_data.get('about_this_tour', '')
            # highlights, inclusions, addons will be handled as separate models
            package.exclusions = self.validated_json_data.get('exclusions', [])
            package.itinerary = self.validated_json_data.get('itinerary', [])
            package.hotels = self.validated_json_data.get('hotels', [])
            package.popular_restaurants = self.validated_json_data.get('popular_restaurants', [])
            package.popular_activities = self.validated_json_data.get('popular_activities', [])
            package.cultural_info = self.validated_json_data.get('cultural_info', '')
            package.what_to_shop = self.validated_json_data.get('what_to_shop', '')
            package.what_to_pack = self.validated_json_data.get('what_to_pack', '')
            package.important_notes = self.validated_json_data.get('important_notes', [])
            # addons will be handled as separate models
            package.is_published = self.validated_json_data.get('is_published', False)
            package.is_active = self.validated_json_data.get('is_active', True)
            package.explore_order = self.validated_json_data.get('explore_order', 0)

            print(f"[DEBUG] Saving updated package to database")
            package.save()
            print(f"[DEBUG] Package saved successfully")
            logger.info(f"Package fields updated and saved for ID: {package.id}")

            print(f"[DEBUG] Clearing and updating highlights with icon mapping")
            # Clear and re-add highlights with icon mapping - USE HARD DELETE
            existing_highlights = PackageHighlight.objects.filter(package=package)
            deleted_count = existing_highlights.count()
            print(f"[DEBUG] Found {deleted_count} existing highlights to hard delete")
            
            # Iterate and hard delete each object
            for highlight in existing_highlights:
                highlight.hard_delete()
            print(f"[DEBUG] Hard deleted {deleted_count} existing highlights")
            
            if 'highlights' in self.validated_json_data and isinstance(self.validated_json_data['highlights'], list):
                highlights_count = 0
                for highlight_data in self.validated_json_data['highlights']:
                    value = None
                    icon_class = 'activity'  # Default
                    
                    if isinstance(highlight_data, dict) and 'value' in highlight_data:
                        # New format with icon mapping (proper JSON object)
                        value = highlight_data['value'].strip() if highlight_data['value'] else ''
                        icon_class = highlight_data.get('icon_class', 'activity')
                        print(f"[DEBUG] Processing highlight as dict: '{value}' with icon '{icon_class}'")
                    elif isinstance(highlight_data, str):
                        # Check if it's a JSON string that needs parsing
                        try:
                            # First try to parse as-is (in case it's already proper JSON)
                            parsed_data = json.loads(highlight_data)
                            if isinstance(parsed_data, dict) and 'value' in parsed_data:
                                value = parsed_data['value'].strip() if parsed_data['value'] else ''
                                icon_class = parsed_data.get('icon_class', 'activity')
                                print(f"[DEBUG] Parsed highlight from JSON string: '{value}' with icon '{icon_class}'")
                            else:
                                # Not a JSON object, treat as plain string
                                value = highlight_data.strip()
                                print(f"[DEBUG] Processing highlight as plain string: '{value}'")
                        except json.JSONDecodeError:
                            # Try with Python dict parsing if it looks like a dict
                            try:
                                # Only try dict parsing if the string looks like a Python dict
                                if highlight_data.strip().startswith('{') and highlight_data.strip().endswith('}'):
                                    # This looks like a Python dict literal, try to convert it
                                    parsed_data = ast.literal_eval(highlight_data)
                                    if isinstance(parsed_data, dict) and 'value' in parsed_data:
                                        value = parsed_data['value'].strip() if parsed_data['value'] else ''
                                        icon_class = parsed_data.get('icon_class', 'activity')
                                        print(f"[DEBUG] Parsed highlight from Python dict string: '{value}' with icon '{icon_class}'")
                                    else:
                                        # Not a proper dict, treat as plain string
                                        value = highlight_data.strip()
                                        print(f"[DEBUG] Processing highlight as plain string (invalid dict): '{value}'")
                                else:
                                    # Not JSON-like, treat as plain string
                                    value = highlight_data.strip()
                                    print(f"[DEBUG] Processing highlight as plain string (not JSON): '{value}'")
                            except (ValueError, SyntaxError):
                                # Not valid Python dict either, treat as plain string
                                value = highlight_data.strip()
                                print(f"[DEBUG] Processing highlight as plain string (failed parsing): '{value}'")
                    
                    if value:
                        PackageHighlight.objects.create(
                            package=package,
                            value=value,
                            icon_class=icon_class
                        )
                        highlights_count += 1
                        print(f"[DEBUG] Created highlight: '{value}' with icon '{icon_class}'")
                print(f"[DEBUG] Processed {highlights_count} highlights")
            
            print(f"[DEBUG] Clearing and updating inclusions with icon mapping")
            # Clear and re-add inclusions with icon mapping - USE HARD DELETE
            existing_inclusions = PackageInclusion.objects.filter(package=package)
            deleted_count = existing_inclusions.count()
            print(f"[DEBUG] Found {deleted_count} existing inclusions to hard delete")
            
            # Iterate and hard delete each object
            for inclusion in existing_inclusions:
                inclusion.hard_delete()
            print(f"[DEBUG] Hard deleted {deleted_count} existing inclusions")
            
            if 'inclusions' in self.validated_json_data and isinstance(self.validated_json_data['inclusions'], list):
                inclusions_count = 0
                for inclusion_data in self.validated_json_data['inclusions']:
                    value = None
                    icon_class = 'breakfast'  # Default
                    
                    if isinstance(inclusion_data, dict) and 'value' in inclusion_data:
                        # New format with icon mapping (proper JSON object)
                        value = inclusion_data['value'].strip() if inclusion_data['value'] else ''
                        icon_class = inclusion_data.get('icon_class', 'breakfast')
                        print(f"[DEBUG] Processing inclusion as dict: '{value}' with icon '{icon_class}'")
                    elif isinstance(inclusion_data, str):
                        # Check if it's a JSON string that needs parsing
                        try:
                            # First try to parse as-is (in case it's already proper JSON)
                            parsed_data = json.loads(inclusion_data)
                            if isinstance(parsed_data, dict) and 'value' in parsed_data:
                                value = parsed_data['value'].strip() if parsed_data['value'] else ''
                                icon_class = parsed_data.get('icon_class', 'breakfast')
                                print(f"[DEBUG] Parsed inclusion from JSON string: '{value}' with icon '{icon_class}'")
                            else:
                                # Not a JSON object, treat as plain string
                                value = inclusion_data.strip()
                                print(f"[DEBUG] Processing inclusion as plain string: '{value}'")
                        except json.JSONDecodeError:
                            # Try with Python dict parsing if it looks like a dict
                            try:
                                # Only try dict parsing if the string looks like a Python dict
                                if inclusion_data.strip().startswith('{') and inclusion_data.strip().endswith('}'):
                                    # This looks like a Python dict literal, try to convert it
                                    parsed_data = ast.literal_eval(inclusion_data)
                                    if isinstance(parsed_data, dict) and 'value' in parsed_data:
                                        value = parsed_data['value'].strip() if parsed_data['value'] else ''
                                        icon_class = parsed_data.get('icon_class', 'breakfast')
                                        print(f"[DEBUG] Parsed inclusion from Python dict string: '{value}' with icon '{icon_class}'")
                                    else:
                                        # Not a proper dict, treat as plain string
                                        value = inclusion_data.strip()
                                        print(f"[DEBUG] Processing inclusion as plain string (invalid dict): '{value}'")
                                else:
                                    # Not JSON-like, treat as plain string
                                    value = inclusion_data.strip()
                                    print(f"[DEBUG] Processing inclusion as plain string (not JSON): '{value}'")
                            except (ValueError, SyntaxError):
                                # Not valid Python dict either, treat as plain string
                                value = inclusion_data.strip()
                                print(f"[DEBUG] Processing inclusion as plain string (failed parsing): '{value}'")
                    
                    if value:
                        PackageInclusion.objects.create(
                            package=package,
                            value=value,
                            icon_class=icon_class
                        )
                        inclusions_count += 1
                        print(f"[DEBUG] Created inclusion: '{value}' with icon '{icon_class}'")
                print(f"[DEBUG] Processed {inclusions_count} inclusions")
            
            print(f"[DEBUG] Clearing and updating addons with icon mapping")
            # Clear and re-add addons with icon mapping - USE HARD DELETE
            existing_addons = PackageAddon.objects.filter(package=package)
            deleted_count = existing_addons.count()
            print(f"[DEBUG] Found {deleted_count} existing addons to hard delete")
            
            # Iterate and hard delete each object
            for addon in existing_addons:
                addon.hard_delete()
            print(f"[DEBUG] Hard deleted {deleted_count} existing addons")
            
            if 'addons' in self.validated_json_data and isinstance(self.validated_json_data['addons'], list):
                addons_count = 0
                for addon_data in self.validated_json_data['addons']:
                    value = None
                    icon_class = 'activity'  # Default
                    
                    if isinstance(addon_data, dict) and 'value' in addon_data:
                        # New format with icon mapping (proper JSON object)
                        value = addon_data['value'].strip() if addon_data['value'] else ''
                        icon_class = addon_data.get('icon_class', 'activity')
                        print(f"[DEBUG] Processing addon as dict: '{value}' with icon '{icon_class}'")
                    elif isinstance(addon_data, str):
                        # Check if it's a JSON string that needs parsing
                        try:
                            # First try to parse as-is (in case it's already proper JSON)
                            parsed_data = json.loads(addon_data)
                            if isinstance(parsed_data, dict) and 'value' in parsed_data:
                                value = parsed_data['value'].strip() if parsed_data['value'] else ''
                                icon_class = parsed_data.get('icon_class', 'activity')
                                print(f"[DEBUG] Parsed addon from JSON string: '{value}' with icon '{icon_class}'")
                            else:
                                # Not a JSON object, treat as plain string
                                value = addon_data.strip()
                                print(f"[DEBUG] Processing addon as plain string: '{value}'")
                        except json.JSONDecodeError:
                            # Try with Python dict parsing if it looks like a dict
                            try:
                                # Only try dict parsing if the string looks like a Python dict
                                if addon_data.strip().startswith('{') and addon_data.strip().endswith('}'):
                                    # This looks like a Python dict literal, try to convert it
                                    parsed_data = ast.literal_eval(addon_data)
                                    if isinstance(parsed_data, dict) and 'value' in parsed_data:
                                        value = parsed_data['value'].strip() if parsed_data['value'] else ''
                                        icon_class = parsed_data.get('icon_class', 'activity')
                                        print(f"[DEBUG] Parsed addon from Python dict string: '{value}' with icon '{icon_class}'")
                                    else:
                                        # Not a proper dict, treat as plain string
                                        value = addon_data.strip()
                                        print(f"[DEBUG] Processing addon as plain string (invalid dict): '{value}'")
                                else:
                                    # Not JSON-like, treat as plain string
                                    value = addon_data.strip()
                                    print(f"[DEBUG] Processing addon as plain string (not JSON): '{value}'")
                            except (ValueError, SyntaxError):
                                # Not valid Python dict either, treat as plain string
                                value = addon_data.strip()
                                print(f"[DEBUG] Processing addon as plain string (failed parsing): '{value}'")
                    
                    if value:
                        PackageAddon.objects.create(
                            package=package,
                            value=value,
                            icon_class=icon_class
                        )
                        addons_count += 1
                        print(f"[DEBUG] Created addon: '{value}' with icon '{icon_class}'")
                print(f"[DEBUG] Processed {addons_count} addons")

            print(f"[DEBUG] Clearing and updating categories")
            # Only process categories if they exist in the validated data AND M2M processing is not skipped
            if not self.skip_m2m_processing and 'category' in self.validated_json_data and isinstance(self.validated_json_data['category'], list):
                # Get existing categories for this package
                existing_categories = PackageCategory.objects.filter(package=package)
                existing_category_names = set(
                    existing_categories.values_list('category__title', flat=True)
                )
                
                # Get new categories from validated data
                new_category_names = set(
                    cat_name.strip().lower() 
                    for cat_name in self.validated_json_data['category'] 
                    if cat_name and cat_name.strip()
                )
                
                print(f"[DEBUG] Existing categories: {existing_category_names}")
                print(f"[DEBUG] New categories from form: {new_category_names}")
                
                # Only delete relationships for categories that are being replaced
                categories_to_delete = existing_categories.filter(
                    category__title__in=new_category_names
                )
                deleted_count = categories_to_delete.count()
                print(f"[DEBUG] Found {deleted_count} existing category relationships to replace")
                
                # Hard delete only the relationships being replaced
                for category_rel in categories_to_delete:
                    category_rel.hard_delete()
                print(f"[DEBUG] Hard deleted {deleted_count} category relationships that are being replaced")
                
                categories_count = 0
                for cat_name in self.validated_json_data['category']:
                    if cat_name and cat_name.strip():  # Only process non-empty category names
                        print(f"[DEBUG] Processing category: '{cat_name.strip()}'")
                        # Get or create category following admin pattern
                        category, created = Category.objects.get_or_create(
                            title__iexact=cat_name.strip().lower(),  # Case-insensitive lookup
                            partner=self.partner,
                            defaults={
                                'title': cat_name.strip().lower(),  # Always store lowercase
                                'partner': self.partner,
                                'description': f'Category for {cat_name.strip()}',
                                'is_active': True
                            }
                        )
                        print(f"[DEBUG] Category {'created' if created else 'found'}: {category.title} (ID: {category.id})")
                        # Create the relationship through the through model
                        relationship, rel_created = PackageCategory.objects.get_or_create(
                            package=package,
                            category=category
                        )
                        print(f"[DEBUG] Category relationship {'created' if rel_created else 'already exists'}")
                        categories_count += 1
                print(f"[DEBUG] Processed {categories_count} category relationships")
            elif self.skip_m2m_processing:
                print(f"[DEBUG] Skipping category processing - M2M processing disabled (admin inline handling)")
            else:
                print(f"[DEBUG] No categories in validated data - preserving existing categories")
            
            print(f"[DEBUG] Clearing and updating activities")
            # Only process activities if they exist in the validated data AND M2M processing is not skipped
            if not self.skip_m2m_processing and 'activities' in self.validated_json_data and isinstance(self.validated_json_data['activities'], list):
                # Get existing activities for this package
                existing_activities = PackageActivity.objects.filter(package=package)
                existing_activity_names = set(
                    existing_activities.values_list('activity__title', flat=True)
                )
                
                # Get new activities from validated data
                new_activity_names = set(
                    activity_name.strip().lower() 
                    for activity_name in self.validated_json_data['activities'] 
                    if activity_name and activity_name.strip()
                )
                
                print(f"[DEBUG] Existing activities: {existing_activity_names}")
                print(f"[DEBUG] New activities from form: {new_activity_names}")
                
                # Only delete relationships for activities that are being replaced
                activities_to_delete = existing_activities.filter(
                    activity__title__in=new_activity_names
                )
                deleted_count = activities_to_delete.count()
                print(f"[DEBUG] Found {deleted_count} existing activity relationships to replace")
                
                # Hard delete only the relationships being replaced
                for activity_rel in activities_to_delete:
                    activity_rel.hard_delete()
                print(f"[DEBUG] Hard deleted {deleted_count} activity relationships that are being replaced")
                
                activities_count = 0
                for activity_name in self.validated_json_data['activities']:
                    if activity_name and activity_name.strip():  # Only process non-empty activity names
                        print(f"[DEBUG] Processing activity: '{activity_name.strip()}'")
                        # Get or create activity following admin pattern
                        activity, created = Activity.objects.get_or_create(
                            title__iexact=activity_name.strip().lower(),  # Case-insensitive lookup
                            partner=self.partner,
                            defaults={
                                'title': activity_name.strip().lower(),  # Always store lowercase
                                'partner': self.partner,
                                'description': f'Activity for {activity_name.strip()}',
                                'is_active': True
                            }
                        )
                        print(f"[DEBUG] Activity {'created' if created else 'found'}: {activity.title} (ID: {activity.id})")
                        # Create the relationship through the through model
                        relationship, rel_created = PackageActivity.objects.get_or_create(
                            package=package,
                            activity=activity
                        )
                        print(f"[DEBUG] Activity relationship {'created' if rel_created else 'already exists'}")
                        activities_count += 1
                print(f"[DEBUG] Added {activities_count} activity relationships")
            elif self.skip_m2m_processing:
                print(f"[DEBUG] Skipping activity processing - M2M processing disabled (admin inline handling)")
            else:
                print(f"[DEBUG] No activities in validated data - preserving existing activities")
            
            print(f"[DEBUG] Package update completed successfully")
            logger.info(f"Package updated successfully: {package.title} ({package.package_no})")
            return package
            
        except Exception as e:
            print(f"[DEBUG] Exception in package update: {str(e)} (type: {type(e).__name__})")
            logger.error(f"Package update failed: {str(e)}")
            raise ValidationError(f"Failed to update package: {str(e)}")

    def get_errors(self) -> list:
        """Get any errors that occurred during processing"""
        return self.errors
    
    def has_errors(self) -> bool:
        """Check if any errors occurred"""
        return len(self.errors) > 0
    
    def get_processing_summary(self) -> Dict[str, Any]:
        """
        Get a summary of the processing steps
        
        Returns:
            Dictionary with processing information
        """
        return {
            'source_type': self.source_type,
            'partner': self.partner.name if self.partner else None,
            'has_raw_data': self.raw_json_data is not None,
            'has_processed_data': self.processed_json_data is not None,
            'has_validated_data': self.validated_json_data is not None,
            'package_created': self.created_package is not None,
            'package_id': self.created_package.id if self.created_package else None,
            'errors': self.errors,
            'success': self.created_package is not None and not self.has_errors()
        }

    def _get_appropriate_icon_class(self, value, field_type):
        """
        Get appropriate icon class based on content and field type
        
        Args:
            value: The text content to analyze
            field_type: 'highlight', 'inclusion', or 'addon'
            
        Returns:
            str: Appropriate icon class
        """
        if not value:
            return 'activity'  # Default fallback
            
        value_lower = value.lower()
        
        # Transportation related
        if any(word in value_lower for word in ['flight', 'air', 'airplane', 'airport']):
            return 'flight'
        elif any(word in value_lower for word in ['taxi', 'car', 'private transport', 'transfer', 'pickup', 'drop']):
            return 'taxi'
        elif any(word in value_lower for word in ['bus', 'coach', 'group transport', 'shuttle']):
            return 'bus'
        elif any(word in value_lower for word in ['ship', 'boat', 'cruise', 'ferry', 'sailing']):
            return 'ship'
        
        # Accommodation related
        elif any(word in value_lower for word in ['hotel', 'accommodation', 'room', 'stay', 'lodge', 'resort']):
            return 'hotel'
        
        # Food and meals
        elif any(word in value_lower for word in ['breakfast', 'lunch', 'dinner', 'meal', 'food', 'dining', 'restaurant', 'bbq']):
            return 'breakfast'
        
        # Documentation and services
        elif any(word in value_lower for word in ['visa', 'document', 'passport', 'permit', 'paperwork']):
            return 'visa'
        elif any(word in value_lower for word in ['insurance', 'protection', 'coverage', 'safety', 'medical']):
            return 'insurance'
        elif any(word in value_lower for word in ['forex', 'currency', 'money', 'exchange', 'payment']):
            return 'forex_card'
        
        # Beach and water activities
        elif any(word in value_lower for word in ['beach', 'swimming', 'swim', 'coastal', 'seaside', 'ocean']):
            return 'beach'
        elif any(word in value_lower for word in ['island', 'archipelago', 'tropical']):
            return 'island'
        
        # Scenic and viewpoints
        elif any(word in value_lower for word in ['sunset', 'sunrise', 'view', 'panoramic', 'scenic', 'observation', 'deck', 'viewpoint']):
            return 'sunrise'
        
        # Group activities and tours
        elif any(word in value_lower for word in ['group', 'passenger', 'capacity', 'people', 'tour group']):
            return 'passenger'
        
        # Field-specific defaults
        elif field_type == 'inclusion':
            # For inclusions, if we can't determine the type, default to breakfast (meals/services)
            return 'breakfast'
        elif field_type == 'addon':
            # For addons, default to activity since they're usually services/activities
            return 'activity'
        else:
            # For highlights, default to activity since they're usually activities/experiences
            return 'activity'

    def _extract_months_from_text(self, text):
        """
        Extract month names from best_time_to_visit text
        
        Args:
            text: Text containing month information
            
        Returns:
            list: List of month names found in the text
        """
        if not text:
            return []
            
        text_lower = text.lower()
        
        # Month mappings - both full names and abbreviations
        month_mappings = {
            'january': 'January', 'jan': 'January',
            'february': 'February', 'feb': 'February',
            'march': 'March', 'mar': 'March',
            'april': 'April', 'apr': 'April',
            'may': 'May',
            'june': 'June', 'jun': 'June',
            'july': 'July', 'jul': 'July',
            'august': 'August', 'aug': 'August',
            'september': 'September', 'sep': 'September', 'sept': 'September',
            'october': 'October', 'oct': 'October',
            'november': 'November', 'nov': 'November',
            'december': 'December', 'dec': 'December'
        }
        
        # Season mappings
        season_mappings = {
            'winter': ['December', 'January', 'February'],
            'spring': ['March', 'April', 'May'],
            'summer': ['June', 'July', 'August'],
            'autumn': ['September', 'October', 'November'],
            'fall': ['September', 'October', 'November'],
            'monsoon': ['June', 'July', 'August', 'September'],
            'post-monsoon': ['October', 'November']
        }
        
        extracted_months = set()
        
        # Look for individual months
        for month_key, month_name in month_mappings.items():
            if month_key in text_lower:
                extracted_months.add(month_name)
        
        # Look for seasons
        for season_key, season_months in season_mappings.items():
            if season_key in text_lower:
                extracted_months.update(season_months)
        
        # Look for month ranges like "October to March"
        import re
        range_pattern = r'(\w+)\s+to\s+(\w+)'
        matches = re.findall(range_pattern, text_lower)
        for start_month, end_month in matches:
            start_found = month_mappings.get(start_month)
            end_found = month_mappings.get(end_month)
            if start_found and end_found:
                # Get month numbers for range calculation
                month_order = ['January', 'February', 'March', 'April', 'May', 'June',
                              'July', 'August', 'September', 'October', 'November', 'December']
                try:
                    start_idx = month_order.index(start_found)
                    end_idx = month_order.index(end_found)
                    
                    # Handle year wrap-around (e.g., October to March)
                    if start_idx <= end_idx:
                        # Same year range
                        for i in range(start_idx, end_idx + 1):
                            extracted_months.add(month_order[i])
                    else:
                        # Year wrap-around
                        for i in range(start_idx, len(month_order)):
                            extracted_months.add(month_order[i])
                        for i in range(0, end_idx + 1):
                            extracted_months.add(month_order[i])
                except ValueError:
                    pass
        
        return sorted(list(extracted_months))
