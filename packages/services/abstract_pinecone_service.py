"""
Abstract base class for Pinecone service.

This class is used to manage the Pinecone index.
It provides a generic interface for upsert and delete operations.
"""
import logging
from abc import ABC, abstractmethod
from typing import Dict, Optional, TypeVar, Generic

from langchain_core.documents import Document
from langchain_openai import OpenAIEmbeddings
from langchain_pinecone import PineconeVectorStore
from pinecone import Pinecone, ServerlessSpec

from zuumm import settings

# Define a generic type for the data model
T = TypeVar("T")

# Default values for the service
PINECONE_EMBEDDING_MODEL = "text-embedding-3-small"
PINECONE_VECTOR_DIMENSION = 1536
SERVERLESS_SPEC = ServerlessSpec(cloud="aws", region="us-east-1")


class AbstractPineconeService(ABC, Generic[T]):
    """
    An abstract base service for interacting with Pinecone, handling the core
    initialization and providing a generic, multi-tenant interface for
    upsert using namespaces.
    """

    def __init__(
        self,
        name: str,
        index_name: str,
        embedding_model: Optional[str] = None,
        vector_dimension: Optional[int] = None,
    ):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.INFO)

        if not all([settings.OPENAI_API_KEY, settings.PINECONE_API_KEY]):
            raise ValueError("Required Pinecone and OpenAI API keys must be set.")

        self.index_name = index_name
        self.embedding_model = embedding_model or PINECONE_EMBEDDING_MODEL
        self.vector_dimension = vector_dimension or PINECONE_VECTOR_DIMENSION

        self.logger.info("Initializing Pinecone client for index: %s", self.index_name)
        self.pinecone = Pinecone(api_key=settings.PINECONE_API_KEY)

        self.logger.info(
            "Initializing OpenAI Embeddings model: %s", self.embedding_model
        )
        self.embeddings = OpenAIEmbeddings(
            model=self.embedding_model, api_key=settings.OPENAI_API_KEY
        )

        self._create_index_if_not_exists()

        self.vector_store = PineconeVectorStore.from_existing_index(
            self.index_name, self.embeddings
        )

        self.logger.info(
            "Pinecone Service for index '%s' initialized successfully.", self.index_name
        )

    def _create_index_if_not_exists(self):
        """Creates the Pinecone index if it doesn't already exist."""
        if self.index_name not in self.pinecone.list_indexes().names():
            self.logger.info("Index '%s' not found. Creating...", self.index_name)
            try:
                self.pinecone.create_index(
                    name=self.index_name,
                    dimension=self.vector_dimension,
                    metric="cosine",
                    spec=SERVERLESS_SPEC,
                )
                self.logger.info(
                    "Index '%s' created successfully with dimension %d.",
                    self.index_name,
                    self.vector_dimension,
                )
            except Exception as e:
                self.logger.error(
                    "Failed to create index '%s': %s",
                    self.index_name,
                    e,
                    exc_info=True,
                )
                raise

    @abstractmethod
    def _prepare_text_for_embedding(self, data: T) -> str:
        pass

    @abstractmethod
    def _extract_metadata(self, data: T) -> Dict:
        pass

    def upsert(self, data: T, namespace: str):
        """Upserts a single item to a specific namespace in the Pinecone index."""
        if not namespace:
            raise ValueError("Namespace must be provided for upsert operations.")

        item_id = str(data.id)
        text_to_embed = self._prepare_text_for_embedding(data)
        metadata = self._extract_metadata(data)
        metadata["id"] = item_id

        doc = Document(page_content=text_to_embed, metadata=metadata)

        self.vector_store.add_documents([doc], ids=[item_id], namespace=namespace)

        self.logger.info(
            "Successfully upserted item with ID: %s to namespace: %s",
            item_id,
            namespace,
        )

        return {"status": "success", "id": item_id, "namespace": namespace}

    def delete(self, item_id: str, namespace: str):
        """Deletes a single item from a specific namespace in the Pinecone index."""
        if not namespace:
            raise ValueError("Namespace must be provided for delete operations.")

        self.vector_store.delete(ids=[item_id], namespace=namespace)

        self.logger.info(
            "Successfully deleted item with ID: %s from namespace: %s",
            item_id,
            namespace,
        )

        return {"status": "success", "id": item_id, "namespace": namespace}
