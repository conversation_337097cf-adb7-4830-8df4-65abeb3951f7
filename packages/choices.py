from django.db.models import TextChoices


class PackageTypeChoices(TextChoices):
    FIXED = "Fixed"
    CUSTOM_ADMIN = "Custom Admin"
    CUSTOM_AI = "Custom AI"

    @classmethod
    def custom_package_choices(cls):
        return [cls.CUSTOM_ADMIN.value, cls.CUSTOM_AI.value]


class ItineraryDayItemType(TextChoices):
    ACTIVITY = "Activity"
    HOTEL = "Hotel"
    FLIGHT = "Flight"
    STAY = "Stay"
    MEAL = "Meal"
    TRANSPORTATION = "Transportation"
    OTHER = "Other"


class PackageMediaTypes(TextChoices):
    IMAGE = "image"
    VIDEO = "video"


class PersonaChoices(TextChoices):
    """Choices for persona field in CustomActivity"""
    COUPLE = "Couple", "Couple"
    SOLO = "Solo", "Solo"
    FAMILY = "Family", "Family"
    FRIENDS = "Friends", "Friends"


class MonthChoices(TextChoices):
    """Choices for months of the year"""
    # JANUARY = "1", "January"
    # FEBRUARY = "2", "February"
    # MARCH = "3", "March"
    # APRIL = "4", "April"
    # MAY = "5", "May"
    # JUNE = "6", "June"
    # JULY = "7", "July"
    # AUGUST = "8", "August"
    # SEPTEMBER = "9", "September"
    # OCTOBER = "10", "October"
    # NOVEMBER = "11", "November"
    # DECEMBER = "12", "December"

    JANUARY = "1", "Jan"
    FEBRUARY = "2", "Feb"
    MARCH = "3", "Mar"
    APRIL = "4", "Apr"
    MAY = "5", "May"
    JUNE = "6", "Jun"
    JULY = "7", "Jul"
    AUGUST = "8", "Aug"
    SEPTEMBER = "9", "Sep"
    OCTOBER = "10", "Oct"
    NOVEMBER = "11", "Nov"
    DECEMBER = "12", "Dec"
    
    @classmethod
    def get_month_list(cls):
        """Get list of all month values"""
        return [choice[0] for choice in cls.choices]
