from django.db import models
from django.core.validators import FileExtensionValidator
from django.core.exceptions import ValidationError
from django_better_admin_arrayfield.models.fields import ArrayField
from django.conf import settings
from ckeditor.fields import RichTextField
from django.contrib.gis.db import models as gis_models
from django.contrib.postgres.indexes import OpClass, GinIndex, GistIndex
from django.contrib.postgres.operations import TrigramExtension
from django.db.models.functions import Lower

from base.models import BaseModel
from base.storage_utils import (
    package_upload_path, 
    package_media_upload_path, 
    category_upload_path,
    destination_upload_path, 
    activity_upload_path,
    custom_activity_media_upload_path,
)
from base.static import Constants
from accounts.models import Partner
from packages.choices import PackageMediaTypes, PackageTypeChoices, PersonaChoices, ItineraryDayItemType
from packages.choices import MonthChoices
from dynamic_packages.models import Hotel

operations = [
    TrigramExtension(),
]

class Category(BaseModel):
    """
    Category model for package categorization (HONEYMOON, FAMILY, RELIGIOUS, etc.)
    """
    partner = models.ForeignKey(Partner, on_delete=models.CASCADE, related_name='categories')
    title = models.CharField(max_length=100)
    description = models.TextField(help_text="Description is required")
    explore_order = models.PositiveIntegerField(default=0)

    def save(self, *args, **kwargs):
        """Override save to ensure title is lowercase"""
        if self.title:
            self.title = self.title.lower().strip()
        super().save(*args, **kwargs)

    def __str__(self):
        return self.title
    
    class Meta:
        verbose_name = 'Category'
        verbose_name_plural = 'Categories'
        unique_together = ('partner', 'title')


class Destination(BaseModel):
    """
    Destination model for travel destinations (DARJEELING, GOA, KERALA, etc.)
    """
    partner = models.ForeignKey(Partner, on_delete=models.CASCADE, related_name='destinations')
    title = models.CharField(max_length=100)
    description = models.TextField(help_text="Description is required")
    is_international = models.BooleanField(default=False)
    is_trending = models.BooleanField(default=False)
    best_time_to_visit = models.JSONField(
        default=list, 
        blank=True,
        help_text="List of month numbers when it's best to visit this destination"
    )
    explore_order = models.PositiveIntegerField(default=0)

    def save(self, *args, **kwargs):
        """Override save to ensure title is lowercase"""
        if self.title:
            self.title = self.title.lower().strip()
        super().save(*args, **kwargs)

    def __str__(self):
        return self.title
    
    def get_best_months_display(self):
        """Get display names for best time to visit months"""
        if not self.best_time_to_visit:
            return "Not specified"
        
        month_names = []
        for month_num in self.best_time_to_visit:
            try:
                # Convert to string since MonthChoices uses string values
                month_choice = MonthChoices(str(month_num))
                month_names.append(month_choice.label)
            except ValueError:
                continue
        return ", ".join(month_names) if month_names else "Not specified"
    
    def clean(self):
        """Validate best_time_to_visit field"""
        super().clean()
        if self.best_time_to_visit:
            valid_months = MonthChoices.get_month_list()
            for month in self.best_time_to_visit:
                if str(month) not in valid_months:
                    raise ValidationError(f"Invalid month: {month}. Must be between 1-12.")
    
    class Meta:
        verbose_name = 'Destination'
        verbose_name_plural = 'Destinations'
        unique_together = ('partner', 'title')
        indexes = [
            GistIndex(
                OpClass(Lower('title'), name='gist_trgm_ops'),
                name='destination_title_gist_idx'
            )
        ]


class Activity(BaseModel):
    """
    Activity model for travel activities (Sightseeing, Scuba Diving, etc.)
    """
    partner = models.ForeignKey(Partner, on_delete=models.CASCADE, related_name='activities')
    title = models.CharField(max_length=100)
    description = models.TextField(help_text="Description is required")
    is_featured = models.BooleanField(default=False)
    explore_order = models.PositiveIntegerField(default=0)

    def save(self, *args, **kwargs):
        """Override save to ensure title is lowercase"""
        if self.title:
            self.title = self.title.lower().strip()
        super().save(*args, **kwargs)

    def __str__(self):
        return self.title
    
    class Meta:
        verbose_name = 'Activity'
        verbose_name_plural = 'Activities'
        unique_together = ('partner', 'title')


class CategoryMedia(BaseModel):
    """
    Media model for Category - multiple media file per category
    """
    category = models.ForeignKey(Category, on_delete=models.CASCADE, related_name='media')
    media = models.FileField(upload_to=category_upload_path, help_text="Media file is required")
    
    def __str__(self):
        return f"Media for {self.category.title}"

    def clean(self):
        # Check if maximum media count is reached - only for existing categories
        if not self.pk and self.category and self.category.pk and self.category.media.count() >= Constants.CATEGORY_MAX_MEDIA_COUNT:
            raise ValidationError(f"Maximum of {Constants.CATEGORY_MAX_MEDIA_COUNT} media files allowed per category")
        
        # Validate file extension
        if self.media:
            ext = self.media.name.split('.')[-1].lower()
            if ext not in Constants.CATEGORY_MEDIA_EXTENSIONS:
                raise ValidationError(f"Invalid file extension. Allowed: {', '.join(Constants.CATEGORY_MEDIA_EXTENSIONS)}")
        
        super().clean()

    class Meta:
        verbose_name = 'Category Media'
        verbose_name_plural = 'Category Media'


class DestinationMedia(BaseModel):
    """
    Media model for Destination - multiple media files per destination
    """
    destination = models.ForeignKey(Destination, on_delete=models.CASCADE, related_name='media')
    media = models.FileField(upload_to=destination_upload_path, help_text="Media file is required")
    
    def __str__(self):
        return f"Media for {self.destination.title}"

    def clean(self):
        # Check if maximum media count is reached - only for existing destinations
        if not self.pk and self.destination and self.destination.pk and self.destination.media.count() >= Constants.DESTINATION_MAX_MEDIA_COUNT:
            raise ValidationError(f"Maximum of {Constants.DESTINATION_MAX_MEDIA_COUNT} media files allowed per destination")

        # Validate file extension
        if self.media:
            ext = self.media.name.split('.')[-1].lower()
            if ext not in Constants.DESTINATION_MEDIA_EXTENSIONS:
                raise ValidationError(f"Invalid file extension. Allowed: {', '.join(Constants.DESTINATION_MEDIA_EXTENSIONS)}")

        super().clean()

    class Meta:
        verbose_name = 'Destination Media'
        verbose_name_plural = 'Destination Media'


class ActivityMedia(BaseModel):
    """
    Media model for Activity - multiple media files per activity
    """
    main_display = models.BooleanField(default=False)
    activity = models.ForeignKey(Activity, on_delete=models.CASCADE, related_name='media')
    media = models.FileField(upload_to=activity_upload_path, help_text="Media file is required")
    
    def __str__(self):
        return f"Media for {self.activity.title}"

    def clean(self):
        # Check if maximum media count is reached - only for existing activities
        if not self.pk and self.activity and self.activity.pk and self.activity.media.count() >= Constants.ACTIVITY_MAX_MEDIA_COUNT:
            raise ValidationError(f"Maximum of {Constants.ACTIVITY_MAX_MEDIA_COUNT} media files allowed per activity")
        
        # Validate file extension
        if self.media: 
            ext = self.media.name.split('.')[-1].lower()
            if ext not in Constants.ACTIVITY_MEDIA_EXTENSIONS:
                raise ValidationError(f"Invalid file extension. Allowed: {', '.join(Constants.ACTIVITY_MEDIA_EXTENSIONS)}")
        
        # Validate main_display - only one per activity
        if self.main_display and self.activity:
            existing_main = ActivityMedia.objects.filter(
                activity=self.activity,
                main_display=True
            )
            
            # Exclude current instance if editing
            if self.pk:
                existing_main = existing_main.exclude(pk=self.pk)
            
            if existing_main.exists():
                raise ValidationError("Only one media file per activity can be set as main display")
        
        super().clean()

    class Meta:
        verbose_name = 'Activity Media'
        verbose_name_plural = 'Activity Media'



class PackageUploader(BaseModel):
    """
    Model to handle JSON/document file uploads for creating packages
    """
    partner = models.ForeignKey(Partner, on_delete=models.CASCADE, related_name='package_uploads')
    file = models.FileField(
        upload_to=package_upload_path,
        validators=[FileExtensionValidator(allowed_extensions=Constants.PACKAGE_FILE_EXTENSIONS)]
    )
    file_type = models.CharField(max_length=255, choices=PackageTypeChoices.choices)
    
    def __str__(self):
        return f"Package Upload {self.id}"
    
    class Meta:
        verbose_name = 'Package Uploader'
        verbose_name_plural = 'Package Uploaders'


"""
M2M Relationships b/w Package, Category, Activity:
Package -> Category
Package -> Activity
"""

class PackageCategory(BaseModel):
    """
    Through model for Package-Category M2M relationship
    """
    package = models.ForeignKey('Package', on_delete=models.CASCADE, related_name='package_categories')
    category = models.ForeignKey(Category, on_delete=models.CASCADE, related_name='package_categories')
    
    def save(self, *args, **kwargs):
        """Override save to handle soft delete conflicts"""
        if not self.pk:  # Only for new instances
            # Check for existing soft-deleted records with same package-category pair
            existing_soft_deleted = PackageCategory.global_objects.filter(
                package=self.package,
                category=self.category,
                deleted_at__isnull=False
            )
            
            # Hard delete any existing soft-deleted records
            if existing_soft_deleted.exists():
                existing_soft_deleted._raw_delete(existing_soft_deleted.db)
        
        super().save(*args, **kwargs)
    
    def delete(self, using=None, keep_parents=False):
        """Override delete to use hard_delete instead of soft delete for M2M relationships"""
        return self.hard_delete()
    
    class Meta:
        unique_together = ('package', 'category')
        verbose_name = 'Package Category'
        verbose_name_plural = 'Package Categories'
        
    def __str__(self):
        return f"{self.package.title} - {self.category.title}"


class PackageActivity(BaseModel):
    """
    Through model for Package-Activity M2M relationship
    """
    package = models.ForeignKey('Package', on_delete=models.CASCADE, related_name='package_activities')
    activity = models.ForeignKey(Activity, on_delete=models.CASCADE, related_name='package_activities')
    
    def save(self, *args, **kwargs):
        """Override save to handle soft delete conflicts"""
        if not self.pk:  # Only for new instances
            # Check for existing soft-deleted records with same package-activity pair
            existing_soft_deleted = PackageActivity.global_objects.filter(
                package=self.package,
                activity=self.activity,
                deleted_at__isnull=False
            )
            
            # Hard delete any existing soft-deleted records
            if existing_soft_deleted.exists():
                existing_soft_deleted._raw_delete(existing_soft_deleted.db)
        
        super().save(*args, **kwargs)
    
    def delete(self, using=None, keep_parents=False):
        """Override delete to use hard_delete instead of soft delete for M2M relationships"""
        return self.hard_delete()
    
    class Meta:
        unique_together = ('package', 'activity')
        verbose_name = 'Package Activity'
        verbose_name_plural = 'Package Activities'
        
    def __str__(self):
        return f"{self.package.title} - {self.activity.title}"


"""
M2M Relationships b/w Destination, Category, Activity:
Destination -> Category
Destination -> Activity

Category -> Activity
Category -> Destination

Activity -> Destination
Activity -> Category
"""

class DestinationCategory(BaseModel):
    """
    Through model for Destination-Category M2M relationship
    """
    destination = models.ForeignKey(Destination, on_delete=models.CASCADE, related_name='categories')
    category = models.ForeignKey(Category, on_delete=models.CASCADE, related_name='destinations')
    
    def save(self, *args, **kwargs):
        """Override save to handle soft delete conflicts"""
        if not self.pk:  # Only for new instances
            # Check for existing soft-deleted records with same destination-category pair
            existing_soft_deleted = DestinationCategory.global_objects.filter(
                destination=self.destination,
                category=self.category,
                deleted_at__isnull=False
            )
            
            # Hard delete any existing soft-deleted records
            if existing_soft_deleted.exists():
                existing_soft_deleted._raw_delete(existing_soft_deleted.db)
        
        super().save(*args, **kwargs)
    
    def delete(self, using=None, keep_parents=False):
        """Override delete to use hard_delete instead of soft delete for M2M relationships"""
        return self.hard_delete()
    
    class Meta:
        unique_together = ('destination', 'category')
        verbose_name = 'Destination Category'
        verbose_name_plural = 'Destination Categories'
        
    def __str__(self):
        return f"{self.destination.title} - {self.category.title}"


class DestinationActivity(BaseModel):
    """
    Through model for Destination-Activity M2M relationship
    """
    destination = models.ForeignKey(Destination, on_delete=models.CASCADE, related_name='activities')
    activity = models.ForeignKey(Activity, on_delete=models.CASCADE, related_name='destinations')
    
    def save(self, *args, **kwargs):
        """Override save to handle soft delete conflicts"""
        if not self.pk:  # Only for new instances
            # Check for existing soft-deleted records with same destination-activity pair
            existing_soft_deleted = DestinationActivity.global_objects.filter(
                destination=self.destination,
                activity=self.activity,
                deleted_at__isnull=False
            )
            
            # Hard delete any existing soft-deleted records
            if existing_soft_deleted.exists():
                existing_soft_deleted._raw_delete(existing_soft_deleted.db)
        
        super().save(*args, **kwargs)
    
    def delete(self, using=None, keep_parents=False):
        """Override delete to use hard_delete instead of soft delete for M2M relationships"""
        return self.hard_delete()
    
    class Meta:
        unique_together = ('destination', 'activity')
        verbose_name = 'Destination Activity'
        verbose_name_plural = 'Destination Activities'
        
    def __str__(self):
        return f"{self.destination.title} - {self.activity.title}"


# class CategoryActivity(BaseModel):
#     """
#     Through model for Category-Activity M2M relationship
#     """
#     category = models.ForeignKey(Category, on_delete=models.CASCADE, related_name='activities')
#     activity = models.ForeignKey(Activity, on_delete=models.CASCADE, related_name='categories')
    
#     class Meta:
#         unique_together = ('category', 'activity')
#         verbose_name = 'Category Activity'
#         verbose_name_plural = 'Category Activities'
        
#     def __str__(self):
#         return f"{self.category.title} - {self.activity.title}"



class Establishment(BaseModel):
    name = models.TextField()
    address = models.TextField(blank=True, null=True)
    phone = models.CharField(max_length=255, blank=True, null=True)
    website = models.TextField(blank=True, null=True)
    rating = models.DecimalField(max_digits=3, decimal_places=2, blank=True, null=True)
    review_count = models.PositiveIntegerField(blank=True, null=True)
    image_urls = ArrayField(models.CharField(max_length=500), blank=True, default=list)
    description = models.TextField(blank=True, null=True)
    amenities = ArrayField(models.CharField(max_length=500), blank=True, default=list)

    class Meta:
        abstract = True

    def __str__(self):
        return self.name


class PackageHotel(Establishment):
    class Meta:
        verbose_name = "Hotel"
        verbose_name_plural = "Hotels"


class PackageRestaurant(Establishment):
    class Meta:
        verbose_name = "Restaurant"
        verbose_name_plural = "Restaurants"


class Package(BaseModel):
    """
    Main Package model with all relationships
    """
    partner = models.ForeignKey(Partner, on_delete=models.CASCADE, related_name='packages')
    package_uploaded = models.ForeignKey(PackageUploader, on_delete=models.CASCADE, related_name='packages', null=True, blank=True)

    title = models.CharField(max_length=255)
    package_no = models.CharField(max_length=255)
    
    # M2M Relationships with through tables
    categories = models.ManyToManyField(Category, through=PackageCategory, related_name='packages')
    activities = models.ManyToManyField(Activity, through=PackageActivity, related_name='packages')
    
    # Single destination (M2O)
    destination = models.ForeignKey(Destination, on_delete=models.CASCADE, related_name='packages')
    owner = models.CharField(max_length=255)
    type = models.CharField(max_length=255, choices=PackageTypeChoices.choices)
    duration = models.CharField(max_length=255)
    duration_in_nights = models.PositiveIntegerField()
    duration_in_days = models.PositiveIntegerField()

    currency = models.CharField(max_length=255)
    price = models.DecimalField(max_digits=10, decimal_places=2)
    price_per_person = models.CharField(max_length=255)
    visa_type = ArrayField(models.CharField(max_length=255))

    # Description
    about_this_tour = models.TextField()
    # highlights = ArrayField(models.CharField())
    # inclusions = ArrayField(models.CharField())
    exclusions = ArrayField(models.CharField())

    # Package data
    itinerary = RichTextField()
    hotels = ArrayField(models.CharField(max_length=255), default=list)
    package_hotels = models.ManyToManyField(PackageHotel, through='PackageHotelRelation', related_name='packages', blank=True)
    popular_activities = ArrayField(models.CharField(max_length=255), default=list)
    # addons = ArrayField(models.CharField(max_length=255), default=list, verbose_name="Add Ons")

    is_published = models.BooleanField(default=False)
    important_notes = ArrayField(models.CharField(max_length=255), default=list, blank=True)

    best_time_to_visit_months = ArrayField(models.CharField(max_length=255), null=True, blank=True)
    best_time_to_visit = models.TextField(null=True, blank=True)
    rating = models.DecimalField(max_digits=2, decimal_places=1, null=True, blank=True)
    rating_description = models.TextField(null=True, blank=True, help_text="Brief description about the package rating (auto-generated if empty)")
    currency_conversion_rate = models.TextField(null=True, blank=True)
    destination_safety = models.TextField(null=True, blank=True)
    popular_restaurants = ArrayField(models.CharField(max_length=255), default=list, blank=True)
    package_restaurants = models.ManyToManyField(PackageRestaurant, through='PackageRestaurantRelation', related_name='packages', blank=True)
    what_to_shop = models.TextField(null=True, blank=True)
    what_to_pack = RichTextField(null=True, blank=True)
    cultural_info = models.TextField(null=True, blank=True)
    
    explore_order = models.PositiveIntegerField(default=0)

    def __str__(self):
        return f"{self.title} ({self.package_no})"
    
    class Meta:
        verbose_name = 'Package'
        verbose_name_plural = 'Packages'


class CustomPackage(Package):
    class Meta:
        proxy = True


class PackageHotelRelation(BaseModel):
    package = models.ForeignKey(Package, on_delete=models.CASCADE)
    hotel = models.ForeignKey(PackageHotel, on_delete=models.CASCADE)

    class Meta:
        unique_together = ('package', 'hotel')
        verbose_name = 'Package Hotel Relation'
        verbose_name_plural = 'Package Hotel Relations'


class PackageRestaurantRelation(BaseModel):
    package = models.ForeignKey(Package, on_delete=models.CASCADE)
    restaurant = models.ForeignKey(PackageRestaurant, on_delete=models.CASCADE)

    class Meta:
        unique_together = ('package', 'restaurant')
        verbose_name = 'Package Restaurant Relation'
        verbose_name_plural = 'Package Restaurant Relations'


class PackageHighlight(BaseModel):
    """
    Model for package highlights with icon mapping
    """
    package = models.ForeignKey(Package, on_delete=models.CASCADE, related_name='highlights')
    value = models.TextField(help_text="Highlight description")
    icon_class = models.CharField(
        max_length=50, 
        help_text="Icon class for this highlight"
    )
    
    def __str__(self):
        return f"{self.package.title} - {self.value[:50]}"
    
    class Meta:
        verbose_name = 'Package Highlight'
        verbose_name_plural = 'Package Highlights'
        ordering = ['created_at']


class PackageInclusion(BaseModel):
    """
    Model for package inclusions with icon mapping
    """
    package = models.ForeignKey(Package, on_delete=models.CASCADE, related_name='inclusions')
    value = models.TextField(help_text="Inclusion description")
    icon_class = models.CharField(
        max_length=50,
        help_text="Icon class for this inclusion"
    )
    
    def __str__(self):
        return f"{self.package.title} - {self.value[:50]}"
    
    class Meta:
        verbose_name = 'Package Inclusion'
        verbose_name_plural = 'Package Inclusions'
        ordering = ['created_at']


class PackageAddon(BaseModel):
    """
    Model for package addons with icon mapping
    """
    package = models.ForeignKey(Package, on_delete=models.CASCADE, related_name='addons')
    value = models.TextField(help_text="Addon description")
    icon_class = models.CharField(
        max_length=50,
        help_text="Icon class for this addon"
    )
    
    def __str__(self):
        return f"{self.package.title} - {self.value[:50]}"
    
    class Meta:
        verbose_name = 'Package Addon'
        verbose_name_plural = 'Package Addons'
        ordering = ['created_at']


class PackageMedia(BaseModel):
    """
    Model to store media files (images/videos) for a package
    """
    package = models.ForeignKey(Package, on_delete=models.CASCADE, related_name='media')
    title = models.CharField(max_length=255, null=True, blank=True)
    file_type = models.CharField(max_length=255, choices=PackageMediaTypes.choices)
    file = models.FileField(
        upload_to=package_media_upload_path,
        validators=[FileExtensionValidator(allowed_extensions=Constants.PACKAGE_MEDIA_EXTENSIONS)]
    )

    def __str__(self):
        return f"Media for {self.package.title}"
    
    def clean(self):
        # Check if maximum media count is reached - only for existing packages
        if not self.pk and self.package and self.package.pk and self.package.media.count() >= Constants.PACKAGE_MAX_MEDIA_COUNT:
            raise ValidationError(f"Maximum of {Constants.PACKAGE_MAX_MEDIA_COUNT} media files allowed per package")

        # Validate file extension if not already validated by FileExtensionValidator
        if self.file:
            ext = self.file.name.split('.')[-1].lower()
            if ext not in Constants.PACKAGE_MEDIA_EXTENSIONS:
                raise ValidationError(f"Invalid file extension. Allowed: {', '.join(Constants.PACKAGE_MEDIA_EXTENSIONS)}")

        super().clean()

    class Meta:
        verbose_name = 'Package Media'
        verbose_name_plural = 'Package Media'


class DestinationFaq(BaseModel):
    """
    Model to store FAQ for a destination
    """
    destination = models.ForeignKey(Destination, on_delete=models.CASCADE, related_name='faqs')
    partner = models.ForeignKey(Partner, on_delete=models.CASCADE, related_name='destination_faqs')
    question = models.TextField()
    answer = models.TextField()
    is_published = models.BooleanField(default=False)
    priority = models.PositiveIntegerField(default=0)

    class Meta:
        verbose_name = 'Destination FAQ'
        verbose_name_plural = 'Destination FAQs'
        ordering = ['-priority', '-created_at']


class CustomActivityCategory(BaseModel):
    """
    Category model for Custom Activities from GetYourGuide Platform
    """
    name = models.CharField(max_length=255, null=True, blank=True)

    def __str__(self):
        return self.name or 'Unknown Category'
    
    class Meta:
        verbose_name = 'Custom Activity Category'
        verbose_name_plural = 'Custom Activity Categories'


class CustomActivityLocation(BaseModel):
    """
    Location model for Custom Activities from GetYourGuide Platform
    """
    location_coordinates = gis_models.PointField(srid=4326, null=True, blank=True)
    country = models.CharField(max_length=255, null=True, blank=True)
    city = models.CharField(max_length=255, null=True, blank=True)
    google_place_id = models.CharField(max_length=255, null=True, blank=True)

    def __str__(self):
        return f"{self.city}, {self.country}" if self.city and self.country else 'Unknown Location'
    
    class Meta:
        verbose_name = 'Custom Activity Location'
        verbose_name_plural = 'Custom Activity Locations'


class CustomActivity(BaseModel):
    """
    Custom Activity model for storing data from GetYourGuide Platform
    """
    destination = models.ForeignKey(Destination, on_delete=models.CASCADE, related_name='custom_activities', null=True, blank=True)
    tour_id = models.CharField(max_length=255, null=True, blank=True)
    title = models.TextField(null=True, blank=True)
    abstract = models.TextField(null=True, blank=True)
    description = models.TextField(null=True, blank=True)
    activity_type = models.CharField(max_length=255, null=True, blank=True)
    additional_information = models.TextField(null=True, blank=True)
    items_to_bring = models.TextField(null=True, blank=True)
    not_allowed = models.TextField(null=True, blank=True)
    not_suitable_for = models.TextField(null=True, blank=True)
    bestseller = models.BooleanField(default=False, null=True, blank=True)
    certified = models.BooleanField(default=False, null=True, blank=True)
    has_pick_up = models.BooleanField(default=False, null=True, blank=True)
    overall_rating = models.DecimalField(max_digits=3, decimal_places=2, null=True, blank=True)
    number_of_ratings = models.PositiveIntegerField(null=True, blank=True)
    highlights = models.TextField(null=True, blank=True)
    inclusions = models.TextField(null=True, blank=True)
    exclusions = models.TextField(null=True, blank=True)
    coordinates = gis_models.PointField(srid=4326, null=True, blank=True)
    price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    location_id = models.CharField(max_length=255, null=True, blank=True)
    opening_hours = models.TextField(null=True, blank=True)
    cancellation_policy_text = models.TextField(null=True, blank=True)
    cancellation_policy = models.JSONField(default=dict, null=True, blank=True)
    persona = ArrayField(
        models.CharField(max_length=50, choices=PersonaChoices.choices),
        default=list,
        blank=True,
        help_text="Select one or more personas this activity is suitable for"
    )
    durations = models.JSONField(default=list, null=True, blank=True)
    
    # M2M relationships with through tables
    categories = models.ManyToManyField(CustomActivityCategory, through='CustomActivityCategoryRelation', related_name='activities')
    locations = models.ManyToManyField(CustomActivityLocation, through='CustomActivityLocationRelation', related_name='activities')
    # M2M relationship with system categories
    system_categories = models.ManyToManyField(Category, through='CustomActivitySystemCategoryRelation', related_name='custom_activities', blank=True)

    addons = ArrayField(models.CharField(max_length=255), default=list, blank=True)
    adult_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    child_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    infant_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)

    preferred_start_time = models.TimeField(null=True, blank=True)
    preferred_end_time = models.TimeField(null=True, blank=True)

    def __str__(self):
        return self.title or f'Custom Activity {self.tour_id or self.id}'
    
    class Meta:
        verbose_name = 'Custom Activity'
        verbose_name_plural = 'Custom Activities'
        indexes = [
            models.Index(
                fields=['destination', 'is_active', '-overall_rating'],
                name='ca_dest_active_rating'
            ),
            # For queries without persona filter
            models.Index(
                fields=['destination', 'is_active'],
                name='cust_act_dest_active_idx'
            ),
            # GIN index for persona array overlap operations
            GinIndex(
                fields=['persona'],
                name='cust_act_persona_gin_idx'
            ),
        ]


class CustomActivityMedia(BaseModel):
    """
    Media model for Custom Activity - multiple media files per activity
    """
    custom_activity = models.ForeignKey(CustomActivity, on_delete=models.CASCADE, related_name='media')
    media = models.FileField(upload_to=custom_activity_media_upload_path, help_text="Media file is required")
    title = models.CharField(max_length=255, null=True, blank=True)
    description = models.TextField(null=True, blank=True)
    meta_information = models.JSONField(default=dict, null=True, blank=True)
    
    def __str__(self):
        return f"Media for {self.custom_activity.title or 'Custom Activity'}"

    # def clean(self):
    #     # Check if maximum media count is reached - only for existing activities
    #     if not self.pk and self.custom_activity and self.custom_activity.pk and self.custom_activity.media.count() >= Constants.ACTIVITY_MAX_MEDIA_COUNT:
    #         raise ValidationError(f"Maximum of {Constants.ACTIVITY_MAX_MEDIA_COUNT} media files allowed per activity")
    #     
    #     # Validate file extension
    #     if self.media: 
    #         ext = self.media.name.split('.')[-1].lower()
    #         if ext not in Constants.ACTIVITY_MEDIA_EXTENSIONS:
    #             raise ValidationError(f"Invalid file extension. Allowed: {', '.join(Constants.ACTIVITY_MEDIA_EXTENSIONS)}")
    #     
    #     super().clean()

    class Meta:
        verbose_name = 'Custom Activity Media'
        verbose_name_plural = 'Custom Activity Media'


class CustomActivityCategoryRelation(BaseModel):
    """
    Through model for CustomActivity-CustomActivityCategory M2M relationship
    """
    activity = models.ForeignKey(CustomActivity, on_delete=models.CASCADE, related_name='activity_categories')
    category = models.ForeignKey(CustomActivityCategory, on_delete=models.CASCADE, related_name='category_activities')
    
    def save(self, *args, **kwargs):
        """Override save to handle soft delete conflicts"""
        if not self.pk:  # Only for new instances
            # Check for existing soft-deleted records with same activity-category pair
            existing_soft_deleted = CustomActivityCategoryRelation.global_objects.filter(
                activity=self.activity,
                category=self.category,
                deleted_at__isnull=False
            )
            
            # Hard delete any existing soft-deleted records
            if existing_soft_deleted.exists():
                existing_soft_deleted._raw_delete(existing_soft_deleted.db)
        
        super().save(*args, **kwargs)
    
    def delete(self, using=None, keep_parents=False):
        """Override delete to use hard_delete instead of soft delete for M2M relationships"""
        return self.hard_delete()
    
    class Meta:
        unique_together = ('activity', 'category')
        verbose_name = 'Custom Activity Category Relation'
        verbose_name_plural = 'Custom Activity Category Relations'
        
    def __str__(self):
        return f"{self.activity.title or 'Activity'} - {self.category.name or 'Category'}"


class CustomActivityLocationRelation(BaseModel):
    """
    Through model for CustomActivity-CustomActivityLocation M2M relationship
    """
    activity = models.ForeignKey(CustomActivity, on_delete=models.CASCADE, related_name='activity_locations')
    location = models.ForeignKey(CustomActivityLocation, on_delete=models.CASCADE, related_name='location_activities')
    
    def save(self, *args, **kwargs):
        """Override save to handle soft delete conflicts"""
        if not self.pk:  # Only for new instances
            # Check for existing soft-deleted records with same activity-location pair
            existing_soft_deleted = CustomActivityLocationRelation.global_objects.filter(
                activity=self.activity,
                location=self.location,
                deleted_at__isnull=False
            )
            
            # Hard delete any existing soft-deleted records
            if existing_soft_deleted.exists():
                existing_soft_deleted._raw_delete(existing_soft_deleted.db)
        
        super().save(*args, **kwargs)
    
    def delete(self, using=None, keep_parents=False):
        """Override delete to use hard_delete instead of soft delete for M2M relationships"""
        return self.hard_delete()
    
    class Meta:
        unique_together = ('activity', 'location')
        verbose_name = 'Custom Activity Location Relation'
        verbose_name_plural = 'Custom Activity Location Relations'
        
    def __str__(self):
        return f"{self.activity.title or 'Activity'} - {self.location}"


class CustomActivitySystemCategoryRelation(BaseModel):
    """
    Through model for CustomActivity-SystemCategory M2M relationship
    """
    activity = models.ForeignKey(CustomActivity, on_delete=models.CASCADE, related_name='system_category_relations')
    category = models.ForeignKey(Category, on_delete=models.CASCADE, related_name='custom_activity_relations')
    
    def save(self, *args, **kwargs):
        """Override save to handle soft delete conflicts"""
        if not self.pk:  # Only for new instances
            # Check for existing soft-deleted records with same activity-category pair
            existing_soft_deleted = CustomActivitySystemCategoryRelation.global_objects.filter(
                activity=self.activity,
                category=self.category,
                deleted_at__isnull=False
            )
            
            # Hard delete any existing soft-deleted records
            if existing_soft_deleted.exists():
                existing_soft_deleted._raw_delete(existing_soft_deleted.db)
        
        super().save(*args, **kwargs)
    
    def delete(self, using=None, keep_parents=False):
        """Override delete to use hard_delete instead of soft delete for M2M relationships"""
        return self.hard_delete()
    
    class Meta:
        unique_together = ('activity', 'category')
        verbose_name = 'Custom Activity System Category Relation'
        verbose_name_plural = 'Custom Activity System Category Relations'
        
    def __str__(self):
        return f"{self.activity.title or 'Activity'} - {self.category.title}"


# CustomPackages Models
class Itinerary(BaseModel):
    partner = models.ForeignKey(Partner, on_delete=models.CASCADE, related_name='itineraries')
    package = models.ForeignKey(
        Package, 
        on_delete=models.CASCADE, 
        related_name='itineraries'
    )
    day_number = models.PositiveIntegerField()
    date = models.DateField()
    day_title = models.CharField(max_length=255)
    description = models.TextField()
    order = models.PositiveIntegerField()

    inclusions = models.JSONField(default=list, null=True, blank=True)
    meta_information = models.JSONField(default=dict, null=True, blank=True)
    
    class Meta:
        verbose_name = 'Itinerary'
        verbose_name_plural = 'Itineraries'
        ordering = ['package', 'day_number']
        unique_together = ('package', 'day_number')
    
    def __str__(self):
        return f"{self.package.title} - Day {self.day_number}"


class ItineraryHotel(BaseModel):
    hotel = models.ForeignKey(Hotel, on_delete=models.SET_NULL, null=True, blank=True)
    check_in_time = models.TimeField(
        null=True, 
        blank=True,
        help_text="Check-in time for hotel"
    )
    check_out_time = models.TimeField(
        null=True, 
        blank=True,
        help_text="Check-out time from hotel"
    )
    meta_information = models.JSONField(default=dict, null=True, blank=True)


class ItineraryActivity(BaseModel):
    activity = models.ForeignKey(CustomActivity, on_delete=models.SET_NULL, null=True, blank=True)
    start_time = models.TimeField(
        null=True, 
        blank=True,
        help_text="Start time for activity"
    )
    end_time = models.TimeField(
        null=True, 
        blank=True,
        help_text="End time for activity"
    )
    meta_information = models.JSONField(default=dict, null=True, blank=True)


class ItineraryDayItem(BaseModel):
    partner = models.ForeignKey(Partner, on_delete=models.CASCADE, related_name='itinerary_day_items')
    itinerary = models.ForeignKey(Itinerary, on_delete=models.CASCADE, related_name='day_items')
    type = models.CharField(max_length=255, choices=ItineraryDayItemType.choices)
    title = models.CharField(max_length=255, null=True, blank=True)
    description = models.TextField()
    order = models.PositiveIntegerField()
    duration = models.CharField(max_length=255, null=True, blank=True)
    inclusions = models.JSONField(default=list, null=True, blank=True)
    meta_information = models.JSONField(default=dict, null=True, blank=True)

    hotel = models.ForeignKey(ItineraryHotel, on_delete=models.SET_NULL, null=True, blank=True)
    activity = models.ForeignKey(ItineraryActivity, on_delete=models.SET_NULL, null=True, blank=True)

    class Meta:
        verbose_name = 'Itinerary Day Item'
        verbose_name_plural = 'Itinerary Day Items'

    def __str__(self):
        return f"{self.itinerary.package.title} - Day {self.itinerary.day_number} - {self.type}"


def delete_packages_data():
    """
    Delete all packages data from the database
    """
    packages = Package.global_objects.all()
    for package in packages:
        package.hard_delete()
    
    categories = Category.global_objects.all()
    for category in categories:
        category.hard_delete()
    
    activities = Activity.global_objects.all()
    for activity in activities:
        activity.hard_delete()
    
    destinations = Destination.global_objects.all()
    for destination in destinations:
        destination.hard_delete()
    
    package_uploads = PackageUploader.global_objects.all()
    for package_uploader in package_uploads:
        package_uploader.hard_delete()
    
    package_categories = PackageCategory.global_objects.all()
    for package_category in package_categories:
        package_category.hard_delete()
    
    package_activities = PackageActivity.global_objects.all()
    for package_activity in package_activities:
        package_activity.hard_delete()
    
    destination_categories = DestinationCategory.global_objects.all()
    for destination_category in destination_categories:
        destination_category.hard_delete()
    
    destination_activities = DestinationActivity.global_objects.all()
    for destination_activity in destination_activities:
        destination_activity.hard_delete()
    
    package_highlights = PackageHighlight.global_objects.all()
    for package_highlight in package_highlights:
        package_highlight.hard_delete()
    
    package_inclusions = PackageInclusion.global_objects.all()
    for package_inclusion in package_inclusions:
        package_inclusion.hard_delete()
    
    package_addons = PackageAddon.global_objects.all()
    for package_addon in package_addons:
        package_addon.hard_delete()
    
    destination_faqs = DestinationFaq.global_objects.all()
    for destination_faq in destination_faqs:
        destination_faq.hard_delete()
    
    package_media = PackageMedia.global_objects.all()
    for package_media in package_media:
        package_media.hard_delete()
    
    destination_media = DestinationMedia.global_objects.all()
    for destination_media in destination_media:
        destination_media.hard_delete()
    
    activity_media = ActivityMedia.global_objects.all()
    for activity_media in activity_media:
        activity_media.hard_delete()
    
    category_media = CategoryMedia.global_objects.all()
    for category_media in category_media:
        category_media.hard_delete()
    
    custom_activity_categories = CustomActivityCategory.global_objects.all()
    for custom_activity_category in custom_activity_categories:
        custom_activity_category.hard_delete()

    custom_activity_locations = CustomActivityLocation.global_objects.all()
    for custom_activity_location in custom_activity_locations:
        custom_activity_location.hard_delete()

    custom_activities = CustomActivity.global_objects.all()
    for custom_activity in custom_activities:
        custom_activity.hard_delete()
    
    custom_activity_media = CustomActivityMedia.global_objects.all()
    for custom_activity_media in custom_activity_media:
        custom_activity_media.hard_delete()
    
    custom_activity_category_relations = CustomActivityCategoryRelation.global_objects.all()
    for relation in custom_activity_category_relations:
        relation.hard_delete()
    
    custom_activity_location_relations = CustomActivityLocationRelation.global_objects.all()
    for relation in custom_activity_location_relations:
        relation.hard_delete()
    
    custom_activity_system_category_relations = CustomActivitySystemCategoryRelation.global_objects.all()
    for relation in custom_activity_system_category_relations:
        relation.hard_delete()
    
    print("All packages data deleted successfully")


def delete_custom_activities_data():
    """
    Delete all custom activities data from the database
    """

    custom_activity_category_relations = CustomActivityCategoryRelation.global_objects.all()
    for relation in custom_activity_category_relations:
        relation.hard_delete()
    
    custom_activity_location_relations = CustomActivityLocationRelation.global_objects.all()
    for relation in custom_activity_location_relations:
        relation.hard_delete()
    
    custom_activity_system_category_relations = CustomActivitySystemCategoryRelation.global_objects.all()
    for relation in custom_activity_system_category_relations:
        relation.hard_delete()

    custom_activity_categories = CustomActivityCategory.global_objects.all()
    for custom_activity_category in custom_activity_categories:
        custom_activity_category.hard_delete()

    custom_activity_locations = CustomActivityLocation.global_objects.all()
    for custom_activity_location in custom_activity_locations:
        custom_activity_location.hard_delete()

    custom_activities = CustomActivity.global_objects.all()
    for custom_activity in custom_activities:
        custom_activity.hard_delete()
    
    print("All custom activities data deleted successfully")
