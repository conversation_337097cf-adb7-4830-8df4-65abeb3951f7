from django import forms
from django.core.exceptions import ValidationError
from django.db import transaction
from django.utils.safestring import mark_safe
import json
import re
import logging

from base.utils import get_file_and_extension
from base.static import Constants
from packages.models import (
    Package, PackageUploader, PackageCategory, PackageActivity, 
    Destination, Category, Activity, CustomActivity, DestinationFaq,
    ActivityMedia, PackageHighlight, PackageInclusion, PackageAddon,
    Itinerary, ItineraryDayItem, ItineraryHotel, ItineraryActivity, CustomPackage
)
from dynamic_packages.models import Hotel
from packages.choices import PackageTypeChoices, PersonaChoices, MonthChoices, ItineraryDayItemType
from packages.utils.package_format_helpers import (
    get_sample_package_format, 
    get_sample_doc_format,
)
from packages.services.package_creation_service import PackageCreationService
from packages.utils.custom_package_helper import CustomPackageHelper

from django_better_admin_arrayfield.forms.fields import DynamicArrayField
from django_better_admin_arrayfield.forms.widgets import DynamicArrayWidget
from ckeditor.widgets import CKEditorWidget
from django.contrib.admin.widgets import ForeignKeyRawIdWidget
from django.utils.html import format_html
from django.contrib.postgres.fields import ArrayField
import logging
from .widgets import InfiniteScrollSelectWidget

logger = logging.getLogger(__name__)


class FixedTypeWidget(forms.TextInput):
    """Custom widget that always shows 'Fixed' for the type field"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.attrs.update({
            'readonly': True,
            'style': 'background-color: #f5f5f5;',
            'value': 'Fixed'
        })
    
    def render(self, name, value, attrs=None, renderer=None):
        # Always render with 'Fixed' value regardless of what's passed
        from packages.choices import PackageTypeChoices
        return super().render(name, PackageTypeChoices.FIXED.value, attrs, renderer)
    
    def value_from_datadict(self, data, files, name):
        # Always return 'Fixed' regardless of form data
        from packages.choices import PackageTypeChoices
        return PackageTypeChoices.FIXED.value


class ClearableDynamicArrayField(DynamicArrayField):
    """
    Custom DynamicArrayField that properly handles empty submissions.
    When no data is submitted (user cleared all items), returns empty array
    instead of falling back to initial value.
    """
    
    def clean(self, value):
        """Override clean to handle empty submissions properly"""
        # If no value is submitted (None or empty list), return empty list
        if value is None or (isinstance(value, list) and len(value) == 0):
            return []
        
        # If we get [''] (single empty string), also return empty list
        if isinstance(value, list) and len(value) == 1 and value[0] == '':
            return []
        
        # Otherwise use the parent clean method
        cleaned = super().clean(value)
        
        # Filter out any empty strings from the cleaned data
        if isinstance(cleaned, list):
            cleaned = [item.strip() for item in cleaned if item and item.strip()]
        
        return cleaned


class DynamicModelChoiceField(forms.ModelChoiceField):
    """
    A ModelChoiceField that dynamically populates its queryset based on the submitted value.
    This is useful for infinite scroll widgets where the full queryset is too large to load.
    """
    
    def __init__(self, model_class=None, *args, **kwargs):
        self.model_class = model_class
        super().__init__(*args, **kwargs)
    
    def to_python(self, value):
        """Convert the submitted value to a model instance"""
        if value in self.empty_values:
            return None
        
        try:
            value = int(value)
            # Try to get the object from the database
            if self.model_class:
                obj = self.model_class.objects.filter(pk=value, is_active=True).first()
                if obj:
                    # Dynamically update the queryset to include this object
                    self.queryset = self.model_class.objects.filter(pk=value)
                    return obj
        except (ValueError, TypeError, self.model_class.DoesNotExist):
            pass
            
        raise forms.ValidationError(
            self.error_messages['invalid_choice'],
            code='invalid_choice',
            params={'value': value},
        )
    
    def validate(self, value):
        """Validate that the selected value exists and is active"""
        if value is None and not self.required:
            return
        
        if value is not None:
            # Additional validation - ensure the object is still active
            if hasattr(value, 'is_active') and not value.is_active:
                raise forms.ValidationError(
                    f"The selected {self.model_class._meta.verbose_name.lower()} is no longer available.",
                    code='invalid_choice'
                )
        
        return super().validate(value)


class PackageAdminForm(forms.ModelForm):
    """Comprehensive Package form with AI-oriented validation"""

    # Virtual array fields for the admin interface
    highlights = ClearableDynamicArrayField(
        forms.CharField(max_length=500),
        widget=DynamicArrayWidget(),
        required=True,
        help_text='Package highlights (one per line)'
    )
    
    inclusions = ClearableDynamicArrayField(
        forms.CharField(max_length=500),
        widget=DynamicArrayWidget(),
        required=True,
        help_text='What is included in the package (one per line)'
    )
    
    addons = ClearableDynamicArrayField(
        forms.CharField(max_length=500),
        widget=DynamicArrayWidget(),
        required=False,
        help_text='Add On services like Flight, Visa, Insurance, Upgrades (one per line)'
    )

    visa_type = ClearableDynamicArrayField(
        forms.CharField(max_length=255),
        widget=DynamicArrayWidget(),
        required=False,
        help_text='Types of visa available (one per line)'
    )

    class Meta:
        model = Package
        exclude = ['currency', 'price', 'package_uploaded', 'partner', 'duration_in_nights', 'duration_in_days', 'best_time_to_visit_months']
        widgets = {
            'itinerary': CKEditorWidget(config_name='default'),
            'what_to_pack': CKEditorWidget(config_name='default'),
            'type': forms.TextInput(attrs={'readonly': 'readonly'}),  # Make type readonly
        }

    def __init__(self, *args, **kwargs):
        # Extract request from kwargs if passed by admin
        self.request = kwargs.pop('request', None)
        super().__init__(*args, **kwargs)
        
        # Set type field to FIXED and make it readonly
        from packages.choices import PackageTypeChoices
        if 'type' in self.fields:  # Safety check to ensure field exists
            # Replace the field with a simple CharField that shows Fixed using custom widget
            self.fields['type'] = forms.CharField(
                initial=PackageTypeChoices.FIXED.value,
                widget=FixedTypeWidget(),
                help_text='Package type is set to Fixed for structured packages',
                required=False  # Make it not required since it's readonly
            )
            
            # For new instances, ensure the instance type is set
            if not self.instance.pk:
                self.instance.type = PackageTypeChoices.FIXED.value
        
        # Initialize virtual fields with existing data
        if self.instance and self.instance.pk:
            self.fields['highlights'].initial = [h.value for h in self.instance.highlights.all()]
            self.fields['inclusions'].initial = [i.value for i in self.instance.inclusions.all()]
            self.fields['addons'].initial = [a.value for a in self.instance.addons.all()]
        
        # Add comprehensive help texts
        self._add_help_texts()
    
    def _add_help_texts(self):
        """Add comprehensive help texts for all fields"""
        help_texts = {
            'title': 'Enter a descriptive title for the package',
            'package_no': 'Unique package number/code',
            'destination': 'Select the destination for this package',
            'type': 'Package type is automatically set to Fixed',
            'explore_order': 'Order for displaying packages in API results (lower numbers appear first)',
            'duration': 'Format: "4N & 5D" (4 Nights & 5 Days)',
            'price_per_person': 'Enter price with currency symbol or code (e.g., ₹39,999, INR 39,999, $500, €450) - AI will extract currency and numeric value',
            'currency_conversion_rate': 'Conversion rate to INR (if applicable)',
            'visa_type': 'Types of visa available (one per line)',
            'best_time_to_visit': 'Best months/seasons to visit (AI will extract months automatically)',
            'destination_safety': 'Safety information for travelers',
            'rating': 'Package rating (0.0 to 5.0)',
            'rating_description': 'Brief description about the package rating (optional - AI will auto-generate if empty)',
            'about_this_tour': 'Detailed description of the tour',
            'highlights': 'Key highlights of the package (one per line)',
            'inclusions': 'What is included in the package (one per line)',
            'exclusions': 'What is not included (one per line)',
            'itinerary': 'Day-wise itinerary in HTML format.',
            'hotels': 'Recommended hotels (one per line)',
            'popular_restaurants': 'Popular restaurants (one per line)',
            'popular_activities': 'Popular activities (one per line)',
            'addons': 'Add On services like Flight, Visa, Insurance, Upgrades (one per line)',
            'cultural_info': 'Cultural information and tips',
            'what_to_shop': 'Shopping recommendations',
            'what_to_pack': 'Packing list in HTML format with categories and items (AI will auto-generate if empty)',
            'important_notes': 'Important notes for travelers (one per line)',
            'owner': 'Package owner/operator name',
        }
        
        for field_name, help_text in help_texts.items():
            if field_name in self.fields:
                self.fields[field_name].help_text = help_text

    @transaction.atomic
    def clean(self):
        """Validate and process package data with AI enhancement"""
        cleaned_data = super().clean()
        
        # Force type to be FIXED for PackageAdmin
        cleaned_data['type'] = PackageTypeChoices.FIXED.value
        
        # Convert virtual array fields to regular arrays for PackageCreationService
        if 'highlights' in cleaned_data and cleaned_data['highlights']:
            # Filter out empty strings and None values
            cleaned_data['highlights'] = [h.strip() for h in cleaned_data['highlights'] if h and h.strip()]
        else:
            cleaned_data['highlights'] = []
            
        if 'inclusions' in cleaned_data and cleaned_data['inclusions']:
            # Filter out empty strings and None values
            cleaned_data['inclusions'] = [i.strip() for i in cleaned_data['inclusions'] if i and i.strip()]
        else:
            cleaned_data['inclusions'] = []
            
        if 'addons' in cleaned_data and cleaned_data['addons']:
            # Filter out empty strings and None values
            cleaned_data['addons'] = [a.strip() for a in cleaned_data['addons'] if a and a.strip()]
        else:
            cleaned_data['addons'] = []
        
        # EXPLICIT VALIDATION for required array fields
        print(f"[DEBUG] Validating highlights: {cleaned_data.get('highlights')} (length: {len(cleaned_data.get('highlights', []))})")
        if not cleaned_data.get('highlights') or len(cleaned_data['highlights']) == 0:
            print(f"[DEBUG] Adding error for empty highlights")
            self.add_error('highlights', 'At least one highlight is required.')
        
        print(f"[DEBUG] Validating inclusions: {cleaned_data.get('inclusions')} (length: {len(cleaned_data.get('inclusions', []))})")
        if not cleaned_data.get('inclusions') or len(cleaned_data['inclusions']) == 0:
            print(f"[DEBUG] Adding error for empty inclusions")
            self.add_error('inclusions', 'At least one inclusion is required.')
        
        print(f"[DEBUG] Form errors after validation: {self.errors}")
        print(f"[DEBUG] Form has errors: {bool(self.errors)}")
        
        # Store processed package for later use in save()
        self._processed_package = None
        self._is_update = False
        
        # Only validate absolutely critical fields that AI cannot guess
        critical_fields = ['title', 'package_no', 'destination']
        for field in critical_fields:
            if not cleaned_data.get(field):
                self.add_error(field, f"{field.replace('_', ' ').title()} is required")
        
        # If basic validation failed, don't proceed with AI processing
        if self.errors:
            return cleaned_data
        
        print("DEBUG: Starting PackageAdminForm clean() with AI processing")
        
        # Create a savepoint to ensure rollback if anything fails
        savepoint_id = transaction.savepoint()
        
        try:
            # Get partner information
            partner = None
            if hasattr(self, 'request') and self.request:
                from packages.admin import get_user_effective_partner
                partner = get_user_effective_partner(self.request)
                print(f"DEBUG: Partner from request: {partner}")
            
            if not partner:
                raise forms.ValidationError("Partner information is required but not available")
            
            # Determine if this is an update or new package
            is_update = bool(self.instance and self.instance.pk)
            self._is_update = is_update
            
            print(f"DEBUG: Processing {'UPDATE' if is_update else 'NEW'} package")
            print(f"DEBUG: Highlights: {len(cleaned_data.get('highlights', []))} items")
            print(f"DEBUG: Inclusions: {len(cleaned_data.get('inclusions', []))} items")
            print(f"DEBUG: Addons: {len(cleaned_data.get('addons', []))} items")
            
            # Initialize PackageCreationService
            creation_service = PackageCreationService(
                partner=partner,
                source_type="manual",
                updating_package=is_update,
                skip_m2m_processing=True  # Skip M2M processing for admin - let inlines handle it
            )
            print(f"DEBUG: PackageCreationService initialized for {'update' if is_update else 'creation'}")
            
            # Convert form data to raw JSON
            print(f"DEBUG: Converting form data to raw JSON, cleaned_data keys: {list(cleaned_data.keys())}")
            creation_service.convert_form_data_to_raw_data(cleaned_data)
            print(f"DEBUG: Form data conversion completed")
            
            if is_update:
                # For updates, process and validate but don't create new package
                print(f"DEBUG: Starting package processing for update")
                creation_service.process_package()
                print(f"DEBUG: Package processing for update completed")
                
                # Store the service for use in save() method
                self._creation_service = creation_service
                
            else:
                # For new packages, create the package during validation
                print(f"DEBUG: Starting package creation")
                created_package = creation_service.process_package()
                print(f"DEBUG: Package creation completed, created_package ID: {created_package.id if created_package else 'None'}")
                
                if not created_package:
                    raise forms.ValidationError("Package processing failed - no package was created.")
                
                # Store processed data for save() method
                self._processed_package = created_package
                print(f"DEBUG: Stored processed package {created_package.id} for save() method")
            
            # Commit the savepoint if everything succeeded
            transaction.savepoint_commit(savepoint_id)
            print("DEBUG: PackageAdminForm clean() completed successfully, savepoint committed")
            
        except forms.ValidationError as ve:
            # Rollback the savepoint on validation errors
            transaction.savepoint_rollback(savepoint_id)
            print(f"DEBUG: ValidationError in clean(), rolling back: {str(ve)}")
            
            # Just re-raise the original error without cleaning
            raise ve
            
        except Exception as e:
            # Rollback the savepoint on unexpected errors
            transaction.savepoint_rollback(savepoint_id)
            print(f"DEBUG: Unexpected error in clean(), rolling back: {str(e)}")
            raise forms.ValidationError(f"Package processing failed: {str(e)}. Please check your data and try again.")
        
        return cleaned_data

    @transaction.atomic
    def save(self, commit=True):
        print(f"[DEBUG] PackageAdminForm.save() called with commit={commit}")
        logger.info(f"PackageAdminForm.save() called with commit={commit}")
        
        # Get partner using the correct method
        partner = None
        if hasattr(self, 'request') and self.request:
            from packages.admin import get_user_effective_partner
            partner = get_user_effective_partner(self.request)
            partner_name = getattr(partner, 'entity_name', 'Unknown') if partner else 'None'
            print(f"[DEBUG] Partner from request: {partner_name}")
        
        # Check if we have a processed package from clean()
        if hasattr(self, '_processed_package') and self._processed_package:
            print(f"[DEBUG] Using processed package from clean(): ID {self._processed_package.id}")
            instance = self._processed_package
            
            # Update the instance with any additional form data that might have changed
            # (though this shouldn't happen in normal flow since clean() processes everything)
            instance.is_published = self.cleaned_data.get('is_published', instance.is_published)
            instance.is_active = self.cleaned_data.get('is_active', instance.is_active)
            instance.explore_order = self.cleaned_data.get('explore_order', instance.explore_order)
            
            if commit:
                instance.save()
                print(f"[DEBUG] Processed package saved to database")
            
            return instance
        
        # Fallback: if no processed package (shouldn't happen), create manually
        print(f"[DEBUG] No processed package found, creating manually (fallback)")
        
        # Get the package instance using super().save(commit=False) 
        instance = super().save(commit=False)
        print(f"[DEBUG] Form save(commit=False) completed, instance.pk: {instance.pk}")
        
        if instance.pk:
            # UPDATE existing package
            print(f"[DEBUG] Applying update to existing package ID: {instance.pk}")
            
            # Always commit the instance first to make sure it's saved
            if commit:
                instance.save()
                print(f"[DEBUG] Package instance saved to database")
            
            # Apply AI processing to update related objects
            print(f"[DEBUG] Starting AI processing for package update")
            try:
                # Ensure we have a partner for package creation service
                if not partner:
                    print(f"[ERROR] No partner available for package update")
                    raise ValidationError("Cannot update package: No partner information available")
                
                # Create service for updating package
                service = PackageCreationService(
                    partner=partner,
                    source_type="manual",
                    updating_package=True,
                    skip_m2m_processing=True  # Skip M2M processing for admin - let inlines handle it
                )
                
                print(f"[DEBUG] Converting form data to raw data for AI processing")
                # Convert cleaned form data to the expected format
                service.convert_form_data_to_raw_data(self.cleaned_data)
                
                print(f"[DEBUG] Starting package processing for update")
                # Process the package data (validation + AI enhancement)
                validated_data = service.process_package()
                
                print(f"[DEBUG] Package processing for update completed")
                # Apply the validated data to update the package
                service.update_package(instance)
                
                print(f"[DEBUG] Package update completed successfully")
                
                # Refresh virtual fields after successful update
                self._refresh_virtual_fields_after_update()
                
            except Exception as e:
                print(f"[ERROR] Package update failed: {str(e)}")
                logger.error(f"Package update failed: {str(e)}")
                raise ValidationError(f"Failed to update package: {str(e)}")
        else:
            # CREATE new package - this is the problematic case
            print(f"[ERROR] Attempting to create new package without processed data")
            raise ValidationError("Cannot create package: Missing processed data from validation. Please ensure all required fields are filled correctly.")
        
        print(f"[DEBUG] PackageAdminForm.save() completed successfully")
        return instance

    def _refresh_virtual_fields_after_update(self):
        """Force refresh virtual fields with current database state to clear frontend cache"""
        print(f"[DEBUG] _refresh_virtual_fields_after_update called")
        
        # Force reload current data from database
        fresh_highlights = list(self.instance.highlights.values_list('value', flat=True))
        fresh_inclusions = list(self.instance.inclusions.values_list('value', flat=True)) 
        fresh_addons = list(self.instance.addons.values_list('value', flat=True))
        
        print(f"[DEBUG] Fresh database state - highlights: {len(fresh_highlights)}, inclusions: {len(fresh_inclusions)}, addons: {len(fresh_addons)}")
        
        # Update form fields to reflect current database state
        self.fields['highlights'].initial = fresh_highlights
        self.fields['inclusions'].initial = fresh_inclusions  
        self.fields['addons'].initial = fresh_addons
        
        # DON'T update cleaned_data - this was causing the caching issue
        # The cleaned_data should reflect what the user actually submitted, not the database state
        # self.cleaned_data['highlights'] = fresh_highlights
        # self.cleaned_data['inclusions'] = fresh_inclusions
        # self.cleaned_data['addons'] = fresh_addons
        
        print(f"[DEBUG] Virtual fields refreshed with fresh database data (cleaned_data preserved)")

    def is_valid(self):
        """Override is_valid to add comprehensive logging"""
        print(f"[DEBUG] PackageAdminForm.is_valid() called")
        result = super().is_valid()
        print(f"[DEBUG] PackageAdminForm.is_valid() result: {result}")
        if not result:
            print(f"[DEBUG] PackageAdminForm errors: {self.errors}")
            print(f"[DEBUG] PackageAdminForm non_field_errors: {self.non_field_errors()}")
        return result
    
    def full_clean(self):
        """Override full_clean to add comprehensive logging"""
        print(f"[DEBUG] PackageAdminForm.full_clean() called")
        try:
            result = super().full_clean()
            print(f"[DEBUG] PackageAdminForm.full_clean() completed successfully")
            return result
        except Exception as e:
            print(f"[DEBUG] PackageAdminForm.full_clean() exception: {type(e).__name__}: {e}")
            raise

    def clean_type(self):
        """Ensure type is always FIXED"""
        from packages.choices import PackageTypeChoices
        return PackageTypeChoices.FIXED.value


class PackageUploaderForm(forms.ModelForm):
    """Form for uploading package files (JSON/DOC/DOCX) with unified processing service"""
    
    class Meta:
        model = PackageUploader
        fields = ('file',)  # Remove file_type from fields - it will be set internally
    
    def __init__(self, *args, **kwargs):
        self.file = None
        # Extract request from kwargs if passed by admin
        self.request = kwargs.pop('request', None)
        super().__init__(*args, **kwargs)
        
        # Only add help text if field is available and not readonly
        if 'file' in self.fields and not self.fields['file'].widget.attrs.get('readonly'):
            # Add help text with sample formats
            self.fields['file'].help_text = mark_safe(f"""
            <div style="margin-top: 10px;">
                <details>
                    <summary style="cursor: pointer; color: #0066cc; font-weight: bold;">📄 JSON Sample Format</summary>
                    <pre style="background: #f8f9fa; padding: 15px; border-radius: 5px; font-size: 12px; overflow-x: auto; margin-top: 10px;">{get_sample_package_format()}</pre>
                </details>
                <br>
                <details>
                    <summary style="cursor: pointer; color: #0066cc; font-weight: bold;">📄 DOC Sample Format</summary>
                    <pre style="background: #f8f9fa; padding: 15px; border-radius: 5px; font-size: 12px; overflow-x: auto; margin-top: 10px; white-space: pre-wrap;">{get_sample_doc_format()}</pre>
                </details>
            </div>
            """)

    @transaction.atomic
    def clean(self):
        """Validate and process uploaded files"""
        cleaned_data = super().clean()
        file = cleaned_data.get('file')
        
        if not file:
            raise forms.ValidationError("Please select a file to upload")
        
        print(f"DEBUG: Processing file: {file.name}")
        
        # Determine file type based on extension (this is the actual file extension, not package type)
        filename = file.name.lower()
        
        try:
            if filename.endswith('.json'):
                file_extension = 'json'
                print(f"DEBUG: Processing JSON file")
                
                # Validate JSON format
                try:
                    file.seek(0)
                    content = file.read()
                    
                    # Handle bytes vs string
                    if isinstance(content, bytes):
                        content = content.decode('utf-8')
                    
                    # Try to parse JSON to ensure it's valid
                    json_data = json.loads(content)
                    
                    # Check required fields for JSON
                    required_fields = ['title', 'package_no', 'destination', 'owner', 'price_per_person', 'duration']
                    missing_fields = []
                    
                    for field in required_fields:
                        value = json_data.get(field)
                        if not value or (isinstance(value, str) and not value.strip()) or (isinstance(value, list) and len(value) == 0):
                            missing_fields.append(field.replace('_', ' ').title())
                    
                    # Special handling for addons/add_ons - accept either one
                    addons_value = json_data.get('addons') or json_data.get('add_ons')
                    if not addons_value or (isinstance(addons_value, str) and not addons_value.strip()) or (isinstance(addons_value, list) and len(addons_value) == 0):
                        missing_fields.append('Add Ons')
                    
                    if missing_fields:
                        raise forms.ValidationError(f"Required fields are missing: {', '.join(missing_fields)}")
                    
                    print(f"DEBUG: JSON validation successful")
                    
                except json.JSONDecodeError as e:
                    print(f"DEBUG: JSON validation failed: {str(e)}")
                    raise forms.ValidationError(f"Invalid JSON format: {str(e)}")
                except Exception as e:
                    print(f"DEBUG: File processing error: {str(e)}")
                    raise forms.ValidationError(f"Error processing file: {str(e)}")
                    
            elif filename.endswith(('.doc', '.docx')):
                file_extension = 'doc' if filename.endswith('.doc') else 'docx'
                print(f"DEBUG: Processing {file_extension.upper()} file")
                
                # Basic validation - just check that file can be read
                file.seek(0)
                content = file.read()
                if len(content) == 0:
                    raise forms.ValidationError("The uploaded document appears to be empty")
                print(f"DEBUG: Document validation successful")
                
            else:
                raise forms.ValidationError("Please upload a JSON, DOC, or DOCX file")
            
            # Reset file pointer for later processing
            file.seek(0)
            
            # Create uploader instance first (but don't save to DB until processing succeeds)
            if hasattr(self, 'request') and self.request:
                # Get partner for processing
                from packages.admin import get_user_effective_partner
                from packages.choices import PackageTypeChoices
                partner = get_user_effective_partner(self.request)
                
                if not partner:
                    raise forms.ValidationError("Partner information is required but not available")

                # Create uploader instance (but don't save to DB yet)
                # Always set file_type to FIXED for PackageUploaderAdmin (this is package type, not file extension)
                uploader_instance = PackageUploader(
                    file=file,
                    file_type=PackageTypeChoices.FIXED.value,  # Always set to FIXED (this is for package type in DB)
                    partner=partner
                )
                
                # Store for later use in save() method
                self._package_uploader_instance = uploader_instance
                print(f"DEBUG: Created uploader instance for {file_extension.upper()} file")
            
                # Now process file and create package - this will only happen if validation passes
                # Note: removing @transaction.atomic from _create_package to let this transaction control it
                creation_service = PackageCreationService(
                    partner=partner,
                    source_type="file_upload",
                    file=file,
                    file_type=file_extension  # Use actual file extension (docx, json) for processing
                )
                
                file.seek(0)

                # Read file content based on type
                if file_extension.lower() == 'json':
                    try:
                        file_content = json.loads(file.read().decode('utf-8'))
                        creation_service.raw_json_data = file_content
                        print(f"DEBUG: JSON content loaded in clean(): {len(file_content) if file_content else 0} keys")
                    except json.JSONDecodeError as e:
                        raise forms.ValidationError(f"Invalid JSON file: {str(e)}")
                    except UnicodeDecodeError as e:
                        raise forms.ValidationError(f"File encoding error: {str(e)}")
                else:
                    # For DOC/DOCX, store binary content
                    creation_service.raw_json_data = file.read()
                    print(f"DEBUG: Binary content loaded in clean() for {file_extension.upper()}: {len(creation_service.raw_json_data)} bytes")
                
                # Process and create package within this transaction
                print(f"DEBUG: Starting package processing in clean() for {file_extension.upper()} file")
                created_package = creation_service.process_package()
                print(f"DEBUG: Package processing completed in clean(): {created_package.id if created_package else 'None'}")
                
                if not created_package:
                    raise forms.ValidationError("Package processing failed - no package was created.")
                
                # Store processed data for save() method - only set if everything succeeded
                self._processed_package = created_package
                print(f"DEBUG: Stored processed package {created_package.id} for save() method")
                
                # Reset file pointer for later use
                file.seek(0)
                
            print("DEBUG: clean() completed successfully")
                
        except forms.ValidationError as ve:
            print(f"DEBUG: ValidationError in clean(): {str(ve)}")
            
            # Even if package creation failed, we should still create the uploader instance
            # so that the upload attempt is recorded and user can see what went wrong
            if not hasattr(self, '_package_uploader_instance'):
                print(f"DEBUG: Creating uploader instance even though package creation failed")
                file = cleaned_data.get('file')
                
                if file and hasattr(self, 'request') and self.request:
                    try:
                        from packages.admin import get_user_effective_partner
                        from packages.choices import PackageTypeChoices
                        partner = get_user_effective_partner(self.request)
                        
                        uploader_instance = PackageUploader(
                            file=file,
                            file_type=PackageTypeChoices.FIXED.value,  # Always FIXED for package type
                            partner=partner
                        )
                        self._package_uploader_instance = uploader_instance
                        print(f"DEBUG: Created uploader instance for failed upload attempt")
                    except Exception as e:
                        print(f"DEBUG: Failed to create uploader instance for failed upload: {str(e)}")
            
            # Store the error for display but don't prevent form from saving
            self._validation_error = str(ve)
            print(f"DEBUG: Stored validation error for later display: {self._validation_error}")
            
            # Add error to the form's error dict for display
            if 'file' not in self.errors:
                self.add_error('file', str(ve))
            
            # Return cleaned data without raising - let the form save so uploader instance is created
            print("DEBUG: Returning cleaned data despite validation error to allow uploader creation")
            return cleaned_data
        
        except Exception as e:
            print(f"DEBUG: Unexpected error in clean(): {str(e)}")
            raise forms.ValidationError(f"File processing failed: {str(e)}. Please check your file format and try again.")
        
        return cleaned_data

    @transaction.atomic
    def save(self, commit=True):
        """Save uploader instance and connect to already-created package - SIMPLIFIED"""
        print("DEBUG: PackageUploaderForm.save() called")
        
        # Get the instance - this should have file from form data
        instance = super().save(commit=False)
        
        # Always set file_type to FIXED (this is the package type, not file extension)
        from packages.choices import PackageTypeChoices
        instance.file_type = PackageTypeChoices.FIXED.value
        print(f"DEBUG: Set file_type to FIXED: {instance.file_type}")
        
        # Set partner - always required
        if hasattr(self, 'request') and self.request:
            from packages.admin import get_user_effective_partner
            instance.partner = get_user_effective_partner(self.request)
            print(f"DEBUG: Set partner: {instance.partner}")
        
        # Store the processed package for later linking
        if hasattr(self, '_processed_package') and self._processed_package:
            instance._processed_package_for_linking = self._processed_package
            print(f"DEBUG: Stored processed package {self._processed_package.id} on instance for later linking")
        
        # ALWAYS save the uploader instance first to get an ID
        if commit:
            instance.save()
            print(f"DEBUG: Saved PackageUploader instance with ID: {instance.pk}")
            
            # Handle linking immediately if we have a processed package
            self._link_package_to_uploader(instance)
        else:
            print(f"DEBUG: Commit=False, storing instance for later save")
            # Store the linking function for later execution
            original_save = instance.save
            
            def enhanced_save(*args, **kwargs):
                print(f"DEBUG: Enhanced save called on uploader instance")
                result = original_save(*args, **kwargs)
                print(f"DEBUG: PackageUploader saved with ID: {instance.pk}")
                # Perform linking after save
                self._link_package_to_uploader(instance)
                return result
            
            instance.save = enhanced_save
        
        print(f"DEBUG: PackageUploaderForm.save() completed successfully with instance ID: {instance.pk if instance.pk else 'not saved yet'}")
        return instance
    
    def _link_package_to_uploader(self, uploader_instance):
        """Link the processed package to the uploader instance"""
        if hasattr(uploader_instance, '_processed_package_for_linking'):
            package = uploader_instance._processed_package_for_linking
            try:
                print(f"DEBUG: Attempting to link package {package.id} to uploader {uploader_instance.id}")
                
                # Update the package's package_uploaded field
                package.package_uploaded = uploader_instance
                package.save(update_fields=['package_uploaded'])
                
                print(f"DEBUG: ✅ SUCCESS - Connected package {package.id} to uploader {uploader_instance.id}")
                
                # Verify the link was saved
                package.refresh_from_db()
                if package.package_uploaded and package.package_uploaded.id == uploader_instance.id:
                    print(f"DEBUG: ✅ VERIFIED - Package-Uploader link saved correctly in database")
                else:
                    print(f"DEBUG: ❌ ERROR - Package-Uploader link not saved correctly")
                    
                # Clean up the temporary attribute
                delattr(uploader_instance, '_processed_package_for_linking')
                
            except Exception as e:
                print(f"DEBUG: ❌ ERROR connecting package to uploader: {str(e)}")
                # Don't raise here since the uploader is already saved
                # Just log the error
        else:
            print(f"DEBUG: No processed package to connect - uploader saved standalone")

    def _check_file_changed(self):
        """Check if the file field has actually changed"""
        if not self.instance.pk:
            return True  # New instance, file is definitely "changed"
        
        # For existing instances, check if file field was updated
        if 'file' in self.changed_data:
            return True
        
        return False


# Rest of the existing forms remain the same...
class MonthCheckboxSelectMultiple(forms.CheckboxSelectMultiple):
    """Custom widget for month selection with checkboxes"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.choices = MonthChoices.choices


class CategoryAdminForm(forms.ModelForm):
    """Custom form for Category admin"""
    
    class Meta:
        model = Category
        exclude = ['partner']  # Hide partner field from admin form
    
    def __init__(self, *args, **kwargs):
        # Extract request from kwargs if passed by admin
        self.request = kwargs.pop('request', None)
        super().__init__(*args, **kwargs)
        
        # Customize field appearance
        self.fields['title'].help_text = "Category name (will be converted to lowercase)"
        self.fields['description'].help_text = "Brief description of this category"

    def clean_title(self):
        """Clean and validate title - unique per partner"""
        title = self.cleaned_data.get('title', '').strip().lower()
        
        if not title:
            raise ValidationError("Category title is required.")
        
        # Get partner context
        if hasattr(self, 'request') and self.request:
            from packages.admin import get_user_effective_partner
            partner = get_user_effective_partner(self.request)
        elif self.instance.pk and self.instance.partner:
            partner = self.instance.partner
        else:
            # Fallback - shouldn't happen in normal admin usage
            partner = None
        
        if partner:
            # Check for duplicate categories within the same partner
            existing_filter = {'title': title, 'partner': partner}
            if self.instance.pk:
                # Editing existing category
                existing = Category.objects.filter(**existing_filter).exclude(pk=self.instance.pk)
            else:
                # Creating new category
                existing = Category.objects.filter(**existing_filter)
            
            if existing.exists():
                raise ValidationError(f"Category '{title}' already exists for your organization.")
        
        return title

    def clean(self):
        """Validate that media will be present after saving"""
        cleaned_data = super().clean()
        
        # For existing categories, check if they currently have media
        # We can't check for new media being uploaded here since we don't have access to formsets
        # That validation will be handled at the admin level through custom validation
        if self.instance.pk and not self.instance.media.exists():
            # This will only trigger for existing categories that currently have no media
            # If media is being uploaded, the formset will handle it properly
            pass  # We'll handle this with JavaScript or admin-level validation
        
        return cleaned_data


class DestinationAdminForm(forms.ModelForm):
    """Custom form for Destination admin with month checkboxes"""
    
    best_time_to_visit = forms.MultipleChoiceField(
        choices=MonthChoices.choices,
        widget=MonthCheckboxSelectMultiple,
        required=False,
        help_text="Select the months when it's best to visit this destination"
    )
    
    class Meta:
        model = Destination
        exclude = ['partner']  # Hide partner field from admin form
    
    def __init__(self, *args, **kwargs):
        # Extract request from kwargs if passed by admin
        self.request = kwargs.pop('request', None)
        super().__init__(*args, **kwargs)
        
        # Customize field appearance
        self.fields['title'].help_text = "Destination name (will be converted to lowercase)"
        self.fields['description'].help_text = "Brief description of this destination"

    def clean_title(self):
        """Clean and validate title - unique per partner"""
        title = self.cleaned_data.get('title', '').strip().lower()
        
        if not title:
            raise ValidationError("Destination title is required.")
        
        # Get partner context
        if hasattr(self, 'request') and self.request:
            from packages.admin import get_user_effective_partner
            partner = get_user_effective_partner(self.request)
        elif self.instance.pk and self.instance.partner:
            partner = self.instance.partner
        else:
            # Fallback - shouldn't happen in normal admin usage
            partner = None
        
        if partner:
            # Check for duplicate destinations within the same partner
            existing_filter = {'title': title, 'partner': partner}
            if self.instance.pk:
                # Editing existing destination
                existing = Destination.objects.filter(**existing_filter).exclude(pk=self.instance.pk)
            else:
                # Creating new destination
                existing = Destination.objects.filter(**existing_filter)
            
            if existing.exists():
                raise ValidationError(f"Destination '{title}' already exists for your organization.")
        
        return title

    def clean_best_time_to_visit(self):
        """Convert selected months to list of integers"""
        months = self.cleaned_data.get('best_time_to_visit', [])
        if months:
            try:
                return [int(month) for month in months]
            except (ValueError, TypeError):
                raise ValidationError("Invalid month selection.")
        return []

    def clean(self):
        """Validate that media will be present after saving"""
        cleaned_data = super().clean()
        
        # For existing destinations, check if they currently have media
        # We can't check for new media being uploaded here since we don't have access to formsets
        # That validation will be handled at the admin level through custom validation
        if self.instance.pk and not self.instance.media.exists():
            # This will only trigger for existing destinations that currently have no media
            # If media is being uploaded, the formset will handle it properly
            pass  # We'll handle this with JavaScript or admin-level validation
        
        return cleaned_data

    def save(self, commit=True):
        """Save with proper best_time_to_visit formatting"""
        instance = super().save(commit=False)
        instance.best_time_to_visit = self.cleaned_data.get('best_time_to_visit', [])
        if commit:
            instance.save()
        return instance


class ActivityAdminForm(forms.ModelForm):
    """Custom form for Activity admin"""
    
    class Meta:
        model = Activity
        exclude = ['partner']  # Hide partner field from admin form
    
    def __init__(self, *args, **kwargs):
        # Extract request from kwargs if passed by admin
        self.request = kwargs.pop('request', None)
        super().__init__(*args, **kwargs)
        
        # Customize field appearance
        self.fields['title'].help_text = "Activity name (will be converted to lowercase)"
        self.fields['description'].help_text = "Brief description of this activity"

    def clean_title(self):
        """Clean and validate title - unique per partner"""
        title = self.cleaned_data.get('title', '').strip().lower()
        
        if not title:
            raise ValidationError("Activity title is required.")
        
        # Get partner context
        if hasattr(self, 'request') and self.request:
            from packages.admin import get_user_effective_partner
            partner = get_user_effective_partner(self.request)
        elif self.instance.pk and self.instance.partner:
            partner = self.instance.partner
        else:
            # Fallback - shouldn't happen in normal admin usage
            partner = None
        
        if partner:
            # Check for duplicate activities within the same partner
            existing_filter = {'title': title, 'partner': partner}
            if self.instance.pk:
                # Editing existing activity
                existing = Activity.objects.filter(**existing_filter).exclude(pk=self.instance.pk)
            else:
                # Creating new activity
                existing = Activity.objects.filter(**existing_filter)
            
            if existing.exists():
                raise ValidationError(f"Activity '{title}' already exists for your organization.")
        
        return title

    def clean(self):
        """Validate that media will be present after saving"""
        cleaned_data = super().clean()
        
        # For existing activities, check if they currently have media
        # We can't check for new media being uploaded here since we don't have access to formsets
        # That validation will be handled at the admin level through custom validation
        if self.instance.pk and not self.instance.media.exists():
            # This will only trigger for existing activities that currently have no media
            # If media is being uploaded, the formset will handle it properly
            pass  # We'll handle this with JavaScript or admin-level validation
        
        return cleaned_data


class DestinationFaqAdminForm(forms.ModelForm):
    """Custom form for DestinationFaq admin"""
    
    class Meta:
        model = DestinationFaq
        exclude = ['partner']  # Hide partner field from admin form
    
    def __init__(self, *args, **kwargs):
        # Extract request from kwargs if passed by admin
        self.request = kwargs.pop('request', None)
        super().__init__(*args, **kwargs)
        
        # Set help text
        self.fields['destination'].help_text = 'Select the destination for this FAQ'
        self.fields['question'].help_text = 'Enter the frequently asked question'
        self.fields['answer'].help_text = 'Provide a comprehensive answer to the question'
        self.fields['is_published'].help_text = 'Check to make this FAQ visible to users'
        self.fields['priority'].help_text = 'Order for displaying FAQs (higher numbers appear first)'
        
        # Filter destinations by partner if we have request context
        if hasattr(self, 'request') and self.request:
            from packages.admin import get_user_effective_partner
            partner = get_user_effective_partner(self.request)
            
            if partner:
                # Only show destinations belonging to the user's partner
                self.fields['destination'].queryset = Destination.objects.filter(
                    partner=partner, 
                    is_active=True
                ).order_by('title')
            else:
                # No partner context, show no destinations
                self.fields['destination'].queryset = Destination.objects.none()


class ActivityMediaAdminForm(forms.ModelForm):
    """Custom form for ActivityMedia admin with main_display validation"""
    
    class Meta:
        model = ActivityMedia
        fields = ['media', 'main_display']
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['media'].help_text = "Upload image file (jpg, jpeg, png, webp)"
        self.fields['main_display'].help_text = "Check to set as main display image (only one allowed per activity)"

    def clean(self):
        """Validate main_display field"""
        cleaned_data = super().clean()
        main_display = cleaned_data.get('main_display', False)
        
        # Only validate if main_display is True and we have an activity
        if main_display and hasattr(self.instance, 'activity') and self.instance.activity:
            from packages.models import ActivityMedia
            
            existing_main = ActivityMedia.objects.filter(
                activity=self.instance.activity,
                main_display=True
            )
            
            # Exclude current instance if editing
            if self.instance.pk:
                existing_main = existing_main.exclude(pk=self.instance.pk)
            
            if existing_main.exists():
                self.add_error('main_display', 'Only one media file per activity can be set as main display')
        
        return cleaned_data


class CustomActivityAdminForm(forms.ModelForm):
    """Custom form for CustomActivity admin to handle persona multiselect field"""
    
    persona = forms.MultipleChoiceField(
        choices=PersonaChoices.choices,
        widget=forms.CheckboxSelectMultiple,
        required=False,
        help_text="Select one or more personas this activity is suitable for"
    )
    
    # Array field for addons with proper widget
    # addons = ClearableDynamicArrayField(
    #     forms.CharField(max_length=500),
    #     widget=DynamicArrayWidget(),
    #     required=False,
    # )
    
    class Meta:
        model = CustomActivity
        fields = '__all__'
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # If this is an existing instance, set initial persona values
        if self.instance and self.instance.pk and self.instance.persona:
            self.fields['persona'].initial = self.instance.persona
    
    def save(self, commit=True):
        instance = super().save(commit=False)
        
        # Save the persona field as a list
        persona_data = self.cleaned_data.get('persona', [])
        instance.persona = list(persona_data) if persona_data else []
        
        if commit:
            instance.save()
        
        return instance



"""Custom Package Admin Forms"""

class InclusionsField(forms.CharField):
    """Custom CharField for inclusions that handles list/JSON data properly"""
    
    def prepare_value(self, value):
        """Prepare value for display in the widget"""
        if value is None:
            return ''
        
        # If it's already a string, check if it needs conversion
        if isinstance(value, str):
            try:
                import json
                # Try to parse as JSON
                parsed_data = json.loads(value)
                if isinstance(parsed_data, list):
                    clean_items = [str(item).strip() for item in parsed_data if item and str(item).strip()]
                    return ', '.join(clean_items) if clean_items else ''
                else:
                    return str(parsed_data).strip()
            except (json.JSONDecodeError, TypeError):
                # Not JSON, return as-is
                return str(value).strip()
        
        # If it's a list, convert to comma-separated
        elif isinstance(value, list):
            clean_items = [str(item).strip() for item in value if item and str(item).strip()]
            return ', '.join(clean_items) if clean_items else ''
        
        # Any other type, convert to string
        else:
            return str(value).strip() if value else ''


class ItineraryInlineForm(forms.ModelForm):
    """Form for Itinerary inline"""
    
    # Explicitly define inclusions field to override model defaults
    inclusions = InclusionsField(
        widget=forms.Textarea(attrs={'rows': 3, 'placeholder': 'Enter inclusions separated by commas: Flight, Hotel, Breakfast'}),
        required=False,
        help_text='What is included on this day, separated by commas: Flight, Hotel, Breakfast'
    )
    
    class Meta:
        model = Itinerary
        fields = ['day_number', 'date', 'day_title', 'description', 'order', 'inclusions']
        widgets = {
            'date': forms.DateInput(attrs={'type': 'date'}),
            'description': forms.Textarea(attrs={'rows': 3}),
        }

    def __init__(self, *args, **kwargs):
        logger.info("ItineraryInlineForm.__init__ called")
        logger.debug(f"ItineraryInlineForm.__init__ args: {len(args)}, kwargs keys: {list(kwargs.keys())}")
        
        super().__init__(*args, **kwargs)
        
        # Log the instance data for debugging
        if self.instance and self.instance.pk and self.instance.inclusions:
            logger.info(f"ItineraryInlineForm.__init__ instance inclusions: {self.instance.inclusions}")
            logger.debug(f"ItineraryInlineForm.__init__ inclusions type: {type(self.instance.inclusions)}")
        
        # Add help texts
        help_texts = {
            'day_number': 'Day number in the package (1, 2, 3, etc.)',
            'date': 'Specific date for this itinerary day (optional)',
            'day_title': 'Title for this day (e.g., "Arrival Day", "City Tour")',
            'description': 'Overview description of this day',
            'order': 'Order of this day in the itinerary',
            'inclusions': 'What is included on this day, separated by commas: Flight, Hotel, Breakfast',
        }
        
        for field_name, help_text in help_texts.items():
            if field_name in self.fields:
                self.fields[field_name].help_text = help_text
        
        logger.info("ItineraryInlineForm.__init__ completed successfully")
    
    def clean_inclusions(self):
        """Clean and validate inclusions field - convert comma-separated to list"""
        inclusions_str = self.cleaned_data.get('inclusions', '').strip()
        
        if not inclusions_str:
            return []
        
        # Split by comma and clean up each item
        inclusions = [item.strip() for item in inclusions_str.split(',') if item.strip()]
        return inclusions
    
    def clean(self):
        logger.info("ItineraryInlineForm.clean called")
        cleaned_data = super().clean()
        logger.debug(f"ItineraryInlineForm.clean cleaned_data keys: {list(cleaned_data.keys())}")
        
        # Log inclusions data specifically
        inclusions = cleaned_data.get('inclusions', [])
        logger.info(f"ItineraryInlineForm.clean inclusions: {len(inclusions)} items - {inclusions}")
        
        logger.info("ItineraryInlineForm.clean completed successfully")
        return cleaned_data
    
    def save(self, commit=True):
        logger.info(f"ItineraryInlineForm.save called with commit={commit}")
        logger.debug(f"ItineraryInlineForm.save instance pk: {self.instance.pk if self.instance else 'None'}")
        
        instance = super().save(commit=False)
        
        # Ensure partner is set before saving (same pattern as other forms)
        if not instance.partner_id:
            # Try to get partner from the package
            if hasattr(instance, 'package') and instance.package and hasattr(instance.package, 'partner_id') and instance.package.partner_id:
                instance.partner_id = instance.package.partner_id
                logger.debug(f"ItineraryInlineForm.save setting partner from package: {instance.package.partner_id}")
            else:
                # This should not happen in normal flow, but just in case
                logger.error("ItineraryInlineForm.save: No partner available for itinerary")
                raise ValueError("No partner available for saving itinerary")
        
        if commit:
            instance.save()
        
        logger.info(f"ItineraryInlineForm.save completed successfully, instance pk: {instance.pk if instance else 'None'}")
        return instance


class ItineraryDayItemInlineForm(forms.ModelForm):
    """Form for ItineraryDayItem inline"""
    
    # Direct fields for hotel and activity selection with infinite scroll widgets
    hotel_selection = DynamicModelChoiceField(
        model_class=Hotel,
        queryset=Hotel.objects.none(),  # Will be managed by widget
        required=False,
        label="Hotel",
        help_text="Search and select a hotel for this day item",
        widget=InfiniteScrollSelectWidget(
            model_class=Hotel,
            search_field='name',
            display_field='name'
        )
    )
    
    activity_selection = DynamicModelChoiceField(
        model_class=CustomActivity,
        queryset=CustomActivity.objects.none(),  # Will be managed by widget
        required=False,
        label="Activity", 
        help_text="Search and select an activity for this day item",
        widget=InfiniteScrollSelectWidget(
            model_class=CustomActivity,
            search_field='title',
            display_field='title'
        )
    )
    
    # Explicitly define inclusions field to override model defaults
    inclusions = InclusionsField(
        widget=forms.Textarea(attrs={'rows': 3, 'placeholder': 'Enter inclusions separated by commas: Flight, Hotel, Breakfast'}),
        required=False,
        help_text='What is included in this item, separated by commas: Flight, Hotel, Breakfast'
    )
    
    class Meta:
        model = ItineraryDayItem
        fields = ['type', 'title', 'description', 'order', 'duration', 'hotel_selection', 'activity_selection', 'inclusions']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3}),
        }

    def __init__(self, *args, **kwargs):
        logger.info("ItineraryDayItemInlineForm.__init__ called")
        logger.debug(f"ItineraryDayItemInlineForm.__init__ args: {len(args)}, kwargs keys: {list(kwargs.keys())}")
        
        super().__init__(*args, **kwargs)
        
        # Set initial values for existing items
        if self.instance and self.instance.pk:
            if self.instance.hotel and self.instance.hotel.hotel:
                self.fields['hotel_selection'].initial = self.instance.hotel.hotel
                
            if self.instance.activity and self.instance.activity.activity:
                self.fields['activity_selection'].initial = self.instance.activity.activity
        
        # Update help text to mention infinite scroll functionality
        self.fields['hotel_selection'].help_text = 'Type to search hotels. Results load as you scroll.'
        self.fields['activity_selection'].help_text = 'Type to search activities. Results load as you scroll.'
        
        logger.debug("ItineraryDayItemInlineForm.__init__ completed successfully with infinite scroll widgets")

    def clean_inclusions(self):
        """Clean and validate inclusions field - convert comma-separated to list"""
        inclusions_str = self.cleaned_data.get('inclusions', '').strip()
        
        if not inclusions_str:
            return []
        
        # Split by comma and clean up each item
        inclusions = [item.strip() for item in inclusions_str.split(',') if item.strip()]
        return inclusions

    def clean(self):
        """Validate itinerary day item data"""
        logger.info("ItineraryDayItemInlineForm.clean called")
        cleaned_data = super().clean()
        logger.debug(f"ItineraryDayItemInlineForm.clean cleaned_data keys: {list(cleaned_data.keys())}")
        
        item_type = cleaned_data.get('type')
        hotel_selection = cleaned_data.get('hotel_selection')
        activity_selection = cleaned_data.get('activity_selection')
        
        logger.info(f"ItineraryDayItemInlineForm.clean item_type: {item_type}")
        logger.info(f"ItineraryDayItemInlineForm.clean hotel_selection: {hotel_selection}")
        logger.info(f"ItineraryDayItemInlineForm.clean activity_selection: {activity_selection}")
        
        # Log inclusions data specifically
        inclusions = cleaned_data.get('inclusions', [])
        logger.info(f"ItineraryDayItemInlineForm.clean inclusions: {len(inclusions)} items - {inclusions}")
        
        # Validate that only one of hotel or activity is selected
        if hotel_selection and activity_selection:
            self.add_error('hotel_selection', 'Cannot select both hotel and activity for the same item')
            self.add_error('activity_selection', 'Cannot select both hotel and activity for the same item')
        
        logger.info("ItineraryDayItemInlineForm.clean completed successfully")
        return cleaned_data
    
    def save(self, commit=True):
        logger.info(f"ItineraryDayItemInlineForm.save called with commit={commit}")
        logger.debug(f"ItineraryDayItemInlineForm.save instance pk: {self.instance.pk if self.instance else 'None'}")
        
        instance = super().save(commit=False)
        
        # Handle hotel selection
        hotel_selection = self.cleaned_data.get('hotel_selection')
        if hotel_selection:
            # Create or update ItineraryHotel
            from packages.models import ItineraryHotel
            if instance.hotel:
                # Update existing
                instance.hotel.hotel = hotel_selection
                instance.hotel.save()
            else:
                # Create new
                itinerary_hotel = ItineraryHotel.objects.create(hotel=hotel_selection)
                instance.hotel = itinerary_hotel
        elif instance.hotel:
            # Remove hotel if none selected
            instance.hotel.delete()
            instance.hotel = None
        
        # Handle activity selection  
        activity_selection = self.cleaned_data.get('activity_selection')
        if activity_selection:
            # Create or update ItineraryActivity
            from packages.models import ItineraryActivity
            if instance.activity:
                # Update existing
                instance.activity.activity = activity_selection
                instance.activity.save()
            else:
                # Create new
                itinerary_activity = ItineraryActivity.objects.create(activity=activity_selection)
                instance.activity = itinerary_activity
        elif instance.activity:
            # Remove activity if none selected
            instance.activity.delete()
            instance.activity = None
        
        if commit:
            # Ensure partner is set before saving
            if not instance.partner_id:
                # Try to get partner from the itinerary
                if hasattr(instance, 'itinerary') and instance.itinerary and hasattr(instance.itinerary, 'partner_id') and instance.itinerary.partner_id:
                    instance.partner_id = instance.itinerary.partner_id
                else:
                    # This should not happen in normal flow, but just in case
                    logger.error("ItineraryDayItemInlineForm.save: No partner available for itinerary day item")
                    raise ValueError("No partner available for saving itinerary day item")
            instance.save()
        
        logger.info(f"ItineraryDayItemInlineForm.save completed successfully, instance pk: {instance.pk if instance else 'None'}")
        return instance


class CustomPackageForm(forms.ModelForm):
    """
    Form for CustomPackage creation and editing with helper class integration.
    Simple approach with hyperlink-based itinerary management.
    """

    # Virtual array fields for the admin interface
    highlights = ClearableDynamicArrayField(
        forms.CharField(max_length=500),
        widget=DynamicArrayWidget(),
        required=True,
        help_text='Package highlights (one per line) - Icons will be auto-mapped by AI'
    )
    
    inclusions = ClearableDynamicArrayField(
        forms.CharField(max_length=500),
        widget=DynamicArrayWidget(),
        required=True,
        help_text='What is included in the package (one per line) - Icons will be auto-mapped by AI'
    )
    
    addons = ClearableDynamicArrayField(
        forms.CharField(max_length=500),
        widget=DynamicArrayWidget(),
        required=False,
        help_text='Add On services like Flight, Visa, Insurance, Upgrades (one per line) - Icons will be auto-mapped by AI'
    )
    
    visa_type = ClearableDynamicArrayField(
        forms.CharField(max_length=255),
        widget=DynamicArrayWidget(),
        required=False,
        help_text='Types of visa available (one per line)'
    )
    
    class Meta:
        model = CustomPackage
        fields = '__all__'
        exclude = ['partner', 'currency', 'price', 'package_uploaded', 'best_time_to_visit_months']
    
    def __init__(self, *args, **kwargs):
        # Extract request from kwargs if passed by admin
        logger.info("CustomPackageForm.__init__ called")
        logger.debug(f"CustomPackageForm.__init__ args: {len(args)}, kwargs keys: {list(kwargs.keys())}")
        
        self.request = kwargs.pop('request', None)
        logger.debug(f"CustomPackageForm.__init__ request: {self.request is not None}")
        
        super().__init__(*args, **kwargs)
        
        # Set partner and type fields programmatically
        if self.request:
            effective_partner = self._get_effective_partner()
            logger.debug(f"CustomPackageForm.__init__ effective_partner: {effective_partner}")
            
            # Filter destination choices by partner
            if 'destination' in self.fields:
                from packages.models import Destination
                destinations = Destination.objects.filter(
                    partner=effective_partner,
                    is_active=True
                ).order_by('title')
                logger.debug(f"CustomPackageForm.__init__ filtered to {destinations.count()} destinations")
                self.fields['destination'].queryset = destinations
        
        # Add custom help texts
        self._add_custom_help_texts()
        
        # Set the type field to readonly and CUSTOM_ADMIN
        if hasattr(self.instance, 'type'):
            logger.debug(f"CustomPackageForm.__init__ setting type to CUSTOM_ADMIN")
            self.instance.type = PackageTypeChoices.CUSTOM_ADMIN
        
        # Initialize virtual fields with existing data
        if self.instance and self.instance.pk:
            highlights = [h.value for h in self.instance.highlights.all()]
            inclusions = [i.value for i in self.instance.inclusions.all()]
            addons = [a.value for a in self.instance.addons.all()]
            
            logger.info(f"CustomPackageForm.__init__ initializing virtual fields - highlights: {len(highlights)}, inclusions: {len(inclusions)}, addons: {len(addons)}")
            
            self.fields['highlights'].initial = highlights
            self.fields['inclusions'].initial = inclusions
            self.fields['addons'].initial = addons
        
        logger.info("CustomPackageForm.__init__ completed successfully")
    
    def _get_effective_partner(self):
        """Get the effective partner for the current user"""
        if not self.request or not hasattr(self.request, 'user'):
            return None
        
        user = self.request.user
        if hasattr(user, 'partner'):
            return user.partner
        elif hasattr(user, 'partner_users') and user.partner_users.exists():
            return user.partner_users.first().partner
        return None
    
    def _add_custom_help_texts(self):
        """Add custom help texts for fields"""
        help_texts = {
            'title': 'Descriptive package title (e.g., "Golden Triangle Tour with Rajasthan")',
            'package_no': 'Unique package identifier (e.g., "GT001", "RAJ2024")',
            'duration': 'Duration in format: 4N & 5D (4 Nights & 5 Days)',
            'duration_in_nights': 'Number of nights (e.g., 4 for a 4N & 5D package)',
            'duration_in_days': 'Number of days (e.g., 5 for a 4N & 5D package)',
            'price_per_person': 'Price with currency symbol or code. Examples: ₹39,999 | INR 39999 | $500 | USD 500 | €450 | EUR 450',
            'currency_conversion_rate': 'Current conversion rate information (optional)',
            'owner': 'Package owner/creator name',
            'about_this_tour': 'Detailed description of the package and what makes it special',
            'exclusions': 'What is NOT included in the package (one per line)',
            'visa_type': 'Visa requirements and types (one per line)',
            'best_time_to_visit': 'Best time of year to visit this destination',
            'destination_safety': 'Safety information and tips for travelers',
            'rating': 'Package rating (1.0 to 5.0)',
            'rating_description': 'Brief description about the package rating',
            # 'hotels': 'Recommended hotels (one per line)',
            'popular_restaurants': 'Recommended restaurants (one per line)',
            'popular_activities': 'Popular activities in this package (one per line)',
            'cultural_info': 'Cultural information and etiquette',
            'what_to_shop': 'Shopping recommendations and specialties',
            'what_to_pack': 'Packing recommendations and essential items',
            'important_notes': 'Important notes and reminders (one per line)',
        }
        
        for field_name, help_text in help_texts.items():
            if field_name in self.fields:
                self.fields[field_name].help_text = help_text

    def clean_duration(self):
        """Validate duration format"""
        duration = self.cleaned_data.get('duration', '')
        if duration:
            # Validate format: XN & YD
            import re
            pattern = r'^\d+N\s*&\s*\d+D$'
            if not re.match(pattern, duration.strip()):
                raise forms.ValidationError(
                    "Duration must be in format '4N & 5D' (4 Nights & 5 Days)"
                )
        return duration

    def clean_price_per_person(self):
        """Extract currency and price from price_per_person field"""
        price_per_person = self.cleaned_data.get('price_per_person', '').strip()
        
        if not price_per_person:
            raise forms.ValidationError("Price per person is required")
        
        # Regex to extract currency symbol/code and numeric price
        import re
        
        # Common currency patterns
        currency_patterns = [
            # Currency symbol at start: ₹39,999 or $500 or €450
            r'^([₹$€£¥₦₽₪₩₴₨₪₩₴₨])\s*([0-9,]+(?:\.[0-9]{1,2})?)$',
            # Currency code at start: INR 39,999 or USD 500 or EUR 450
            r'^([A-Z]{3})\s+([0-9,]+(?:\.[0-9]{1,2})?)$',
            # Currency symbol at end: 39,999₹ or 500$ or 450€
            r'^([0-9,]+(?:\.[0-9]{1,2})?)\s*([₹$€£¥₦₽₪₩₴₨₪₩₴₨])$',
            # Currency code at end: 39,999 INR or 500 USD or 450 EUR
            r'^([0-9,]+(?:\.[0-9]{1,2})?)\s+([A-Z]{3})$',
            # Just number (assume default currency)
            r'^([0-9,]+(?:\.[0-9]{1,2})?)$'
        ]
        
        currency = None
        price = None
        
        for pattern in currency_patterns:
            match = re.match(pattern, price_per_person)
            if match:
                groups = match.groups()
                if len(groups) == 2:
                    # Check if first group is currency or price
                    first_group = groups[0]
                    second_group = groups[1]
                    
                    # If first group contains letters or currency symbols, it's currency
                    if re.match(r'^[₹$€£¥₦₽₪₩₴₨₪₩₴₨A-Z]+$', first_group):
                        currency = first_group
                        price_str = second_group
                    else:
                        # First group is price, second is currency
                        price_str = first_group
                        currency = second_group
                elif len(groups) == 1:
                    # Just price, no currency specified
                    price_str = groups[0]
                    currency = '₹'  # Default to INR
                
                # Clean price string (remove commas)
                price_str = price_str.replace(',', '')
                
                try:
                    price = float(price_str)
                    break
                except ValueError:
                    continue
        
        if currency is None or price is None:
            raise forms.ValidationError(
                "Invalid price format. Please use format like: ₹39,999 or INR 39999 or $500 or USD 500"
            )
        
        # Store extracted values for use in clean()
        self._extracted_currency = currency
        self._extracted_price = price
        
        return price_per_person

    def clean(self):
        """Custom validation for the entire form"""
        logger.info("CustomPackageForm.clean called")
        cleaned_data = super().clean()
        logger.debug(f"CustomPackageForm.clean cleaned_data keys: {list(cleaned_data.keys())}")
        
        # Set the partner automatically
        if self.request:
            effective_partner = self._get_effective_partner()
            logger.debug(f"CustomPackageForm.clean setting partner: {effective_partner}")
            if effective_partner:
                cleaned_data['partner'] = effective_partner
        
        # Ensure type is CUSTOM_ADMIN
        logger.debug("CustomPackageForm.clean setting type to CUSTOM_ADMIN")
        cleaned_data['type'] = PackageTypeChoices.CUSTOM_ADMIN.value
        
        # Extract currency and price from price_per_person
        if hasattr(self, '_extracted_currency') and hasattr(self, '_extracted_price'):
            cleaned_data['currency'] = self._extracted_currency
            cleaned_data['price'] = self._extracted_price
            logger.debug(f"CustomPackageForm.clean extracted currency: {self._extracted_currency}, price: {self._extracted_price}")
        
        # # Validate required fields
        # required_fields = ['title', 'package_no', 'destination', 'duration', 'price_per_person', 'about_this_tour', 'duration_in_nights', 'duration_in_days']
        # missing_fields = []
        # for field in required_fields:
        #     if not cleaned_data.get(field):
        #         missing_fields.append(field)
        #         logger.warning(f"CustomPackageForm.clean missing required field: {field}")
        
        # if missing_fields:
        #     for field in missing_fields:
        #         self.add_error(field, f"{field.replace('_', ' ').title()} is required")
        
        # Validate highlights and inclusions
        highlights = cleaned_data.get('highlights', [])
        inclusions = cleaned_data.get('inclusions', [])
        
        logger.info(f"CustomPackageForm.clean validating highlights: {len(highlights)} items")
        logger.info(f"CustomPackageForm.clean validating inclusions: {len(inclusions)} items")
        
        if not highlights:
            logger.warning("CustomPackageForm.clean highlights validation failed - empty")
            self.add_error('highlights', 'At least one highlight is required')
        
        if not inclusions:
            logger.warning("CustomPackageForm.clean inclusions validation failed - empty")
            self.add_error('inclusions', 'At least one inclusion is required')
        
        if self.errors:
            logger.warning(f"CustomPackageForm.clean validation failed with errors: {dict(self.errors)}")
        else:
            logger.info("CustomPackageForm.clean validation passed successfully")
        
        return cleaned_data

    def save(self, commit=True):
        """Save CustomPackage with helper processing"""
        logger.info(f"CustomPackageForm.save called with commit={commit}")
        logger.debug(f"CustomPackageForm.save instance pk: {self.instance.pk if self.instance else 'None'}")
        
        # Create/update package instance
        package = super().save(commit=False)
        logger.debug(f"CustomPackageForm.save super().save(commit=False) completed, package pk: {package.pk if package else 'None'}")
        
        # Set partner if not already set
        if not package.partner_id and self.request:
            effective_partner = self._get_effective_partner()
            if not effective_partner:
                logger.error("CustomPackageForm.save no effective partner available")
                raise ValueError("No effective partner available for saving package")
            package.partner = effective_partner
            logger.debug(f"CustomPackageForm.save setting partner: {package.partner}")
        
        # Set extracted currency and price
        if hasattr(self, '_extracted_currency') and hasattr(self, '_extracted_price'):
            package.currency = self._extracted_currency
            package.price = self._extracted_price
            logger.debug(f"CustomPackageForm.save setting currency: {package.currency}, price: {package.price}")

        package.save()
        logger.info(f"CustomPackageForm.save package saved with pk: {package.pk}")

        # Process highlights, inclusions, and addons - do this regardless of commit
        # Store the data on the instance for later processing in save_related
        if not hasattr(package, '_pending_arrays'):
            package._pending_arrays = {}
        
        package._pending_arrays['highlights'] = self.cleaned_data.get('highlights', [])
        package._pending_arrays['inclusions'] = self.cleaned_data.get('inclusions', [])
        package._pending_arrays['addons'] = self.cleaned_data.get('addons', [])
        
        logger.debug(f"CustomPackageForm.save stored pending arrays - highlights: {len(package._pending_arrays['highlights'])}, inclusions: {len(package._pending_arrays['inclusions'])}, addons: {len(package._pending_arrays['addons'])}")
        
        # If package already exists and commit=True, process arrays immediately
        if commit and package.pk:
            self._process_package_arrays(package)
        
        logger.info(f"CustomPackageForm.save completed successfully, package pk: {package.pk if package else 'None'}")
        return package
    
    def _process_package_arrays(self, package):
        """Process highlights, inclusions, and addons arrays"""
        try:
            logger.info("CustomPackageForm._process_package_arrays starting")
            
            if hasattr(package, '_pending_arrays'):
                highlights = package._pending_arrays.get('highlights', [])
                inclusions = package._pending_arrays.get('inclusions', [])
                addons = package._pending_arrays.get('addons', [])
                
                logger.debug(f"CustomPackageForm._process_package_arrays processing arrays - highlights: {highlights}, inclusions: {inclusions}, addons: {addons}")
                
                # Clear existing entries first
                package.highlights.all().delete()
                package.inclusions.all().delete()
                package.addons.all().delete()
                logger.debug("CustomPackageForm._process_package_arrays cleared existing entries")
                
                # Create new entries
                if highlights:
                    self._create_highlights(package, highlights)
                    
                if inclusions:
                    self._create_inclusions(package, inclusions)
                    
                if addons:
                    self._create_addons(package, addons)
                
                # Clear the pending arrays
                del package._pending_arrays
                logger.info("CustomPackageForm._process_package_arrays completed successfully")
                
        except Exception as e:
            logger.error(f"CustomPackageForm._process_package_arrays error: {e}")
            # Don't re-raise to avoid breaking the main save flow
    
    def save_related(self, package):
        """Save related data after the main instance is saved"""
        logger.info("CustomPackageForm.save_related called")
        
        if hasattr(package, '_pending_arrays'):
            logger.debug("CustomPackageForm.save_related processing pending arrays")
            self._process_package_arrays(package)
        else:
            logger.debug("CustomPackageForm.save_related no pending arrays to process")
    
    def _create_highlights(self, package, highlights):
        """Create PackageHighlight entries"""
        logger.debug(f"CustomPackageForm._create_highlights creating {len(highlights)} highlights")
        from packages.models import PackageHighlight
        
        for highlight_text in highlights:
            if highlight_text and highlight_text.strip():
                PackageHighlight.objects.create(
                    package=package,
                    value=highlight_text.strip(),
                    icon_class='fas fa-star'  # Default icon, can be enhanced later
                )
                logger.debug(f"CustomPackageForm._create_highlights created highlight: {highlight_text}")
    
    def _create_inclusions(self, package, inclusions):
        """Create PackageInclusion entries"""
        logger.debug(f"CustomPackageForm._create_inclusions creating {len(inclusions)} inclusions")
        from packages.models import PackageInclusion
        
        for inclusion_text in inclusions:
            if inclusion_text and inclusion_text.strip():
                PackageInclusion.objects.create(
                    package=package,
                    value=inclusion_text.strip(),
                    icon_class='fas fa-check'  # Default icon, can be enhanced later
                )
                logger.debug(f"CustomPackageForm._create_inclusions created inclusion: {inclusion_text}")
    
    def _create_addons(self, package, addons):
        """Create PackageAddon entries"""
        logger.debug(f"CustomPackageForm._create_addons creating {len(addons)} addons")
        from packages.models import PackageAddon
        
        for addon_text in addons:
            if addon_text and addon_text.strip():
                PackageAddon.objects.create(
                    package=package,
                    value=addon_text.strip(),
                    icon_class='fas fa-plus'  # Default icon, can be enhanced later
                )
                logger.debug(f"CustomPackageForm._create_addons created addon: {addon_text}")

