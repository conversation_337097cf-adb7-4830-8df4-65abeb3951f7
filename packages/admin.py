from django.contrib import admin
from decimal import Decimal, ROUND_HALF_UP
from django.utils.html import format_html
from django.contrib.gis import admin as gis_admin
from django.contrib.gis.admin import GISModelAdmin
from packages.models import (
    PackageUploader, Package, PackageMedia, Category, Destination, Activity,
    CategoryMedia, DestinationMedia, ActivityMedia,
    PackageCategory, PackageActivity, DestinationCategory, 
    DestinationActivity, DestinationFaq, CustomActivity, 
    CustomActivityMedia, CustomActivityCategory, CustomActivityLocation,
    CustomActivityCategoryRelation, CustomActivityLocationRelation, CustomActivitySystemCategoryRelation,
    CustomPackage, Itinerary, ItineraryDayItem, ItineraryHotel, ItineraryActivity
)
import ast
from datetime import datetime
from base.static import Constants
from accounts.choices import PackageTypeChoices as AccountPackageTypeChoices
from accounts.choices import UserTypeChoices
from packages.choices import PackageTypeChoices
from packages.forms import (
    PackageUploaderForm, DestinationAdminForm, CategoryAdminForm, ActivityAdminForm, 
    PackageAdminForm, DestinationFaqAdminForm, ActivityMediaAdminForm, CustomActivityAdminForm, 
    CustomPackageForm, ItineraryInlineForm, ItineraryDayItemInlineForm
)
from django_better_admin_arrayfield.admin.mixins import DynamicArrayMixin
from packages.services.package_creation_service import PackageCreationService
from base.admin_filters import (
    ActiveStatusFilter, 
    InternationalDestinationFilter, 
    TrendingDestinationFilter, 
    FeaturedActivityFilter,
    PublishStatusFilter
)
from django.urls import reverse
from django import forms
import logging
import re
import decimal

logger = logging.getLogger(__name__)


# Helper function to get user's effective partner
def get_user_effective_partner(request):
    """Get the effective partner for the user - more efficient than multiple queries"""
    # Check if user is authenticated to prevent AnonymousUser errors
    if not request.user.is_authenticated:
        return None
        
    if request.user.is_superuser:
        # For superadmins, return ZUUMM partner (should be cached in middleware)
        return getattr(request, 'zuumm_partner', None) or request.user.partner
    else:
        # For partner admins, return their own partner
        return request.user.partner


def user_can_manage_content(request):
    """Check if user can manage Category/Destination/Activity based on package_type"""
    # Check if user is authenticated to prevent AnonymousUser errors
    if not request.user.is_authenticated:
        return False
        
    if request.user.is_superuser:
        return True
    
    if request.user.user_type == UserTypeChoices.PARTNER_ADMIN.value:
        user_partner = request.user.partner
        if user_partner and user_partner.package_type == AccountPackageTypeChoices.OWN_PACKAGE.value:
            return True
    
    return False


def user_can_manage_packages(request):
    """Check if user can manage Packages based on package_type"""
    # Check if user is authenticated to prevent AnonymousUser errors
    if not request.user.is_authenticated:
        return False
        
    if request.user.is_superuser:
        return True
    
    if request.user.user_type == UserTypeChoices.PARTNER_ADMIN.value:
        user_partner = request.user.partner
        if user_partner and user_partner.package_type == AccountPackageTypeChoices.OWN_PACKAGE.value:
            return True
    
    return False


# Inline admins for M2M relationships  
class DestinationCategoryInline(admin.TabularInline):
    """Inline to add categories to destinations"""
    model = DestinationCategory
    extra = 1
    show_change_link = False  # Hide pencil/eye/plus icons
    autocomplete_fields = ['category']
    fields = ['destination', 'category']
    exclude = ['deleted_at', 'restored_at', 'is_active', 'external_id', 'created_at', 'updated_at']

    def get_formset(self, request, obj=None, **kwargs):
        formset = super().get_formset(request, obj, **kwargs)
        for field_name in self.autocomplete_fields:
            widget = formset.form.base_fields[field_name].widget
            widget.can_add_related = False
            widget.can_change_related = False
            widget.can_view_related = False
        return formset

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        """Filter categories by user's effective partner and is_active=True"""
        if db_field.name == "category":
            effective_partner = get_user_effective_partner(request)
            if effective_partner:
                kwargs["queryset"] = Category.objects.filter(partner=effective_partner, is_active=True)
            else:
                kwargs["queryset"] = Category.objects.none()
        return super().formfield_for_foreignkey(db_field, request, **kwargs)
    
    def has_add_permission(self, request, obj=None):
        """Allow adding M2M relationships if user can manage content"""
        return user_can_manage_content(request)
    
    def has_change_permission(self, request, obj=None):
        """Allow changing M2M relationships if user can manage content"""
        return user_can_manage_content(request)
    
    def has_delete_permission(self, request, obj=None):
        """Allow deleting M2M relationships if user can manage content"""
        return user_can_manage_content(request)
    
    def has_view_permission(self, request, obj=None):
        """Allow viewing M2M relationships if user can manage content"""
        return user_can_manage_content(request)


class DestinationActivityInline(admin.TabularInline):
    """Inline to add activities to destinations"""
    model = DestinationActivity
    extra = 1
    show_change_link = False  # Hide pencil/eye/plus icons
    autocomplete_fields = ['activity']
    fields = ['destination', 'activity']
    exclude = ['deleted_at', 'restored_at', 'is_active', 'external_id', 'created_at', 'updated_at']

    def get_formset(self, request, obj=None, **kwargs):
        formset = super().get_formset(request, obj, **kwargs)
        for field_name in self.autocomplete_fields:
            widget = formset.form.base_fields[field_name].widget
            widget.can_add_related = False
            widget.can_change_related = False
            widget.can_view_related = False
        return formset

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        """Filter activities by user's effective partner and is_active=True"""
        if db_field.name == "activity":
            effective_partner = get_user_effective_partner(request)
            if effective_partner:
                kwargs["queryset"] = Activity.objects.filter(partner=effective_partner, is_active=True)
            else:
                kwargs["queryset"] = Activity.objects.none()
        return super().formfield_for_foreignkey(db_field, request, **kwargs)
    
    def has_add_permission(self, request, obj=None):
        """Allow adding M2M relationships if user can manage content"""
        return user_can_manage_content(request)
    
    def has_change_permission(self, request, obj=None):
        """Allow changing M2M relationships if user can manage content"""
        return user_can_manage_content(request)
    
    def has_delete_permission(self, request, obj=None):
        """Allow deleting M2M relationships if user can manage content"""
        return user_can_manage_content(request)
    
    def has_view_permission(self, request, obj=None):
        """Allow viewing M2M relationships if user can manage content"""
        return user_can_manage_content(request)


# Media inlines
class CategoryMediaInline(admin.StackedInline):
    """Inline admin for CategoryMedia - one media per category"""
    model = CategoryMedia
    extra = 1
    show_change_link = False  # Hide pencil/eye/plus icons
    max_num = Constants.CATEGORY_MAX_MEDIA_COUNT
    fields = ('media',)
    
    def get_formset(self, request, obj=None, **kwargs):
        """Customize formset to make media field required and add validation"""
        formset = super().get_formset(request, obj, **kwargs)
        formset.form.base_fields['media'].required = True
        formset.form.base_fields['media'].help_text = "Media file is required (jpg, jpeg, png, webp)"
        
        # Override formset clean method to validate at least one media
        original_clean = formset.clean
        
        def clean_with_media_validation(self):
            if hasattr(original_clean, '__call__'):
                original_clean(self)
            
            # Check if at least one valid media file is provided
            valid_media_count = 0
            for form in self.forms:
                if not form.cleaned_data.get('DELETE', False) and form.cleaned_data.get('media'):
                    valid_media_count += 1
            
            if valid_media_count == 0:
                from django.core.exceptions import ValidationError
                raise ValidationError("At least one media file is required for this category. Please upload an image.")
        
        formset.clean = clean_with_media_validation
        return formset
    
    def get_extra(self, request, obj=None, **kwargs):
        """Only show extra form if no media exists"""
        if obj and obj.media.exists():
            return 0
        return 1
    
    def has_add_permission(self, request, obj=None):
        """Allow adding media if user can manage content"""
        return user_can_manage_content(request)
    
    def has_change_permission(self, request, obj=None):
        """Allow changing media if user can manage content"""
        return user_can_manage_content(request)
    
    def has_delete_permission(self, request, obj=None):
        """Allow deleting media if user can manage content"""
        return user_can_manage_content(request)
    
    def has_view_permission(self, request, obj=None):
        """Allow viewing media if user can manage content"""
        return user_can_manage_content(request)


class DestinationMediaInline(admin.StackedInline):
    """Inline admin for DestinationMedia - one media per destination"""
    model = DestinationMedia
    extra = 1
    show_change_link = False  # Hide pencil/eye/plus icons
    max_num = Constants.DESTINATION_MAX_MEDIA_COUNT 
    fields = ('media',)
    
    def get_formset(self, request, obj=None, **kwargs):
        """Customize formset to make media field required and add validation"""
        formset = super().get_formset(request, obj, **kwargs)
        formset.form.base_fields['media'].required = True
        formset.form.base_fields['media'].help_text = "Media file is required (jpg, jpeg, png, webp)"
        
        # Override formset clean method to validate at least one media
        original_clean = formset.clean
        
        def clean_with_media_validation(self):
            if hasattr(original_clean, '__call__'):
                original_clean(self)
            
            # Check if at least one valid media file is provided
            valid_media_count = 0
            for form in self.forms:
                if not form.cleaned_data.get('DELETE', False) and form.cleaned_data.get('media'):
                    valid_media_count += 1
            
            if valid_media_count == 0:
                from django.core.exceptions import ValidationError
                raise ValidationError("At least one media file is required for this destination. Please upload an image.")
        
        formset.clean = clean_with_media_validation
        return formset
    
    def get_extra(self, request, obj=None, **kwargs):
        """Only show extra form if no media exists"""
        if obj and obj.media.exists():
            return 0
        return 1
    
    def has_add_permission(self, request, obj=None):
        """Allow adding media if user can manage content"""
        return user_can_manage_content(request)
    
    def has_change_permission(self, request, obj=None):
        """Allow changing media if user can manage content"""
        return user_can_manage_content(request)
    
    def has_delete_permission(self, request, obj=None):
        """Allow deleting media if user can manage content"""
        return user_can_manage_content(request)
    
    def has_view_permission(self, request, obj=None):
        """Allow viewing media if user can manage content"""
        return user_can_manage_content(request)


class ActivityMediaInline(admin.TabularInline):
    """Inline admin for ActivityMedia - multiple media files per activity (up to 10)"""
    model = ActivityMedia
    form = ActivityMediaAdminForm
    extra = 1
    show_change_link = False  # Hide pencil/eye/plus icons
    max_num = Constants.ACTIVITY_MAX_MEDIA_COUNT
    fields = ('media', 'main_display')
    
    def get_formset(self, request, obj=None, **kwargs):
        """Customize formset to make media field required and add validation"""
        formset = super().get_formset(request, obj, **kwargs)
        formset.form.base_fields['media'].required = True
        formset.form.base_fields['media'].help_text = "Media file is required (jpg, jpeg, png, webp)"
        formset.form.base_fields['main_display'].help_text = "Check to set as main display image (only one allowed per activity)"
        
        # Override formset clean method to validate at least one media and main_display logic
        original_clean = formset.clean
        
        def clean_with_media_validation(self):
            if hasattr(original_clean, '__call__'):
                original_clean(self)
            
            # Check if at least one valid media file is provided
            valid_media_count = 0
            main_display_count = 0
            
            for form in self.forms:
                if not form.cleaned_data.get('DELETE', False):
                    if form.cleaned_data.get('media'):
                        valid_media_count += 1
                    if form.cleaned_data.get('main_display'):
                        main_display_count += 1
            
            if valid_media_count == 0:
                from django.core.exceptions import ValidationError
                raise ValidationError("At least one media file is required for this activity. Please upload an image.")
            
            if main_display_count > 1:
                from django.core.exceptions import ValidationError
                raise ValidationError("Only one media file can be set as main display per activity.")
        
        formset.clean = clean_with_media_validation
        return formset
    
    def get_extra(self, request, obj=None, **kwargs):
        """Show extra forms based on current media count"""
        if obj and obj.media.count() >= Constants.ACTIVITY_MAX_MEDIA_COUNT:
            return 0
        return 1
    
    def has_add_permission(self, request, obj=None):
        """Allow adding media if user can manage content"""
        return user_can_manage_content(request)
    
    def has_change_permission(self, request, obj=None):
        """Allow changing media if user can manage content"""
        return user_can_manage_content(request)
    
    def has_delete_permission(self, request, obj=None):
        """Allow deleting media if user can manage content"""
        return user_can_manage_content(request)
    
    def has_view_permission(self, request, obj=None):
        """Allow viewing media if user can manage content"""
        return user_can_manage_content(request)


class PackageMediaInline(admin.TabularInline):
    """Inline admin for PackageMedia to add media directly to packages"""
    model = PackageMedia
    extra = 1
    show_change_link = False  # Hide pencil/eye/plus icons
    max_num = Constants.PACKAGE_MAX_MEDIA_COUNT
    fields = ('file',)

    def get_extra(self, request, obj=None, **kwargs):
        """Only show extra form if less than max media exists"""
        if obj and obj.media.count() >= Constants.PACKAGE_MAX_MEDIA_COUNT:
            return 0
        return 1

    def has_add_permission(self, request, obj=None):
        """Allow adding media if user can manage packages"""
        return user_can_manage_packages(request)
    
    def has_change_permission(self, request, obj=None):
        """Allow changing media if user can manage packages"""
        return user_can_manage_packages(request)
    
    def has_delete_permission(self, request, obj=None):
        """Allow deleting media if user can manage packages"""
        return user_can_manage_packages(request)
    
    def has_view_permission(self, request, obj=None):
        """Allow viewing media if user can manage packages"""
        return user_can_manage_packages(request)


class PackageCategoryInline(admin.TabularInline):
    """Inline to add categories to packages"""
    model = PackageCategory
    extra = 1
    show_change_link = False  # Hide pencil/eye/plus icons
    autocomplete_fields = ['category']
    fields = ['package', 'category']
    exclude = ['deleted_at', 'restored_at', 'is_active', 'external_id', 'created_at', 'updated_at']

    def get_formset(self, request, obj=None, **kwargs):
        formset = super().get_formset(request, obj, **kwargs)
        for field_name in self.autocomplete_fields:
            widget = formset.form.base_fields[field_name].widget
            widget.can_add_related = False
            widget.can_change_related = False
            widget.can_view_related = False
        return formset

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        """Filter categories by user's effective partner and is_active=True"""
        if db_field.name == "category":
            effective_partner = get_user_effective_partner(request)
            if effective_partner:
                kwargs["queryset"] = Category.objects.filter(partner=effective_partner, is_active=True)
            else:
                kwargs["queryset"] = Category.objects.none()
        return super().formfield_for_foreignkey(db_field, request, **kwargs)
    
    def has_add_permission(self, request, obj=None):
        """Allow adding M2M relationships if user can manage packages"""
        return user_can_manage_packages(request)
    
    def has_change_permission(self, request, obj=None):
        """Allow changing M2M relationships if user can manage packages"""
        return user_can_manage_packages(request)
    
    def has_delete_permission(self, request, obj=None):
        """Allow deleting M2M relationships if user can manage packages"""
        return user_can_manage_packages(request)
    
    def has_view_permission(self, request, obj=None):
        """Allow viewing M2M relationships if user can manage packages"""
        return user_can_manage_packages(request)


class PackageActivityInline(admin.TabularInline):
    """Inline to add activities to packages"""
    model = PackageActivity
    extra = 1
    show_change_link = False  # Hide pencil/eye/plus icons
    autocomplete_fields = ['activity']
    fields = ['package', 'activity']
    exclude = ['deleted_at', 'restored_at', 'is_active', 'external_id', 'created_at', 'updated_at']

    def get_formset(self, request, obj=None, **kwargs):
        formset = super().get_formset(request, obj, **kwargs)
        for field_name in self.autocomplete_fields:
            widget = formset.form.base_fields[field_name].widget
            widget.can_add_related = False
            widget.can_change_related = False
            widget.can_view_related = False
        return formset

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        """Filter activities by user's effective partner and is_active=True"""
        if db_field.name == "activity":
            effective_partner = get_user_effective_partner(request)
            if effective_partner:
                kwargs["queryset"] = Activity.objects.filter(partner=effective_partner, is_active=True)
            else:
                kwargs["queryset"] = Activity.objects.none()
        return super().formfield_for_foreignkey(db_field, request, **kwargs)
    
    def has_add_permission(self, request, obj=None):
        """Allow adding M2M relationships if user can manage packages"""
        return user_can_manage_packages(request)
    
    def has_change_permission(self, request, obj=None):
        """Allow changing M2M relationships if user can manage packages"""
        return user_can_manage_packages(request)
    
    def has_delete_permission(self, request, obj=None):
        """Allow deleting M2M relationships if user can manage packages"""
        return user_can_manage_packages(request)
    
    def has_view_permission(self, request, obj=None):
        """Allow viewing M2M relationships if user can manage packages"""
        return user_can_manage_packages(request)


# Main model admins
@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    """Admin for Category model with improved partner logic"""
    form = CategoryAdminForm
    list_display = ('title', 'description', 'has_media', 'is_active', 'created_at')
    search_fields = ('title', 'description')
    list_filter = (ActiveStatusFilter, 'created_at')
    
    fieldsets = (
        ('General', {
            'fields': ('title', 'description', 'explore_order', 'is_active'),
        }),
    )
    
    inlines = [CategoryMediaInline]
    
    def get_queryset(self, request):
        """Filter by user's effective partner"""
        qs = super().get_queryset(request)
        effective_partner = get_user_effective_partner(request)
        if effective_partner:
            return qs.filter(partner=effective_partner)
        return qs.none()

    def save_model(self, request, obj, form, change):
        """Auto-assign partner based on user"""
        if not change:  # Only for new categories
            obj.partner = get_user_effective_partner(request)
        super().save_model(request, obj, form, change)

    def get_form(self, request, obj=None, **kwargs):
        """Pass request to form for partner context"""
        form = super().get_form(request, obj, **kwargs)
        
        class RequestAwareForm(form):
            def __init__(self, *args, **kwargs):
                kwargs['request'] = request
                super().__init__(*args, **kwargs)
        
        return RequestAwareForm

    def has_media(self, obj):
        """Check if category has media"""
        if not obj or not obj.pk:
            return False
        return obj.media.exists()
    has_media.boolean = True
    has_media.short_description = 'Has Media'
    
    # Permission methods based on package_type
    def has_module_permission(self, request):
        """Only allow users who can manage content"""
        return request.user.is_active and user_can_manage_content(request)
    
    def has_view_permission(self, request, obj=None):
        """Only allow users who can manage content"""
        return request.user.is_active and user_can_manage_content(request)
    
    def has_add_permission(self, request):
        """Only allow users who can manage content"""
        return request.user.is_active and user_can_manage_content(request)
    
    def has_change_permission(self, request, obj=None):
        """Only allow users who can manage content"""
        return request.user.is_active and user_can_manage_content(request)
    
    def has_delete_permission(self, request, obj=None):
        """Remove delete permission for all users"""
        return False


@admin.register(Destination)
class DestinationAdmin(admin.ModelAdmin):
    """Admin for Destination model with improved partner logic"""
    form = DestinationAdminForm
    
    list_display = ('title', 'description', 'is_international', 'is_trending', 'best_months_display', 'has_media', 'category_count', 'activity_count', 'hotels_link', 'is_active', 'created_at')
    search_fields = ('title', 'description')
    list_filter = (InternationalDestinationFilter, TrendingDestinationFilter, ActiveStatusFilter, 'created_at')
    readonly_fields = ('hotels_link', 'category_count', 'activity_count', 'has_media', 'best_months_display')
    
    fieldsets = (
        ('General', {
            'fields': ('title', 'description', 'is_international', 'is_trending',  'explore_order', 'hotels_link', 'is_active',),
        }),
        ('Best Time to Visit', {
            'fields': ('best_time_to_visit',),
            'classes': ('wide',),
            'description': 'Select the months when it\'s best to visit this destination'
        }),
    )
    
    inlines = [DestinationMediaInline, DestinationCategoryInline, DestinationActivityInline]
    
    def get_queryset(self, request):
        """Filter by user's effective partner"""
        qs = super().get_queryset(request)
        effective_partner = get_user_effective_partner(request)
        if effective_partner:
            return qs.filter(partner=effective_partner)
        return qs.none()

    def save_model(self, request, obj, form, change):
        """Auto-assign partner based on user"""
        if not change:  # Only for new destinations
            obj.partner = get_user_effective_partner(request)
        super().save_model(request, obj, form, change)

    def get_form(self, request, obj=None, **kwargs):
        """Pass request to form for partner context"""
        form = super().get_form(request, obj, **kwargs)
        
        class RequestAwareForm(form):
            def __init__(self, *args, **kwargs):
                kwargs['request'] = request
                super().__init__(*args, **kwargs)
        
        return RequestAwareForm

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        """Filter destination dropdown by partner and is_active=True for Package forms"""
        if db_field.name == "destination":
            effective_partner = get_user_effective_partner(request)
            if effective_partner:
                kwargs["queryset"] = Destination.objects.filter(partner=effective_partner, is_active=True)
            else:
                kwargs["queryset"] = Destination.objects.none()
        return super().formfield_for_foreignkey(db_field, request, **kwargs)

    def has_media(self, obj):
        """Check if destination has media - boolean indicator"""
        if not obj or not obj.pk:
            return False
        return obj.media.exists()
    has_media.short_description = 'Has Media'
    has_media.boolean = True
    
    def category_count(self, obj):
        """Count categories - safe for unsaved objects"""
        if not obj or not obj.pk:
            return 0
        return obj.categories.count()
    category_count.short_description = 'Categories'
    
    def activity_count(self, obj):
        """Count activities - safe for unsaved objects"""
        if not obj or not obj.pk:
            return 0
        return obj.activities.count()
    activity_count.short_description = 'Activities'
    
    def hotels_link(self, obj):
        """Display hyperlink to hotels for this destination with count"""
        if not obj or not obj.pk:
            return format_html('<span style="color: gray;">No hotels</span>')
        
        # Import Hotel model from dynamic_packages
        from dynamic_packages.models import Hotel
        
        # Count hotels for this destination
        hotel_count = Hotel.objects.filter(destination=obj, is_active=True).count()
        
        if hotel_count > 0:
            # Create URL to filtered hotels list in dynamic_packages admin
            url = reverse('admin:dynamic_packages_hotel_changelist') + f'?destination__id__exact={obj.id}'
            return format_html(
                '<a href="{}" target="_blank" style="color: #417690; text-decoration: none;">'
                '🏨 {} Hotels</a>',
                url, hotel_count
            )
        else:
            return format_html('<span style="color: gray;">No hotels</span>')
    hotels_link.short_description = 'Hotels'
    
    def best_months_display(self, obj):
        """Display best months to visit"""
        if not obj or not obj.pk:
            return "-"
        return obj.get_best_months_display()
    best_months_display.short_description = 'Best Time to Visit'
    
    # Permission methods based on package_type
    def has_module_permission(self, request):
        """Only allow users who can manage content"""
        return request.user.is_active and user_can_manage_content(request)
    
    def has_view_permission(self, request, obj=None):
        """Only allow users who can manage content"""
        return request.user.is_active and user_can_manage_content(request)
    
    def has_add_permission(self, request):
        """Only allow users who can manage content"""
        return request.user.is_active and user_can_manage_content(request)
    
    def has_change_permission(self, request, obj=None):
        """Only allow users who can manage content"""
        return request.user.is_active and user_can_manage_content(request)
    
    def has_delete_permission(self, request, obj=None):
        """Remove delete permission for all users"""
        return False


@admin.register(Activity)
class ActivityAdmin(admin.ModelAdmin):
    """Admin for Activity model with improved partner logic"""
    form = ActivityAdminForm
    list_display = ('title', 'description', 'is_featured', 'has_media', 'is_active', 'created_at')
    search_fields = ('title', 'description')
    list_filter = (FeaturedActivityFilter, ActiveStatusFilter, 'created_at')
    
    fieldsets = (
        ('General', {
            'fields': ('title', 'description', 'is_featured', 'explore_order', 'is_active'),
        }),
    )
    
    inlines = [ActivityMediaInline]
    
    def get_queryset(self, request):
        """Filter by user's effective partner"""
        qs = super().get_queryset(request)
        effective_partner = get_user_effective_partner(request)
        if effective_partner:
            return qs.filter(partner=effective_partner)
        return qs.none()

    def save_model(self, request, obj, form, change):
        """Auto-assign partner based on user"""
        if not change:  # Only for new activities
            obj.partner = get_user_effective_partner(request)
        super().save_model(request, obj, form, change)

    def get_form(self, request, obj=None, **kwargs):
        """Pass request to form for partner context"""
        form = super().get_form(request, obj, **kwargs)
        
        class RequestAwareForm(form):
            def __init__(self, *args, **kwargs):
                kwargs['request'] = request
                super().__init__(*args, **kwargs)
        
        return RequestAwareForm

    def has_media(self, obj):
        """Check if activity has media - boolean indicator"""
        if not obj or not obj.pk:
            return False
        return obj.media.exists()
    has_media.short_description = 'Has Media'
    has_media.boolean = True
    
    # Permission methods based on package_type
    def has_module_permission(self, request):
        """Only allow users who can manage content"""
        return request.user.is_active and user_can_manage_content(request)
    
    def has_view_permission(self, request, obj=None):
        """Only allow users who can manage content"""
        return request.user.is_active and user_can_manage_content(request)
    
    def has_add_permission(self, request):
        """Only allow users who can manage content"""
        return request.user.is_active and user_can_manage_content(request)
    
    def has_change_permission(self, request, obj=None):
        """Only allow users who can manage content"""
        return request.user.is_active and user_can_manage_content(request)
    
    def has_delete_permission(self, request, obj=None):
        """Remove delete permission for all users"""
        return False


@admin.register(Package)
class PackageAdmin(admin.ModelAdmin, DynamicArrayMixin):
    """Admin interface for travel packages with simplified single form"""
    form = PackageAdminForm
    list_display = ('title', 'package_no', 'destination', 'price_per_person', 'duration', 'is_published', 'created_at')
    search_fields = ('title', 'package_no', 'destination__title')
    list_filter = (PublishStatusFilter, ActiveStatusFilter, 'type', 'created_at')
    readonly_fields = ('created_at', 'updated_at', 'json_file_link')  # Removed type from readonly since form handles it
    inlines = [PackageMediaInline, PackageCategoryInline, PackageActivityInline]
    autocomplete_fields = ['destination']
    
    # Custom template for change list to add custom button - specific to Package model
    change_list_template = 'admin/packages/package/change_list.html'

    def get_form(self, request, obj=None, **kwargs):
        """Pass request to form for partner handling and customize field labels"""
        form = super().get_form(request, obj, **kwargs)
        
        # Create a subclass that has access to request and customized labels
        class RequestAwareForm(form):
            def __init__(self, *args, **kwargs):
                super().__init__(*args, **kwargs)
                self.request = request
                
                # Customize field labels
                if 'package_no' in self.fields:
                    self.fields['package_no'].label = 'Package No.'
            
            def full_clean(self):
                """Override to show only first error per field"""
                super().full_clean()
                
                # If there are field errors, keep only the first error for each field
                if hasattr(self, '_errors') and self._errors:
                    from django.forms.utils import ErrorList
                    for field_name, error_list in self._errors.items():
                        if len(error_list) > 1:
                            # Keep only the first error message but maintain ErrorList type
                            first_error = error_list[0]
                            self._errors[field_name] = ErrorList([first_error])
            
            def save_m2m(self):
                """Save many-to-many relationships - delegate to parent form"""
                if hasattr(super(), 'save_m2m'):
                    return super().save_m2m()
                # If parent doesn't have save_m2m, it's likely because our PackageAdminForm
                # handles M2M relationships differently (through PackageCreationService)
                # In that case, do nothing as M2M relationships are already handled
                pass
        
        return RequestAwareForm
    
    def get_queryset(self, request):
        """Filter by user's effective partner with optimized queries"""
        qs = super().get_queryset(request)
        effective_partner = get_user_effective_partner(request)
        qs = qs.filter(type=PackageTypeChoices.FIXED.value)
        if effective_partner:
            return qs.filter(partner=effective_partner).select_related(
                'destination', 'partner', 'package_uploaded'
            )
        return qs.none()
    
    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        """Filter destination dropdown by partner and is_active=True"""
        if db_field.name == "destination":
            effective_partner = get_user_effective_partner(request)
            if effective_partner:
                kwargs["queryset"] = Destination.objects.filter(partner=effective_partner, is_active=True)
            else:
                kwargs["queryset"] = Destination.objects.none()
        return super().formfield_for_foreignkey(db_field, request, **kwargs)
    
    def get_fields(self, request, obj=None):
        """Single form with all fields except auto-calculated and system fields"""
        fields = [
            'title', 'package_no', 'destination', 'type', 'explore_order', 'is_published', 'is_active',
            'duration',  # AI will calculate duration_in_nights and duration_in_days
            'price_per_person', 'currency_conversion_rate',
            'owner', 'about_this_tour', 'highlights', 'inclusions', 'exclusions',
            'itinerary',
            'visa_type', 'best_time_to_visit', 'destination_safety', 'rating', 'rating_description',
            'hotels', 'popular_restaurants', 'popular_activities',
            'addons',  # New field for add on services
            'cultural_info', 'what_to_shop', 'what_to_pack', 'important_notes',
        ]
        
        # Add readonly fields for existing objects
        if obj:
            fields.extend(['json_file_link', 'created_at', 'updated_at'])
        
        # Partner, currency, currency_symbol, duration_in_nights, duration_in_days, best_time_to_visit_months are handled automatically
        return fields
    
    def save_model(self, request, obj, form, change):
        """Set partner automatically based on user and ensure type is FIXED"""
        if not change:  # Only set partner when creating new package
            obj.partner = get_user_effective_partner(request)
            obj.type = PackageTypeChoices.FIXED.value  # Always set to FIXED
        super().save_model(request, obj, form, change)
    
    def save_related(self, request, form, formsets, change):
        """Override to ensure formsets have the correct package instance"""
        if not change and hasattr(form, '_processed_package') and form._processed_package:
            for formset in formsets:
                if hasattr(formset, 'instance'):
                    formset.instance = form._processed_package
                    for inline_form in formset.forms:
                        if hasattr(inline_form, 'instance') and inline_form.instance:
                            if hasattr(inline_form.instance, 'package'):
                                inline_form.instance.package = form._processed_package
        super().save_related(request, form, formsets, change)
    
    def json_file_link(self, obj):
        """Show link to view PackageUploader details for file-uploaded packages"""
        if obj and obj.pk and obj.package_uploaded:
            # Package was created from file upload - show link to PackageUploader admin
            uploader_url = reverse('admin:packages_packageuploader_change', args=[obj.package_uploaded.pk])
            return format_html('<a href="{}" target="_blank">📄 View Upload Details</a>', uploader_url)
        return "-"  # Manual package or no package_uploaded relationship
    json_file_link.short_description = 'Upload Details'
    
    # Permission methods based on package_type
    def has_module_permission(self, request):
        """Only allow users who can manage packages"""
        return request.user.is_active and user_can_manage_packages(request)
    
    def has_view_permission(self, request, obj=None):
        """Only allow users who can manage packages"""
        return request.user.is_active and user_can_manage_packages(request)
    
    def has_add_permission(self, request):
        """Only allow users who can manage packages"""
        return request.user.is_active and user_can_manage_packages(request)
    
    def has_change_permission(self, request, obj=None):
        """Only allow users who can manage packages"""
        return request.user.is_active and user_can_manage_packages(request)
    
    def has_delete_permission(self, request, obj=None):
        """Remove delete permission for all users"""
        return False


@admin.register(PackageUploader)
class PackageUploaderAdmin(admin.ModelAdmin):
    """Admin interface for package JSON uploads - Hidden from admin panel"""
    form = PackageUploaderForm
    list_display = ('file', 'created_at')  # Remove file_type from display
    readonly_fields = ()
    
    def get_form(self, request, obj=None, **kwargs):
        """Use different form for readonly vs editable views"""
        if obj:  # Existing object - use a simple ModelForm without custom __init__
            from django import forms
            
            class ReadOnlyPackageUploaderForm(forms.ModelForm):
                class Meta:
                    model = PackageUploader
                    fields = ('file',)  # Remove file_type from fields
                
                def __init__(self, *args, **kwargs):
                    super().__init__(*args, **kwargs)
                    # Don't add complex help text or custom logic for readonly views
            
            form = ReadOnlyPackageUploaderForm
        else:  # New object - use the full PackageUploaderForm
            form = super().get_form(request, obj, **kwargs)
            
            # Create a subclass that has access to request
            class RequestAwareForm(form):
                def __init__(self, *args, **kwargs):
                    super().__init__(*args, **kwargs)
                    self.request = request
            
            return RequestAwareForm
        
        return form
    
    def changelist_view(self, request, extra_context=None):
        """Redirect to Package admin with message instead of showing changelist"""
        from django.shortcuts import redirect
        from django.contrib import messages
        
        messages.info(request, 'This section is not accessible. Use the "Upload Package File" button to add new packages.')
        return redirect('admin:packages_package_changelist')
    
    def change_view(self, request, object_id, form_url='', extra_context=None):
        """Allow viewing individual PackageUploader objects but make fields readonly"""
        extra_context = extra_context or {}
        extra_context['title'] = 'Package Upload Details'
        
        return super().change_view(request, object_id, form_url, extra_context)
    
    def get_readonly_fields(self, request, obj=None):
        """Make all fields readonly when viewing existing objects"""
        if obj:  # Existing object (change view)
            return ('file', 'file_type', 'partner', 'file_download_link', 'created_package_link', 'created_at', 'updated_at')
        else:  # New object (add view)
            return ('partner', 'created_at', 'updated_at')
    
    def file_download_link(self, obj):
        """Show download link for the uploaded file"""
        if obj and obj.file:
            file_url = obj.file.url
            file_name = obj.file.name.split('/')[-1]  # Get just the filename
            return format_html('<a href="{}" download="{}">📥 Download Original File</a>', file_url, file_name)
        return "-"
    file_download_link.short_description = 'Download File'
    
    def created_package_link(self, obj):
        """Show link to the package that was created from this upload"""
        if obj and obj.pk:
            try:
                # Find the package that was created from this uploader
                package = Package.objects.get(package_uploaded=obj)
                package_url = reverse('admin:packages_package_change', args=[package.pk])
                return format_html('<a href="{}" target="_blank">📦 View Created Package: {}</a>', package_url, package.title)
            except Package.DoesNotExist:
                return "No package created yet"
        return "-"
    created_package_link.short_description = 'Created Package'
    
    def get_list_filter(self, request):
        """Remove file_type filter - only show created_at"""
        return ('created_at',)
    
    def get_fields(self, request, obj=None):
        """Show only file field - partner and file_type are set automatically"""
        return ('file',)
    
    def get_queryset(self, request):
        """Filter by user's effective partner"""
        qs = super().get_queryset(request)
        effective_partner = get_user_effective_partner(request)
        if effective_partner:
            return qs.filter(partner=effective_partner)
        return qs.none()
    
    def save_model(self, request, obj, form, change):
        """Set partner and file_type automatically based on user"""
        if not change:  # Only set partner and file_type when creating new uploader
            obj.partner = get_user_effective_partner(request)
            obj.file_type = PackageTypeChoices.FIXED.value  # Always set to FIXED
        super().save_model(request, obj, form, change)
    
    def response_add(self, request, obj, post_url_continue=None):
        """Redirect to created Package after successful upload with simplified success message"""
        from django.shortcuts import redirect
        from django.contrib import messages
        
        # Always show success message for file upload
        messages.success(
            request, 
            f'Package file "{obj.file.name.split("/")[-1]}" was uploaded successfully.'
        )
        
        # Try to find and redirect to created package if it exists
        try:
            package = Package.objects.get(package_uploaded=obj)
            messages.success(
                request, 
                f'Package "{package.title}" was created successfully.'
            )
            return redirect('admin:packages_package_change', package.pk)
        except Package.DoesNotExist:
            # Just redirect to package list if no package was created
            return redirect('admin:packages_package_changelist')
    
    def response_change(self, request, obj):
        """Redirect to associated Package after successful update with simplified success message"""
        from django.shortcuts import redirect
        from django.contrib import messages
        
        # Always show success message for file update
        messages.success(
            request, 
            f'Package file "{obj.file.name.split("/")[-1]}" was updated successfully.'
        )
        
        # Try to find and redirect to associated package if it exists
        try:
            package = Package.objects.get(package_uploaded=obj)
            return redirect('admin:packages_package_change', package.pk)
        except Package.DoesNotExist:
            # Just redirect to package list if no package is associated
            return redirect('admin:packages_package_changelist')
    
    def has_change_permission(self, request, obj=None):
        """Only allow users who can manage packages"""
        return request.user.is_active and user_can_manage_packages(request)
    
    def has_delete_permission(self, request, obj=None):
        """Disable delete permission"""
        return False
    
    def has_module_permission(self, request):
        """Hide from admin panel sidebar"""
        return False
    
    def has_add_permission(self, request):
        """Allow adding through direct URL if user can manage packages"""
        return request.user.is_active and user_can_manage_packages(request)
    
    def has_view_permission(self, request, obj=None):
        """Allow viewing through direct URL if user can manage packages"""
        return request.user.is_active and user_can_manage_packages(request)


@admin.register(DestinationFaq)
class DestinationFaqAdmin(admin.ModelAdmin):
    """Admin for DestinationFaq model with improved partner logic"""
    form = DestinationFaqAdminForm
    list_display = ('destination', 'question_preview', 'is_published', 'priority', 'created_at')
    search_fields = ('destination__title', 'question', 'answer')
    list_filter = ('is_published', 'created_at')
    ordering = ('-priority', '-created_at')
    
    fieldsets = (
        ('General', {
            'fields': ('destination', 'question', 'answer', 'is_published', 'priority'),
        }),
    )
    
    def question_preview(self, obj):
        """Show first 50 characters of question"""
        if obj.question and len(obj.question) > 50:
            return obj.question[:50] + "..."
        return obj.question or "-"
    question_preview.short_description = 'Question'
    
    def get_queryset(self, request):
        """Filter by user's effective partner"""
        qs = super().get_queryset(request)
        effective_partner = get_user_effective_partner(request)
        if effective_partner:
            return qs.filter(partner=effective_partner)
        return qs.none()

    def save_model(self, request, obj, form, change):
        """Auto-assign partner based on user"""
        if not change:  # Only for new FAQs
            obj.partner = get_user_effective_partner(request)
        super().save_model(request, obj, form, change)

    def get_form(self, request, obj=None, **kwargs):
        """Pass request to form for partner context"""
        form = super().get_form(request, obj, **kwargs)
        
        class RequestAwareForm(form):
            def __init__(self, *args, **kwargs):
                kwargs['request'] = request
                super().__init__(*args, **kwargs)
        
        return RequestAwareForm

    def has_module_permission(self, request):
        """Only allow users who can manage content"""
        return request.user.is_active and user_can_manage_content(request)
    
    def has_view_permission(self, request, obj=None):
        """Only allow users who can manage content"""
        return request.user.is_active and user_can_manage_content(request)
    
    def has_add_permission(self, request):
        """Only allow users who can manage content"""
        return request.user.is_active and user_can_manage_content(request)
    
    def has_change_permission(self, request, obj=None):
        """Only allow users who can manage content"""
        return request.user.is_active and user_can_manage_content(request)
    
    def has_delete_permission(self, request, obj=None):
        """Allow delete permission for FAQs"""
        return request.user.is_active and user_can_manage_content(request)

    def delete_model(self, request, obj):
        """Use hard_delete instead of soft delete"""
        # Use the hard_delete method from django-softdelete
        obj.hard_delete()
    
    def delete_queryset(self, request, queryset):
        """Hard delete multiple objects"""
        # Use hard_delete for bulk deletions
        for obj in queryset:
            obj.hard_delete()


# =============================================================================
# CUSTOM ACTIVITY MODELS ADMIN (GetYourGuide Data)
# =============================================================================

class CustomActivityCategoryInlineFormSet(forms.models.BaseInlineFormSet):
    """Custom formset for CustomActivityCategoryInline to handle M2M relationships properly"""
    
    def clean(self):
        """Validate the formset data"""
        super().clean()
        
        if any(self.errors):
            return
        
        # Check for duplicate categories
        categories = []
        for form in self.forms:
            if form.cleaned_data and not form.cleaned_data.get('DELETE', False):
                category = form.cleaned_data.get('category')
                if category:
                    if category in categories:
                        raise forms.ValidationError("Cannot add the same category multiple times.")
                    categories.append(category)


# Inline admins for Custom Activity M2M relationships
class CustomActivityCategoryInline(admin.TabularInline):
    """Inline to add categories to custom activities"""
    model = CustomActivityCategoryRelation
    formset = CustomActivityCategoryInlineFormSet
    extra = 0  # Don't show extra empty forms by default
    show_change_link = False
    autocomplete_fields = ['category']
    fields = ['category']  # Remove 'activity' field as it's automatically set
    exclude = ['deleted_at', 'restored_at', 'is_active', 'external_id', 'created_at', 'updated_at']

    def get_formset(self, request, obj=None, **kwargs):
        kwargs['formset'] = self.formset
        formset = super().get_formset(request, obj, **kwargs)
        for field_name in self.autocomplete_fields:
            widget = formset.form.base_fields[field_name].widget
            widget.can_add_related = False
            widget.can_change_related = False
            widget.can_view_related = False
        return formset

    def get_extra(self, request, obj=None, **kwargs):
        """Control number of extra empty forms"""
        if obj and obj.pk:
            # For existing objects, check if they have any categories
            existing_count = obj.categories.count()
            if existing_count > 0:
                return 0  # Don't show extra forms if there are existing categories
            else:
                return 1  # Show one empty form if no categories exist
        # For new objects, show one empty form
        return 1

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        """Filter categories by CustomActivityCategory objects (not system Category objects)"""
        if db_field.name == "category":
            # Show CustomActivityCategory objects, not system Category objects
            from packages.models import CustomActivityCategory
            kwargs["queryset"] = CustomActivityCategory.objects.all().order_by('name')
        return super().formfield_for_foreignkey(db_field, request, **kwargs)

    def has_add_permission(self, request, obj=None):
        return request.user.is_staff
    
    def has_change_permission(self, request, obj=None):
        return request.user.is_staff
    
    def has_delete_permission(self, request, obj=None):
        return request.user.is_staff
    
    def has_view_permission(self, request, obj=None):
        return request.user.is_staff


class CustomActivityLocationInline(admin.TabularInline):
    """Inline to add locations to custom activities"""
    model = CustomActivityLocationRelation
    extra = 1
    show_change_link = False
    autocomplete_fields = ['location']
    fields = ['activity', 'location']
    exclude = ['deleted_at', 'restored_at', 'is_active', 'external_id', 'created_at', 'updated_at']

    def get_formset(self, request, obj=None, **kwargs):
        """Customize formset to handle partner-specific logic"""
        formset = super().get_formset(request, obj, **kwargs)
        
        # Add custom validation or logic here if needed
        return formset

    def has_add_permission(self, request, obj=None):
        return request.user.is_staff

    def has_change_permission(self, request, obj=None):
        return request.user.is_staff

    def has_delete_permission(self, request, obj=None):
        return request.user.is_staff

    def has_view_permission(self, request, obj=None):
        return request.user.is_staff


class CustomActivityMediaInline(admin.TabularInline):
    """Inline to add media files to custom activities"""
    model = CustomActivityMedia
    fields = ['media']
    extra = 1
    show_change_link = False
    max_num = 10  # Set a reasonable limit
    fk_name = 'custom_activity'  # Explicitly specify the foreign key field name
    
    def get_formset(self, request, obj=None, **kwargs):
        logger.info(f"CustomActivityMediaInline.get_formset START - obj: {obj}")
        logger.info(f"CustomActivityMediaInline.get_formset - request.method: {request.method}")
        logger.info(f"CustomActivityMediaInline.get_formset - kwargs: {kwargs}")
        
        if obj:
            logger.info(f"CustomActivityMediaInline.get_formset - obj.pk: {obj.pk}")
            logger.info(f"CustomActivityMediaInline.get_formset - obj.title: {getattr(obj, 'title', 'No title')}")
            try:
                media_count = obj.media.count()
                logger.info(f"CustomActivityMediaInline.get_formset - obj.media.count(): {media_count}")
                
                # Log existing media
                for i, media in enumerate(obj.media.all()):
                    logger.info(f"CustomActivityMediaInline.get_formset - existing media {i}: {media} (pk: {media.pk}, file: {media.media})")
            except Exception as e:
                logger.error(f"CustomActivityMediaInline.get_formset - error getting media count: {e}")
        else:
            logger.info(f"CustomActivityMediaInline.get_formset - obj is None (new object)")
        
        try:
            logger.info(f"CustomActivityMediaInline.get_formset - calling super().get_formset()")
            formset = super().get_formset(request, obj, **kwargs)
            logger.info(f"CustomActivityMediaInline.get_formset - super().get_formset() successful")
            logger.info(f"CustomActivityMediaInline.get_formset - formset type: {type(formset)}")
            logger.info(f"CustomActivityMediaInline.get_formset - formset: {formset}")
            
            # Log formset attributes
            if hasattr(formset, 'model'):
                logger.info(f"CustomActivityMediaInline.get_formset - formset.model: {formset.model}")
            if hasattr(formset, 'fk'):
                logger.info(f"CustomActivityMediaInline.get_formset - formset.fk: {formset.fk}")
            if hasattr(formset, 'prefix'):
                logger.info(f"CustomActivityMediaInline.get_formset - formset.prefix: {formset.prefix}")
            
            return formset
            
        except Exception as e:
            logger.error(f"CustomActivityMediaInline.get_formset - ERROR: {e}")
            logger.error(f"CustomActivityMediaInline.get_formset - ERROR type: {type(e)}")
            import traceback
            logger.error(f"CustomActivityMediaInline.get_formset - ERROR traceback: {traceback.format_exc()}")
            raise
    
    def get_extra(self, request, obj=None, **kwargs):
        logger.info(f"CustomActivityMediaInline.get_extra - obj: {obj}")
        if obj and obj.pk:
            try:
                media_count = obj.media.count()
                logger.info(f"CustomActivityMediaInline.get_extra - media_count: {media_count}")
                if media_count >= 10:  # max_num limit
                    logger.info(f"CustomActivityMediaInline.get_extra - returning 0 (reached max)")
                    return 0
                else:
                    logger.info(f"CustomActivityMediaInline.get_extra - returning 1")
                    return 1
            except Exception as e:
                logger.error(f"CustomActivityMediaInline.get_extra - error: {e}")
                return 1
        logger.info(f"CustomActivityMediaInline.get_extra - returning 1 (new object or no pk)")
        return 1
    
    def has_add_permission(self, request, obj=None):
        result = request.user.is_staff
        logger.info(f"CustomActivityMediaInline.has_add_permission - result: {result}")
        return result
    
    def has_change_permission(self, request, obj=None):
        result = request.user.is_staff
        logger.info(f"CustomActivityMediaInline.has_change_permission - result: {result}")
        return result
    
    def has_delete_permission(self, request, obj=None):
        result = request.user.is_staff
        logger.info(f"CustomActivityMediaInline.has_delete_permission - result: {result}")
        return result
    
    def has_view_permission(self, request, obj=None):
        result = request.user.is_staff
        logger.info(f"CustomActivityMediaInline.has_view_permission - result: {result}")
        return result


class CustomActivitySystemCategoryInline(admin.TabularInline):
    """Inline to add system categories to custom activities"""
    model = CustomActivitySystemCategoryRelation
    extra = 1
    show_change_link = False
    autocomplete_fields = ['category']
    fields = ['activity', 'category']
    exclude = ['deleted_at', 'restored_at', 'is_active', 'external_id', 'created_at', 'updated_at']

    def get_formset(self, request, obj=None, **kwargs):
        formset = super().get_formset(request, obj, **kwargs)
        for field_name in self.autocomplete_fields:
            widget = formset.form.base_fields[field_name].widget
            widget.can_add_related = False
            widget.can_change_related = False
            widget.can_view_related = False
        return formset

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        """Filter categories by partner (same as activity's destination partner) and active status"""
        if db_field.name == "category":
            # Show all active system categories
            kwargs["queryset"] = Category.objects.filter(is_active=True).order_by('title')
        return super().formfield_for_foreignkey(db_field, request, **kwargs)

    def has_add_permission(self, request, obj=None):
        return request.user.is_staff
    
    def has_change_permission(self, request, obj=None):
        return request.user.is_staff
    
    def has_delete_permission(self, request, obj=None):
        return request.user.is_staff
    
    def has_view_permission(self, request, obj=None):
        return request.user.is_staff


class BestsellerFilter(admin.SimpleListFilter):
    """Custom filter for bestseller status"""
    title = 'Bestseller Status'
    parameter_name = 'bestseller_status'

    def lookups(self, request, model_admin):
        return (
            ('yes', 'Bestseller'),
            ('no', 'Not Bestseller'),
            ('null', 'Unknown'),
        )

    def queryset(self, request, queryset):
        if self.value() == 'yes':
            return queryset.filter(bestseller=True)
        if self.value() == 'no':
            return queryset.filter(bestseller=False)
        if self.value() == 'null':
            return queryset.filter(bestseller__isnull=True)
        return queryset


class CertifiedFilter(admin.SimpleListFilter):
    """Custom filter for certified status"""
    title = 'Certified Status'
    parameter_name = 'certified_status'

    def lookups(self, request, model_admin):
        return (
            ('yes', 'Certified'),
            ('no', 'Not Certified'),
            ('null', 'Unknown'),
        )

    def queryset(self, request, queryset):
        if self.value() == 'yes':
            return queryset.filter(certified=True)
        if self.value() == 'no':
            return queryset.filter(certified=False)
        if self.value() == 'null':
            return queryset.filter(certified__isnull=True)
        return queryset


class OverallRatingFilter(admin.SimpleListFilter):
    """Custom filter for overall rating ranges"""
    title = 'Overall Rating'
    parameter_name = 'overall_rating_range'

    def lookups(self, request, model_admin):
        return (
            ('5_stars', '5.0 Stars (Perfect)'),
            ('4_5_plus', '4.5+ Stars (Excellent)'),
            ('4_plus', '4.0+ Stars (Very Good)'),
            ('3_5_plus', '3.5+ Stars (Good)'),
            ('3_plus', '3.0+ Stars (Average)'),
            ('below_3', 'Below 3.0 Stars (Poor)'),
            ('no_rating', 'No Rating'),
        )

    def queryset(self, request, queryset):
        if self.value() == '5_stars':
            return queryset.filter(overall_rating=5.0)
        if self.value() == '4_5_plus':
            return queryset.filter(overall_rating__gte=4.5)
        if self.value() == '4_plus':
            return queryset.filter(overall_rating__gte=4.0)
        if self.value() == '3_5_plus':
            return queryset.filter(overall_rating__gte=3.5)
        if self.value() == '3_plus':
            return queryset.filter(overall_rating__gte=3.0)
        if self.value() == 'below_3':
            return queryset.filter(overall_rating__lt=3.0, overall_rating__isnull=False)
        if self.value() == 'no_rating':
            return queryset.filter(overall_rating__isnull=True)
        return queryset


class NumberOfRatingsFilter(admin.SimpleListFilter):
    """Custom filter for number of ratings ranges"""
    title = 'Number of Ratings'
    parameter_name = 'number_of_ratings_range'

    def lookups(self, request, model_admin):
        return (
            ('1000_plus', '1000+ Reviews (Very Popular)'),
            ('500_plus', '500+ Reviews (Popular)'),
            ('100_plus', '100+ Reviews (Well-reviewed)'),
            ('50_plus', '50+ Reviews (Moderate)'),
            ('10_plus', '10+ Reviews (Some feedback)'),
            ('1_plus', '1+ Reviews (Has reviews)'),
            ('no_reviews', 'No Reviews'),
        )

    def queryset(self, request, queryset):
        if self.value() == '1000_plus':
            return queryset.filter(number_of_ratings__gte=1000)
        if self.value() == '500_plus':
            return queryset.filter(number_of_ratings__gte=500)
        if self.value() == '100_plus':
            return queryset.filter(number_of_ratings__gte=100)
        if self.value() == '50_plus':
            return queryset.filter(number_of_ratings__gte=50)
        if self.value() == '10_plus':
            return queryset.filter(number_of_ratings__gte=10)
        if self.value() == '1_plus':
            return queryset.filter(number_of_ratings__gte=1)
        if self.value() == 'no_reviews':
            return queryset.filter(number_of_ratings__isnull=True)
        return queryset


class BestsellerFilter(admin.SimpleListFilter):
    """Custom filter for bestseller status"""
    title = 'Bestseller Status'
    parameter_name = 'bestseller_status'

    def lookups(self, request, model_admin):
        return (
            ('yes', 'Bestseller'),
            ('no', 'Not Bestseller'),
            ('null', 'Unknown'),
        )

    def queryset(self, request, queryset):
        if self.value() == 'yes':
            return queryset.filter(bestseller=True)
        if self.value() == 'no':
            return queryset.filter(bestseller=False)
        if self.value() == 'null':
            return queryset.filter(bestseller__isnull=True)
        return queryset


class CertifiedFilter(admin.SimpleListFilter):
    """Custom filter for certified status"""
    title = 'Certified Status'
    parameter_name = 'certified_status'

    def lookups(self, request, model_admin):
        return (
            ('yes', 'Certified'),
            ('no', 'Not Certified'),
            ('null', 'Unknown'),
        )

    def queryset(self, request, queryset):
        if self.value() == 'yes':
            return queryset.filter(certified=True)
        if self.value() == 'no':
            return queryset.filter(certified=False)
        if self.value() == 'null':
            return queryset.filter(certified__isnull=True)
        return queryset


class OverallRatingFilter(admin.SimpleListFilter):
    """Custom filter for overall rating ranges"""
    title = 'Overall Rating'
    parameter_name = 'overall_rating_range'

    def lookups(self, request, model_admin):
        return (
            ('5_stars', '5.0 Stars (Perfect)'),
            ('4_5_plus', '4.5+ Stars (Excellent)'),
            ('4_plus', '4.0+ Stars (Very Good)'),
            ('3_5_plus', '3.5+ Stars (Good)'),
            ('3_plus', '3.0+ Stars (Average)'),
            ('below_3', 'Below 3.0 Stars (Poor)'),
            ('no_rating', 'No Rating'),
        )

    def queryset(self, request, queryset):
        if self.value() == '5_stars':
            return queryset.filter(overall_rating=5.0)
        if self.value() == '4_5_plus':
            return queryset.filter(overall_rating__gte=4.5)
        if self.value() == '4_plus':
            return queryset.filter(overall_rating__gte=4.0)
        if self.value() == '3_5_plus':
            return queryset.filter(overall_rating__gte=3.5)
        if self.value() == '3_plus':
            return queryset.filter(overall_rating__gte=3.0)
        if self.value() == 'below_3':
            return queryset.filter(overall_rating__lt=3.0, overall_rating__isnull=False)
        if self.value() == 'no_rating':
            return queryset.filter(overall_rating__isnull=True)
        return queryset


class NumberOfRatingsFilter(admin.SimpleListFilter):
    """Custom filter for number of ratings ranges"""
    title = 'Number of Ratings'
    parameter_name = 'number_of_ratings_range'

    def lookups(self, request, model_admin):
        return (
            ('1000_plus', '1000+ Reviews (Very Popular)'),
            ('500_plus', '500+ Reviews (Popular)'),
            ('100_plus', '100+ Reviews (Well-reviewed)'),
            ('50_plus', '50+ Reviews (Moderate)'),
            ('10_plus', '10+ Reviews (Some feedback)'),
            ('1_plus', '1+ Reviews (Has reviews)'),
            ('no_reviews', 'No Reviews'),
        )

    def queryset(self, request, queryset):
        if self.value() == '1000_plus':
            return queryset.filter(number_of_ratings__gte=1000)
        if self.value() == '500_plus':
            return queryset.filter(number_of_ratings__gte=500)
        if self.value() == '100_plus':
            return queryset.filter(number_of_ratings__gte=100)
        if self.value() == '50_plus':
            return queryset.filter(number_of_ratings__gte=50)
        if self.value() == '10_plus':
            return queryset.filter(number_of_ratings__gte=10)
        if self.value() == '1_plus':
            return queryset.filter(number_of_ratings__gte=1)
        if self.value() == 'no_reviews':
            return queryset.filter(number_of_ratings__isnull=True)
        return queryset


@admin.register(CustomActivityCategory)
class CustomActivityCategoryAdmin(admin.ModelAdmin):
    """Admin interface for Custom Activity Categories from GetYourGuide"""
    
    list_display = ('name', 'activity_count', 'created_at', 'updated_at')
    search_fields = ('name',)
    list_filter = ('created_at', 'updated_at')
    ordering = ('name',)
    
    # Remove fieldsets to show all fields in single form
    fields = ('name',)
    
    readonly_fields = ('created_at', 'updated_at', 'activity_count')
    
    def activity_count(self, obj):
        """Show count of activities using this category"""
        if obj and obj.pk:
            from django.urls import reverse
            
            # Use the M2M relationship to count activities
            count = obj.activities.count()
            
            if count > 0:
                # Create clickable link to filtered activities
                url = reverse('admin:packages_customactivity_changelist')
                filter_params = f"?categories__exact={obj.pk}"
                return format_html(
                    '<a href="{}{}" style="color: #417690; text-decoration: none;">{}</a>',
                    url, filter_params, count
                )
            return count
        return 0
    activity_count.short_description = 'Activities Count'
    
    def has_module_permission(self, request):
        """Allow access to all users with admin permissions"""
        return request.user.is_staff
    
    def has_view_permission(self, request, obj=None):
        """Allow viewing for all staff users"""
        return request.user.is_staff
    
    def has_add_permission(self, request):
        """Allow adding for all staff users"""
        return request.user.is_staff
    
    def has_change_permission(self, request, obj=None):
        """Allow editing for all staff users"""
        return request.user.is_staff
    
    def has_delete_permission(self, request, obj=None):
        """Allow deletion for all staff users"""
        return False


@admin.register(CustomActivityLocation)
class CustomActivityLocationAdmin(GISModelAdmin):
    """Admin interface for Custom Activity Locations from GetYourGuide"""
    
    list_display = ('city', 'country', 'google_place_id', 'has_coordinates', 'activity_count', 'created_at')
    search_fields = ('city', 'country', 'google_place_id')
    list_filter = ('country', 'created_at', 'updated_at')
    ordering = ('country', 'city')
    
    # Remove fieldsets to show all fields in single form
    fields = ('city', 'country', 'google_place_id', 'location_coordinates')
    
    readonly_fields = ('created_at', 'updated_at', 'has_coordinates', 'activity_count')
    
    # GIS-specific settings
    default_lon = 0.0
    default_lat = 0.0
    default_zoom = 2
    map_width = 800
    map_height = 500
    map_srid = 4326
    display_srid = 4326
    
    def has_coordinates(self, obj):
        """Show if location has coordinates"""
        if obj.location_coordinates:
            try:
                x_coord = float(obj.location_coordinates.x)
                y_coord = float(obj.location_coordinates.y)
                return format_html(
                    '<span style="color: green;">✓ ({})</span>',
                    f'{x_coord:.6f}, {y_coord:.6f}'
                )
            except (ValueError, TypeError, AttributeError):
                return format_html('<span style="color: red;">✗ Invalid coordinates</span>')
        return format_html('<span style="color: red;">✗ No coordinates</span>')
    has_coordinates.short_description = 'Coordinates'
    
    def activity_count(self, obj):
        """Show count of activities in this location"""
        if obj and obj.pk:
            from django.urls import reverse
            
            # Use the M2M relationship to count activities
            count = obj.activities.count()
            
            if count > 0:
                # Create clickable link to filtered activities
                url = reverse('admin:packages_customactivity_changelist')
                filter_params = f"?locations__exact={obj.pk}"
                return format_html(
                    '<a href="{}{}" style="color: #417690; text-decoration: none;">{}</a>',
                    url, filter_params, count
                )
            return count
        return 0
    activity_count.short_description = 'Activities Count'
    
    def has_module_permission(self, request):
        """Allow access to all users with admin permissions"""
        return request.user.is_staff
    
    def has_view_permission(self, request, obj=None):
        """Allow viewing for all staff users"""
        return request.user.is_staff
    
    def has_add_permission(self, request):
        """Allow adding for all staff users"""
        return request.user.is_staff
    
    def has_change_permission(self, request, obj=None):
        """Allow editing for all staff users"""
        return request.user.is_staff
    
    def has_delete_permission(self, request, obj=None):
        """Allow deletion for all staff users"""
        return False


@admin.register(CustomActivity)
class CustomActivityAdmin(DynamicArrayMixin, GISModelAdmin):
    """Admin interface for Custom Activities from GetYourGuide with hyperlink relations"""
    
    form = CustomActivityAdminForm
    
    # Add custom template for change form to include the Reframe Highlights With AI button
    change_form_template = 'admin/packages/customactivity_change_form.html'
    
    list_display = (
        'title_display', 'destination', 'tour_id', 'activity_type', 
        'price_display', 'rating_display', 'persona_display',  'bestseller', 'certified',
        'is_active', 'cancellation_policy_display', 'opening_hours_display',
        'coordinates_display', 'category_count', 'location_count',  'system_category_count', 'created_at'
    )
    
    search_fields = ('title', 'tour_id', 'destination__title', 'activity_type', 'description')
    
    list_filter = (
        BestsellerFilter, CertifiedFilter, OverallRatingFilter, NumberOfRatingsFilter,
        ActiveStatusFilter, 'has_pick_up', 'activity_type', 'destination', 'categories', 'locations', 
        'created_at', 'updated_at'
    )
    
    ordering = ('-created_at',)
    
    readonly_fields = (
        'created_at', 'updated_at', 'coordinates_display',
        'related_categories_link', 'related_locations_link', 'rating_display',
        'category_count', 'location_count', 'system_category_count', 'persona_display'
    )
    
    # Add inlines for M2M relationships
    inlines = [CustomActivityMediaInline, CustomActivityCategoryInline, CustomActivityLocationInline, CustomActivitySystemCategoryInline]
    
    # Remove fieldsets to show all fields in single form
    fields = (
        'destination', 'tour_id', 'title', 'abstract', 'description', 'activity_type',
        'additional_information', 'items_to_bring', 'not_allowed', 'not_suitable_for',
        'bestseller', 'certified', 'has_pick_up', 'is_active',
        'overall_rating', 'number_of_ratings', 'price', 'rating_display',
        'highlights', 'inclusions', 'exclusions', 'durations',
        'coordinates', 'coordinates_display', 'location_id',
        'opening_hours', 'cancellation_policy_text', 'cancellation_policy', 'persona', 'persona_display',
        'preferred_start_time', 'preferred_end_time',
        'addons', 'adult_price', 'child_price', 'infant_price',
        'category_count', 'location_count', 'system_category_count', 'related_categories_link', 'related_locations_link'
    )
    
    autocomplete_fields = ['destination']
    
    # GIS-specific settings
    default_lon = 0.0
    default_lat = 0.0
    default_zoom = 2
    map_width = 800
    map_height = 500
    map_srid = 4326
    display_srid = 4326
    
    def change_view(self, request, object_id, form_url='', extra_context=None):
        """Override change_view to add comprehensive logging"""
        logger.info(f"=== CustomActivityAdmin.change_view START ===")
        logger.info(f"CustomActivityAdmin.change_view - object_id: {object_id}")
        logger.info(f"CustomActivityAdmin.change_view - request.method: {request.method}")
        logger.info(f"CustomActivityAdmin.change_view - request.user: {request.user}")
        logger.info(f"CustomActivityAdmin.change_view - form_url: {form_url}")
        
        # Handle custom AI button BEFORE any formset processing
        if request.method == 'POST' and '_reframe_highlights_ai' in request.POST:
            logger.info(f"CustomActivityAdmin.change_view - AI button detected, handling early...")
            
            from django.http import HttpResponseRedirect
            from django.urls import reverse
            from django.contrib import messages
            from packages.utils.openai_activity_helper import OpenAIActivityHelper
            
            try:
                # Get the object
                obj = self.get_object(request, object_id)
                if not obj:
                    messages.error(request, 'Activity not found.')
                    return HttpResponseRedirect(reverse('admin:packages_customactivity_changelist'))
                
                logger.info(f"CustomActivityAdmin.change_view - processing AI for obj: {obj}")
                
                # Initialize OpenAI helper
                ai_helper = OpenAIActivityHelper()
                
                # Check if activity has highlights to reframe or needs new ones
                if obj.highlights and obj.highlights.strip():
                    # Reframe existing highlights
                    new_highlights = ai_helper.reframe_highlights(obj.highlights, obj.title)
                    action_type = "reframed"
                else:
                    # Generate new highlights from title
                    if not obj.title:
                        messages.error(request, f'Cannot generate highlights: Activity title is required.')
                        return HttpResponseRedirect(
                            reverse('admin:packages_customactivity_change', args=[obj.pk])
                        )
                    
                    new_highlights = ai_helper.generate_highlights(obj.title)
                    action_type = "generated"
                
                # Save the new highlights if generation was successful
                if new_highlights:
                    obj.highlights = new_highlights
                    obj.save(update_fields=['highlights'])
                    
                    # Show success message with preview of highlights
                    highlight_preview = new_highlights[:100] + "..." if len(new_highlights) > 100 else new_highlights
                    messages.success(
                        request, 
                        f'🤖 AI successfully {action_type} highlights for activity "{obj.title}". '
                    )
                else:
                    # Handle AI processing failure
                    error_details = ""
                    if ai_helper.has_errors():
                        error_details = f" Error details: {'; '.join(ai_helper.get_errors())}"
                    
                    messages.error(
                        request, 
                        f'Failed to {action_type.replace("ed", "")} highlights for activity "{obj.title}". '
                        f'Please try again later.{error_details}'
                    )
                
            except Exception as e:
                # Handle any unexpected errors
                logger.error(f"CustomActivityAdmin.change_view - AI processing error: {e}")
                messages.error(
                    request, 
                    f'An error occurred while processing highlights: {str(e)}'
                )
            
            logger.info(f"CustomActivityAdmin.change_view - AI processing complete, redirecting...")
            # Redirect back to the same page to show the message and updated content
            return HttpResponseRedirect(
                reverse('admin:packages_customactivity_change', args=[object_id])
            )
        
        if request.method == 'POST':
            logger.info(f"CustomActivityAdmin.change_view - request.POST keys: {list(request.POST.keys())}")
            # Log management form data specifically
            for key, value in request.POST.items():
                if 'TOTAL_FORMS' in key or 'INITIAL_FORMS' in key or 'media-' in key:
                    logger.info(f"CustomActivityAdmin.change_view - POST[{key}]: {value}")
            
            # Log ALL POST data for debugging
            logger.info(f"CustomActivityAdmin.change_view - FULL POST DATA:")
            for key, value in request.POST.items():
                logger.info(f"CustomActivityAdmin.change_view - POST[{key}]: {value}")
        
        try:
            # Get the object to check if it exists and has data
            obj = self.get_object(request, object_id)
            if obj:
                logger.info(f"CustomActivityAdmin.change_view - obj found: {obj} (pk: {obj.pk})")
                logger.info(f"CustomActivityAdmin.change_view - obj.title: {getattr(obj, 'title', 'No title')}")
                try:
                    media_count = obj.media.count()
                    logger.info(f"CustomActivityAdmin.change_view - obj.media.count(): {media_count}")
                    
                    # Log existing media details
                    for i, media in enumerate(obj.media.all()):
                        logger.info(f"CustomActivityAdmin.change_view - existing media {i}: {media} (pk: {media.pk}, file: {media.media})")
                except Exception as e:
                    logger.error(f"CustomActivityAdmin.change_view - error getting media: {e}")
            else:
                logger.warning(f"CustomActivityAdmin.change_view - obj NOT found for object_id: {object_id}")
        except Exception as e:
            logger.error(f"CustomActivityAdmin.change_view - error getting object: {e}")
        
        try:
            logger.info(f"CustomActivityAdmin.change_view - calling super().change_view()")
            result = super().change_view(request, object_id, form_url, extra_context)
            logger.info(f"CustomActivityAdmin.change_view - super().change_view() completed successfully")
            logger.info(f"=== CustomActivityAdmin.change_view END ===")
            return result
        except Exception as e:
            logger.error(f"CustomActivityAdmin.change_view - ERROR in super().change_view(): {e}")
            import traceback
            logger.error(f"CustomActivityAdmin.change_view - ERROR traceback: {traceback.format_exc()}")
            logger.info(f"=== CustomActivityAdmin.change_view END (WITH ERROR) ===")
            raise
    
    def get_formsets_with_inlines(self, request, obj=None):
        """Override to add logging for inline formsets"""
        logger.info(f"CustomActivityAdmin.get_formsets_with_inlines START - obj: {obj}")
        logger.info(f"CustomActivityAdmin.get_formsets_with_inlines - request.method: {request.method}")
        
        if obj:
            logger.info(f"CustomActivityAdmin.get_formsets_with_inlines - obj.pk: {obj.pk}")
            logger.info(f"CustomActivityAdmin.get_formsets_with_inlines - obj.title: {getattr(obj, 'title', 'No title')}")
        
        # Log configured inlines first
        logger.info(f"CustomActivityAdmin.get_formsets_with_inlines - configured inlines: {[inline.__name__ for inline in self.inlines]}")
        
        try:
            formsets_with_inlines = []
            
            logger.info(f"CustomActivityAdmin.get_formsets_with_inlines - calling super().get_formsets_with_inlines()")
            for i, (formset, inline) in enumerate(super().get_formsets_with_inlines(request, obj)):
                logger.info(f"CustomActivityAdmin.get_formsets_with_inlines - processing inline {i}: {inline.__class__.__name__}")
                logger.info(f"CustomActivityAdmin.get_formsets_with_inlines - formset {i} type: {type(formset)}")
                logger.info(f"CustomActivityAdmin.get_formsets_with_inlines - inline {i} model: {getattr(inline, 'model', 'No model')}")
                
                # Check permissions for each inline
                try:
                    has_view = inline.has_view_permission(request, obj)
                    has_add = inline.has_add_permission(request, obj)
                    has_change = inline.has_change_permission(request, obj)
                    has_delete = inline.has_delete_permission(request, obj)
                    logger.info(f"CustomActivityAdmin.get_formsets_with_inlines - inline {i} permissions: view={has_view}, add={has_add}, change={has_change}, delete={has_delete}")
                    
                    # If no permissions, this inline might be skipped
                    if not (has_view or has_add or has_change or has_delete):
                        logger.warning(f"CustomActivityAdmin.get_formsets_with_inlines - inline {i} has NO permissions, might be skipped!")
                except Exception as e:
                    logger.error(f"CustomActivityAdmin.get_formsets_with_inlines - error checking permissions for inline {i}: {e}")
                
                # Log specific details for media inline
                if hasattr(inline, 'model') and inline.model.__name__ == 'CustomActivityMedia':
                    logger.info(f"CustomActivityAdmin.get_formsets_with_inlines - MEDIA INLINE DETECTED")
                    logger.info(f"CustomActivityAdmin.get_formsets_with_inlines - inline.fk_name: {getattr(inline, 'fk_name', 'Not set')}")
                    logger.info(f"CustomActivityAdmin.get_formsets_with_inlines - inline.fields: {getattr(inline, 'fields', 'Not set')}")
                    
                    # Create an instance of the formset to check management form
                    try:
                        # Create formset instance with the object
                        formset_instance = formset(request.POST or None, instance=obj, prefix=f"customactivitymedia_set")
                        logger.info(f"CustomActivityAdmin.get_formsets_with_inlines - formset_instance created: {formset_instance}")
                        logger.info(f"CustomActivityAdmin.get_formsets_with_inlines - formset_instance.prefix: {formset_instance.prefix}")
                        
                        # Now access management form from the instance
                        mgmt_form = formset_instance.management_form
                        logger.info(f"CustomActivityAdmin.get_formsets_with_inlines - management_form instance: {mgmt_form}")
                        logger.info(f"CustomActivityAdmin.get_formsets_with_inlines - management_form.prefix: {mgmt_form.prefix}")
                        logger.info(f"CustomActivityAdmin.get_formsets_with_inlines - management_form fields: {list(mgmt_form.fields.keys())}")
                        
                        # Check if management form data is in POST
                        if request.method == 'POST':
                            expected_total_forms_key = f"{mgmt_form.prefix}-TOTAL_FORMS"
                            expected_initial_forms_key = f"{mgmt_form.prefix}-INITIAL_FORMS"
                            logger.info(f"CustomActivityAdmin.get_formsets_with_inlines - expected TOTAL_FORMS key: {expected_total_forms_key}")
                            logger.info(f"CustomActivityAdmin.get_formsets_with_inlines - expected INITIAL_FORMS key: {expected_initial_forms_key}")
                            logger.info(f"CustomActivityAdmin.get_formsets_with_inlines - TOTAL_FORMS in POST: {expected_total_forms_key in request.POST}")
                            logger.info(f"CustomActivityAdmin.get_formsets_with_inlines - INITIAL_FORMS in POST: {expected_initial_forms_key in request.POST}")
                            
                            if expected_total_forms_key in request.POST:
                                logger.info(f"CustomActivityAdmin.get_formsets_with_inlines - POST[{expected_total_forms_key}]: {request.POST[expected_total_forms_key]}")
                            if expected_initial_forms_key in request.POST:
                                logger.info(f"CustomActivityAdmin.get_formsets_with_inlines - POST[{expected_initial_forms_key}]: {request.POST[expected_initial_forms_key]}")
                                
                    except Exception as e:
                        logger.error(f"CustomActivityAdmin.get_formsets_with_inlines - ERROR creating formset instance: {e}")
                        import traceback
                        logger.error(f"CustomActivityAdmin.get_formsets_with_inlines - ERROR traceback: {traceback.format_exc()}")
                
                # Try to instantiate the formset to check for errors
                try:
                    test_formset = formset(instance=obj)
                    logger.info(f"CustomActivityAdmin.get_formsets_with_inlines - inline {i} formset instantiated successfully")
                except Exception as e:
                    logger.error(f"CustomActivityAdmin.get_formsets_with_inlines - ERROR instantiating formset for inline {i}: {e}")
                    import traceback
                    logger.error(f"CustomActivityAdmin.get_formsets_with_inlines - formset error traceback: {traceback.format_exc()}")
                
                formsets_with_inlines.append((formset, inline))
                logger.info(f"CustomActivityAdmin.get_formsets_with_inlines - successfully added inline {i} to list")
            
            logger.info(f"CustomActivityAdmin.get_formsets_with_inlines - returning {len(formsets_with_inlines)} formsets")
            return formsets_with_inlines
            
        except Exception as e:
            logger.error(f"CustomActivityAdmin.get_formsets_with_inlines - ERROR: {e}")
            import traceback
            logger.error(f"CustomActivityAdmin.get_formsets_with_inlines - ERROR traceback: {traceback.format_exc()}")
            raise
    
    def title_display(self, obj):
        """Display title with truncation"""
        if obj.title:
            return obj.title[:100] + '...' if len(obj.title) > 100 else obj.title
        return 'No Title'
    title_display.short_description = 'Title'
    
    def price_display(self, obj):
        """Display formatted price"""
        if obj.price:
            try:
                price_value = float(obj.price)
                return format_html('<strong>₹{}</strong>', f'{price_value:.2f}')
            except (ValueError, TypeError):
                return format_html('<span style="color: gray;">Invalid Price</span>')
        return format_html('<span style="color: gray;">No Price</span>')
    price_display.short_description = 'Price'
    
    def rating_display(self, obj):
        """Display rating with stars"""
        if obj.overall_rating and obj.number_of_ratings:
            try:
                rating_value = float(obj.overall_rating)
                rating_count = int(obj.number_of_ratings)
                stars = '★' * int(rating_value)
                empty_stars = '☆' * (5 - int(rating_value))
                return format_html(
                    '<span style="color: gold;">{}</span><span style="color: lightgray;">{}</span> '
                    '({} reviews)',
                    stars, empty_stars, f'{rating_value:.1f}/5 -', rating_count
                )
            except (ValueError, TypeError):
                return format_html('<span style="color: gray;">Invalid Rating</span>')
        elif obj.overall_rating:
            try:
                rating_value = float(obj.overall_rating)
                stars = '★' * int(rating_value)
                empty_stars = '☆' * (5 - int(rating_value))
                return format_html(
                    '<span style="color: gold;">{}</span><span style="color: lightgray;">{}</span> '
                    '({})',
                    stars, empty_stars, f'{rating_value:.1f}/5'
                )
            except (ValueError, TypeError):
                return format_html('<span style="color: gray;">Invalid Rating</span>')
        return format_html('<span style="color: gray;">No Rating</span>')
    rating_display.short_description = 'Rating'
    
    def cancellation_policy_display(self, obj):
        """Display cancellation policy text with truncation and formatting"""
        if obj.cancellation_policy_text:
            policy_text = obj.cancellation_policy_text.strip()
            if len(policy_text) > 50:
                truncated = policy_text[:50] + '...'
                return format_html(
                    '<span title="{}" style="color: #666;">{}</span>',
                    policy_text, truncated
                )
            return format_html('<span style="color: #666;">{}</span>', policy_text)
        elif obj.cancellation_policy and isinstance(obj.cancellation_policy, dict):
            # Try to extract meaningful info from JSON policy
            policy_keys = list(obj.cancellation_policy.keys())
            if policy_keys:
                policy_info = f"JSON: {', '.join(policy_keys[:2])}"
                if len(policy_keys) > 2:
                    policy_info += f" (+{len(policy_keys) - 2} more)"
                return format_html('<span style="color: #666; font-style: italic;">{}</span>', policy_info)
        return format_html('<span style="color: gray;">No Policy</span>')
    cancellation_policy_display.short_description = 'Cancellation Policy'


    def opening_hours_display(self, obj):
        """Display opening hours from first schedule entry"""
        if obj.opening_hours:
            try:
                opening_hours = obj.opening_hours

                # If it's a string in Python list/dict format (with single quotes), parse it
                if isinstance(opening_hours, str):
                    opening_hours = ast.literal_eval(opening_hours)

                if not opening_hours:
                    return "No hours"

                first = opening_hours[0]

                opening = datetime.fromisoformat(first['opening_time'])
                closing = datetime.fromisoformat(first['closing_time'])

                return f"{opening.strftime('%H:%M')} - {closing.strftime('%H:%M')}"
            except Exception as e:
                return "Invalid data"
        return "No hours"
    opening_hours_display.short_description = 'Opening Hours'
    
    def persona_display(self, obj):
        """Display persona values from ArrayField"""
        if obj.persona and len(obj.persona) > 0:
            # Format each persona value and join them
            formatted_personas = [persona.replace('_', ' ').title() for persona in obj.persona]
            return ', '.join(formatted_personas)
        return format_html('<span style="color: gray;">No Persona</span>')
    persona_display.short_description = 'Persona'
    
    def coordinates_display(self, obj):
        """Display coordinates in a readable format"""
        if obj.coordinates:
            try:
                x_coord = float(obj.coordinates.x)
                y_coord = float(obj.coordinates.y)
                return format_html(
                    '<span style="color: blue;">📍 {}</span>',
                    f'{x_coord:.6f}, {y_coord:.6f}'
                )
            except (ValueError, TypeError, AttributeError):
                return format_html('<span style="color: gray;">Invalid Coordinates</span>')
        return format_html('<span style="color: gray;">No Coordinates</span>')
    coordinates_display.short_description = 'Coordinates'
    
    def category_count(self, obj):
        """Show count of categories for this activity"""
        if obj and obj.pk:
            count = obj.categories.count()
            if count > 0:
                return format_html('<span style="color: green;">{} categories</span>', count)
            return format_html('<span style="color: gray;">No categories</span>')
        return 0
    category_count.short_description = 'Categories'
    
    def location_count(self, obj):
        """Show count of locations for this activity"""
        if obj and obj.pk:
            count = obj.locations.count()
            if count > 0:
                return format_html('<span style="color: green;">{} locations</span>', count)
            return format_html('<span style="color: gray;">No locations</span>')
        return 0
    location_count.short_description = 'Locations'
    
    def system_category_count(self, obj):
        """Show count of system categories for this activity"""
        if obj and obj.pk:
            count = obj.system_categories.count()
            if count > 0:
                if obj.destination and obj.destination.partner:
                    partner_name = obj.destination.partner.entity_name[:20]  # Truncate if too long
                    return format_html(
                        '<span style="color: green;">{} categories</span><br>'
                        '<small style="color: gray;">Partner: {}</small>', 
                        count, partner_name
                    )
                else:
                    return format_html('<span style="color: green;">{} categories</span>', count)
            return format_html('<span style="color: gray;">No categories</span>')
        return 0
    system_category_count.short_description = 'System Categories'
    
    def pictures_display(self, obj):
        """Display simple picture links"""
        if obj.media.exists():
            links = []
            for i, media_item in enumerate(obj.media.all()[:5]):  # Show max 5 links
                if media_item.media:
                    links.append('<a href="{}" target="_blank">Image {}</a>'.format(media_item.media.url, i+1))
            if links:
                return format_html('<br>'.join(links))
        return 'No Images'
    pictures_display.short_description = 'Pictures'
    
    def related_categories_link(self, obj):
        """Display hyperlink to view categories for this activity"""
        if obj and obj.pk:
            category_count = obj.categories.count()
            url = reverse('admin:packages_customactivitycategory_changelist')
            
            if category_count > 0:
                # Show the actual categories this activity belongs to
                categories = obj.categories.all()[:3]  # Show first 3
                category_names = [cat.name for cat in categories]
                display_text = ', '.join(category_names)
                if category_count > 3:
                    display_text += f" (+{category_count - 3} more)"
                
                # Add filter parameter to show only categories for this activity
                filter_params = f"?activities__exact={obj.pk}"
                
                return format_html(
                    '<a href="{}{}" target="_blank" style="color: #417690; text-decoration: none;">'
                    '🔗 {} ({})</a><br>'
                    '<small style="color: gray;">{}</small>',
                    url, filter_params, f"{category_count} Categories", category_count, display_text
                )
            else:
                return format_html(
                    '<a href="{}" target="_blank" style="color: #417690; text-decoration: none;">'
                    '🔗 View All Categories</a><br>'
                    '<small style="color: gray;">No categories assigned</small>',
                    url
                )
        else:
            url = reverse('admin:packages_customactivitycategory_changelist')
            return format_html(
                '<a href="{}" target="_blank" style="color: #417690; text-decoration: none;">'
                '🔗 View All Categories</a>',
                url
            )
    related_categories_link.short_description = 'Related Categories'
    
    def related_locations_link(self, obj):
        """Display hyperlink to view locations for this activity"""
        if obj and obj.pk:
            location_count = obj.locations.count()
            url = reverse('admin:packages_customactivitylocation_changelist')
            
            if location_count > 0:
                # Show the actual locations this activity belongs to
                locations = obj.locations.all()[:3]  # Show first 3
                location_names = [str(loc) for loc in locations]
                display_text = ', '.join(location_names)
                if location_count > 3:
                    display_text += f" (+{location_count - 3} more)"
                
                # Add filter parameter to show only locations for this activity
                filter_params = f"?activities__exact={obj.pk}"
                
                return format_html(
                    '<a href="{}{}" target="_blank" style="color: #417690; text-decoration: none;">'
                    '🔗 {} ({})</a><br>'
                    '<small style="color: gray;">{}</small>',
                    url, filter_params, f"{location_count} Locations", location_count, display_text
                )
            else:
                return format_html(
                    '<a href="{}" target="_blank" style="color: #417690; text-decoration: none;">'
                    '🔗 View All Locations</a><br>'
                    '<small style="color: gray;">No locations assigned</small>',
                    url
                )
        else:
            url = reverse('admin:packages_customactivitylocation_changelist')
            return format_html(
                '<a href="{}" target="_blank" style="color: #417690; text-decoration: none;">'
                '🔗 View All Locations</a>',
                url
            )
    related_locations_link.short_description = 'Related Locations'
    
    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        """Filter destinations for dropdown"""
        if db_field.name == "destination":
            # Show all destinations but you could filter based on user permissions
            kwargs["queryset"] = Destination.objects.filter(is_active=True).order_by('title')
        return super().formfield_for_foreignkey(db_field, request, **kwargs)
    
    def get_queryset(self, request):
        """Optimize queryset with select_related"""
        return super().get_queryset(request).select_related('destination')
    
    def has_module_permission(self, request):
        """Allow access to all users with admin permissions"""
        return request.user.is_staff
    
    def has_view_permission(self, request, obj=None):
        """Allow viewing for all staff users"""
        return request.user.is_staff
    
    def has_add_permission(self, request):
        """Allow adding for all staff users"""
        return request.user.is_staff
    
    def has_change_permission(self, request, obj=None):
        """Allow editing for all staff users"""
        return request.user.is_staff
    
    def has_delete_permission(self, request, obj=None):
        """Allow deletion for all staff users"""
        return False
    
    def response_change(self, request, obj):
        """Override to handle custom button actions BEFORE formset processing"""
        # Handle custom button actions BEFORE Django processes formsets
        if '_reframe_highlights_ai' in request.POST:
            logger.info(f"CustomActivityAdmin.response_change - AI button clicked, processing...")
            
            from django.http import HttpResponseRedirect
            from django.urls import reverse
            from django.contrib import messages
            from packages.utils.openai_activity_helper import OpenAIActivityHelper
            
            try:
                # Initialize OpenAI helper
                ai_helper = OpenAIActivityHelper()
                
                # Check if activity has highlights to reframe or needs new ones
                if obj.highlights and obj.highlights.strip():
                    # Reframe existing highlights
                    new_highlights = ai_helper.reframe_highlights(obj.highlights, obj.title)
                    action_type = "reframed"
                else:
                    # Generate new highlights from title
                    if not obj.title:
                        messages.error(request, f'Cannot generate highlights: Activity title is required.')
                        return HttpResponseRedirect(
                            reverse('admin:packages_customactivity_change', args=[obj.pk])
                        )
                    
                    new_highlights = ai_helper.generate_highlights(obj.title)
                    action_type = "generated"
                
                # Save the new highlights if generation was successful
                if new_highlights:
                    obj.highlights = new_highlights
                    obj.save(update_fields=['highlights'])
                    
                    # Show success message with preview of highlights
                    highlight_preview = new_highlights[:100] + "..." if len(new_highlights) > 100 else new_highlights
                    messages.success(
                        request, 
                        f'🤖 AI successfully {action_type} highlights for activity "{obj.title}". '
                    )
                else:
                    # Handle AI processing failure
                    error_details = ""
                    if ai_helper.has_errors():
                        error_details = f" Error details: {'; '.join(ai_helper.get_errors())}"
                    
                    messages.error(
                        request, 
                        f'Failed to {action_type.replace("ed", "")} highlights for activity "{obj.title}". '
                        f'Please try again later.{error_details}'
                    )
                
            except Exception as e:
                # Handle any unexpected errors
                messages.error(
                    request, 
                    f'An error occurred while processing highlights for activity "{obj.title}": {str(e)}'
                )
            
            logger.info(f"CustomActivityAdmin.response_change - AI processing complete, redirecting...")
            # Redirect back to the same page to show the message and updated content
            return HttpResponseRedirect(
                reverse('admin:packages_customactivity_change', args=[obj.pk])
            )
        
        # For normal form submissions, call the parent method
        logger.info(f"CustomActivityAdmin.response_change - normal form submission, calling super()...")
        return super().response_change(request, obj)
    
    # def formfield_for_dbfield(self, db_field, request, **kwargs):
    #     """Ensure array fields are properly handled"""
    #     # Let DynamicArrayMixin handle array fields
    #     formfield = super().formfield_for_dbfield(db_field, request, **kwargs)
    #     return formfield


# =============================================================================
# CUSTOM PACKAGE ADMIN (CustomPackage Proxy Model)
# =============================================================================

# NOTE: ItineraryHotelInline and ItineraryActivityInline were removed because
# ItineraryHotel and ItineraryActivity models don't have FK to ItineraryDayItem.
# Instead, ItineraryDayItem has FK to these models, so they're managed directly
# in the ItineraryDayItem admin form.

class ItineraryInline(admin.StackedInline):
    """Inline to add itineraries to custom packages"""
    model = Itinerary
    form = ItineraryInlineForm
    extra = 0  # Don't show extra empty forms by default
    fields = ['day_number', 'date', 'day_title', 'description', 'order', 'inclusions']
    
    def save_formset(self, request, form, formset, change):
        """Override to set partner for each itinerary"""
        logger.info("ItineraryInline.save_formset called")
        logger.debug(f"ItineraryInline.save_formset change: {change}")
        
        instances = formset.save(commit=False)
        logger.info(f"ItineraryInline.save_formset saving {len(instances)} instances")
        
        effective_partner = get_user_effective_partner(request)
        logger.debug(f"ItineraryInline.save_formset effective_partner: {effective_partner}")
        
        for instance in instances:
            if not instance.partner:
                logger.debug(f"ItineraryInline.save_formset setting partner on instance {instance.pk if instance.pk else 'new'}")
                instance.partner = effective_partner
            instance.save()
            logger.debug(f"ItineraryInline.save_formset saved instance {instance.pk}")
        
        formset.save_m2m()
        logger.info("ItineraryInline.save_formset completed successfully")


class ItineraryDayItemInline(admin.StackedInline):
    """Inline to add day items to itineraries"""
    model = ItineraryDayItem
    form = ItineraryDayItemInlineForm
    extra = 1
    fields = ['type', 'title', 'description', 'order', 'duration', 'hotel_selection', 'activity_selection', 'inclusions']
    autocomplete_fields = ['itinerary']
    
    def save_formset(self, request, form, formset, change):
        """Save formset with partner assignment"""
        logger.info("ItineraryDayItemInline.save_formset called")
        logger.debug(f"ItineraryDayItemInline.save_formset change: {change}")
        
        instances = formset.save(commit=False)
        logger.info(f"ItineraryDayItemInline.save_formset saving {len(instances)} instances")
        
        effective_partner = get_user_effective_partner(request)
        logger.debug(f"ItineraryDayItemInline.save_formset effective_partner: {effective_partner}")
        
        for instance in instances:
            if not instance.partner:
                logger.debug(f"ItineraryDayItemInline.save_formset setting partner on instance {instance.pk if instance.pk else 'new'}")
                instance.partner = effective_partner
            instance.save()
            logger.debug(f"ItineraryDayItemInline.save_formset saved instance {instance.pk}")
        
        formset.save_m2m()
        logger.info("ItineraryDayItemInline.save_formset completed successfully")


@admin.register(CustomPackage)
class CustomPackageAdmin(admin.ModelAdmin, DynamicArrayMixin):
    """Admin interface for CustomPackages with itinerary management link"""
    form = CustomPackageForm
    list_display = ('title', 'package_no', 'destination', 'price_per_person_display', 'duration', 'duration_in_nights', 'duration_in_days', 'is_published', 'itineraries_link', 'created_at')
    search_fields = ('title', 'package_no', 'destination__title')
    list_filter = (PublishStatusFilter, 'type', 'created_at')
    readonly_fields = ('created_at', 'updated_at', 'type', 'itineraries_link')
    inlines = [PackageMediaInline, PackageCategoryInline, ItineraryInline]
    autocomplete_fields = ['destination']
    
    def price_per_person_display(self, obj):
        """Display price per person rounded to 2 decimal places"""
        if obj.price_per_person:
            try:
                # Clean the price_per_person value - remove currency symbols and non-numeric characters
                price_str = str(obj.price_per_person).strip()
                
                # Remove common currency symbols and whitespace
                # Remove currency symbols like ₹, $, USD, etc. and keep only numbers and decimal point
                cleaned_price = re.sub(r'[^\d.-]', '', price_str)
                
                # Handle empty string after cleaning
                if not cleaned_price or cleaned_price in ['-', '.']:
                    logger.warning(f"CustomPackage {obj.id}: price_per_person '{price_str}' resulted in empty value after cleaning")
                    return obj.price_per_person  # Return original value if can't clean
                
                price_decimal = Decimal(cleaned_price)
                rounded_price = price_decimal.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
                
                logger.debug(f"CustomPackage ID: {obj.id}, Title: {obj.title}")
                logger.debug(f"obj.price_per_person: {obj.price_per_person} -> cleaned: {cleaned_price} -> rounded: {rounded_price}")
                
                return str(rounded_price)
            except (ValueError, TypeError, decimal.InvalidOperation) as e:
                logger.error(f"Error formatting price for CustomPackage {obj.id} ('{obj.title}'): {e}")
                logger.error(f"  price_per_person value: '{obj.price_per_person}' (type: {type(obj.price_per_person)})")
                # Return the original value if we can't format it
                return str(obj.price_per_person)
        return "N/A"
    price_per_person_display.short_description = 'Price per Person'
    
    def itineraries_link(self, obj):
        """Show link to manage itineraries for this package"""
        logger.debug(f"CustomPackageAdmin.itineraries_link called for obj: {obj.pk if obj else 'None'}")
        
        from django.urls import reverse
        from django.utils.html import format_html
        
        if obj and obj.pk:
            # Count existing itineraries
            itineraries_count = obj.itineraries.count()
            logger.debug(f"CustomPackageAdmin.itineraries_link found {itineraries_count} itineraries for package {obj.pk}")
            
            # Create link to filtered Itinerary admin
            url = reverse('admin:packages_itinerary_changelist')
            filter_params = f"?package__exact={obj.pk}"
            
            logger.debug(f"CustomPackageAdmin.itineraries_link generating link for package {obj.pk}")
            return format_html(
                '<div style="margin: 10px 0;">'
                '<strong>Itineraries ({} items):</strong><br>'
                '<a href="{}{}" target="_blank" style="color: #417690; text-decoration: none;">'
                '📋 View All Itineraries</a>'
                '</div>',
                itineraries_count, url, filter_params
            )
        
        logger.debug("CustomPackageAdmin.itineraries_link package not saved yet, showing save message")
        return format_html('<em style="color: gray;">Save package first to manage itineraries</em>')
    
    itineraries_link.short_description = 'Manage Itineraries'
    
    def get_form(self, request, obj=None, **kwargs):
        logger.info("CustomPackageAdmin.get_form called")
        logger.debug(f"CustomPackageAdmin.get_form request method: {request.method}")
        logger.debug(f"CustomPackageAdmin.get_form obj: {obj.pk if obj else 'None'}")
        
        form = super().get_form(request, obj, **kwargs)
        logger.debug("CustomPackageAdmin.get_form super().get_form completed")
        
        # Make the form aware of request for partner filtering
        class RequestAwareForm(form):
            def __init__(self, *args, **kwargs):
                logger.debug("CustomPackageAdmin.get_form RequestAwareForm.__init__ called")
                kwargs['request'] = request
                super().__init__(*args, **kwargs)
                logger.debug("CustomPackageAdmin.get_form RequestAwareForm.__init__ completed")
        
        logger.info("CustomPackageAdmin.get_form completed successfully")
        return RequestAwareForm
    
    def get_fields(self, request, obj=None):
        """Show all fields in a single form, with itineraries_link only for existing packages"""
        logger.info("CustomPackageAdmin.get_fields called")
        logger.debug(f"CustomPackageAdmin.get_fields request method: {request.method}")
        logger.debug(f"CustomPackageAdmin.get_fields obj: {obj.pk if obj else 'None'}")
        
        fields = [
            'title', 'package_no', 'destination', 'type', 'duration', 'duration_in_nights', 'duration_in_days', 
            'price_per_person', 'currency_conversion_rate', 'owner', 'about_this_tour', 'highlights', 'inclusions', 
            'addons', 'exclusions', 'visa_type', 'best_time_to_visit', 'destination_safety', 
            'rating', 'rating_description', 'popular_restaurants', 'popular_activities', 
            # 'hotels', 
            'cultural_info', 'what_to_shop', 'what_to_pack', 'important_notes', 'explore_order', 
            'is_published', 'is_active', 'created_at', 'updated_at'
        ]
        
        # Add itineraries_link only for existing packages (not in add view)
        if obj is not None:  # Change view
            logger.debug("CustomPackageAdmin.get_fields adding itineraries_link for existing package")
            fields.append('itineraries_link')
        else:
            logger.debug("CustomPackageAdmin.get_fields skipping itineraries_link for new package")
        
        logger.debug(f"CustomPackageAdmin.get_fields returning {len(fields)} fields")
        return fields
    
    def get_queryset(self, request):
        """Filter packages by user's effective partner and CustomPackage types only"""
        logger.info("CustomPackageAdmin.get_queryset called")
        logger.debug(f"CustomPackageAdmin.get_queryset request method: {request.method}")
        
        qs = super().get_queryset(request)
        effective_partner = get_user_effective_partner(request)
        qs = qs.filter(type__in=PackageTypeChoices.custom_package_choices())
        logger.debug(f"CustomPackageAdmin.get_queryset effective_partner: {effective_partner}")
        
        if effective_partner:
            filtered_qs = qs.filter(
                partner=effective_partner,
                type__in=PackageTypeChoices.custom_package_choices()
            )
            logger.info(f"CustomPackageAdmin.get_queryset returning {filtered_qs.count()} custom packages")
            return filtered_qs
        
        logger.warning("CustomPackageAdmin.get_queryset no effective partner, returning empty queryset")
        return qs.none()
    
    def save_model(self, request, obj, form, change):
        """Save the CustomPackage model with comprehensive logging"""
        logger.info("CustomPackageAdmin.save_model called")
        logger.debug(f"CustomPackageAdmin.save_model change: {change}")
        logger.debug(f"CustomPackageAdmin.save_model obj pk: {obj.pk if obj else 'None'}")
        logger.debug(f"CustomPackageAdmin.save_model form is_valid: {form.is_valid()}")
        
        # Set partner if not already set
        if not change:  # Only for new packages
            effective_partner = get_user_effective_partner(request)
            logger.debug(f"CustomPackageAdmin.save_model setting partner for new package: {effective_partner}")
            obj.partner = effective_partner
        
        logger.debug("CustomPackageAdmin.save_model calling super().save_model")
        super().save_model(request, obj, form, change)
        logger.info(f"CustomPackageAdmin.save_model completed successfully, obj pk: {obj.pk}")
    
    def save_related(self, request, form, formsets, change):
        """Save related objects and process highlights/inclusions/addons"""
        logger.info("CustomPackageAdmin.save_related called")
        logger.debug(f"CustomPackageAdmin.save_related change: {change}")
        logger.debug(f"CustomPackageAdmin.save_related formsets count: {len(formsets)}")
        
        # Process each formset
        for i, formset in enumerate(formsets, 1):
            formset_name = formset.model.__name__
            logger.debug(f"CustomPackageAdmin.save_related processing formset {i}/{len(formsets)}: {formset_name}")
        
        # Call the parent save_related to handle formsets
        logger.debug("CustomPackageAdmin.save_related calling super().save_related")
        super().save_related(request, form, formsets, change)
        
        # Now call the form's save_related to process highlights/inclusions/addons
        if hasattr(form, 'save_related'):
            logger.debug("CustomPackageAdmin.save_related calling form.save_related")
            form.save_related(form.instance)
        
        logger.info("CustomPackageAdmin.save_related completed successfully")
    
    def response_add(self, request, obj, post_url_continue=None):
        """Handle response after adding a new CustomPackage"""
        logger.info("CustomPackageAdmin.response_add called")
        logger.debug(f"CustomPackageAdmin.response_add obj pk: {obj.pk if obj else 'None'}")
        
        response = super().response_add(request, obj, post_url_continue)
        logger.debug(f"CustomPackageAdmin.response_add response type: {type(response).__name__}")
        logger.info("CustomPackageAdmin.response_add completed successfully")
        return response
    
    def response_change(self, request, obj):
        """Handle response after changing a CustomPackage"""
        logger.info("CustomPackageAdmin.response_change called")
        logger.debug(f"CustomPackageAdmin.response_change obj pk: {obj.pk if obj else 'None'}")
        
        response = super().response_change(request, obj)
        logger.debug(f"CustomPackageAdmin.response_change response type: {type(response).__name__}")
        logger.info("CustomPackageAdmin.response_change completed successfully")
        return response
    
    def has_module_permission(self, request):
        """Check if user has module permissions"""
        logger.debug("CustomPackageAdmin.has_module_permission called")
        result = user_can_manage_packages(request)
        logger.debug(f"CustomPackageAdmin.has_module_permission result: {result}")
        return result
    
    def has_view_permission(self, request, obj=None):
        """Check if user has view permissions"""
        logger.debug(f"CustomPackageAdmin.has_view_permission called for obj: {obj.pk if obj else 'None'}")
        result = user_can_manage_packages(request)
        logger.debug(f"CustomPackageAdmin.has_view_permission result: {result}")
        return result
    
    def has_add_permission(self, request):
        """Check if user has add permissions"""
        logger.debug("CustomPackageAdmin.has_add_permission called")
        result = user_can_manage_packages(request)
        logger.debug(f"CustomPackageAdmin.has_add_permission result: {result}")
        return result
    
    def has_change_permission(self, request, obj=None):
        """Check if user has change permissions"""
        logger.debug(f"CustomPackageAdmin.has_change_permission called for obj: {obj.pk if obj else 'None'}")
        result = user_can_manage_packages(request)
        logger.debug(f"CustomPackageAdmin.has_change_permission result: {result}")
        return result
    
    def has_delete_permission(self, request, obj=None):
        """Check if user has delete permissions"""
        logger.debug(f"CustomPackageAdmin.has_delete_permission called for obj: {obj.pk if obj else 'None'}")
        result = user_can_manage_packages(request)
        logger.debug(f"CustomPackageAdmin.has_delete_permission result: {result}")
        return result


@admin.register(Itinerary)
class ItineraryAdmin(admin.ModelAdmin, DynamicArrayMixin):
    """Admin for managing Itineraries with day items"""
    form = ItineraryInlineForm
    list_display = ('package', 'day_number', 'day_title', 'date', 'order', 'day_items_count', 'partner', 'created_at')
    search_fields = ('package__title', 'day_title', 'description')
    list_filter = ('partner', 'day_number', 'date', 'created_at')
    autocomplete_fields = ['package']
    inlines = [ItineraryDayItemInline]
    
    # Single form with all fields - no fieldsets
    fields = ('package', 'day_number', 'day_title', 'date', 'order', 'description', 'inclusions')
    
    def day_items_count(self, obj):
        """Show count of day items for this itinerary"""
        logger.debug(f"ItineraryAdmin.day_items_count called for obj {obj.pk if obj else 'None'}")
        count = obj.day_items.count()
        logger.debug(f"ItineraryAdmin.day_items_count returning {count}")
        return count
    day_items_count.short_description = 'Day Items Count'
    
    def get_queryset(self, request):
        """Filter by user's effective partner and CustomPackage types only"""
        logger.info("ItineraryAdmin.get_queryset called")
        logger.debug(f"ItineraryAdmin.get_queryset request method: {request.method}")
        
        qs = super().get_queryset(request)
        effective_partner = get_user_effective_partner(request)
        logger.debug(f"ItineraryAdmin.get_queryset effective_partner: {effective_partner}")
        
        if effective_partner:
            filtered_qs = qs.filter(
                partner=effective_partner,
                package__type__in=PackageTypeChoices.custom_package_choices()
            )
            logger.info(f"ItineraryAdmin.get_queryset returning {filtered_qs.count()} itineraries")
            return filtered_qs
        
        logger.warning("ItineraryAdmin.get_queryset no effective partner, returning empty queryset")
        return qs.none()

    def save_model(self, request, obj, form, change):
        """Auto-assign partner based on user"""
        logger.info("ItineraryAdmin.save_model called")
        logger.debug(f"ItineraryAdmin.save_model change: {change}, obj pk: {obj.pk if obj else 'None'}")
        
        if not change:  # Only for new itineraries
            effective_partner = get_user_effective_partner(request)
            logger.debug(f"ItineraryAdmin.save_model setting partner for new itinerary: {effective_partner}")
            obj.partner = effective_partner
        
        super().save_model(request, obj, form, change)
        logger.info(f"ItineraryAdmin.save_model completed, obj pk: {obj.pk}")

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        """Filter packages by user's effective partner and CustomPackage types"""
        logger.debug(f"ItineraryAdmin.formfield_for_foreignkey called for field: {db_field.name}")
        
        if db_field.name == "package":
            effective_partner = get_user_effective_partner(request)
            logger.debug(f"ItineraryAdmin.formfield_for_foreignkey effective_partner: {effective_partner}")
            
            if effective_partner:
                filtered_packages = Package.objects.filter(
                    partner=effective_partner,
                    type__in=PackageTypeChoices.custom_package_choices()
                )
                logger.debug(f"ItineraryAdmin.formfield_for_foreignkey filtered to {filtered_packages.count()} packages")
                kwargs["queryset"] = filtered_packages
            else:
                logger.warning("ItineraryAdmin.formfield_for_foreignkey no effective partner, using empty queryset")
                kwargs["queryset"] = Package.objects.none()
        
        return super().formfield_for_foreignkey(db_field, request, **kwargs)

    def has_module_permission(self, request):
        """Only allow users who can manage packages"""
        return user_can_manage_packages(request)
    
    def has_view_permission(self, request, obj=None):
        """Only allow users who can manage packages"""
        return user_can_manage_packages(request)
    
    def has_add_permission(self, request):
        """Only allow users who can manage packages"""
        return user_can_manage_packages(request)
    
    def has_change_permission(self, request, obj=None):
        """Only allow users who can manage packages"""
        return user_can_manage_packages(request)
    
    def has_delete_permission(self, request, obj=None):
        """Allow deletion for itineraries"""
        return user_can_manage_packages(request)


@admin.register(ItineraryDayItem)
class ItineraryDayItemAdmin(admin.ModelAdmin, DynamicArrayMixin):
    """Admin for managing individual itinerary day items"""
    form = ItineraryDayItemInlineForm
    list_display = ('itinerary', 'type', 'title', 'order', 'duration', 'partner', 'created_at')
    search_fields = ('itinerary__package__title', 'itinerary__day_title', 'title', 'description')
    list_filter = ('partner', 'type', 'created_at')
    autocomplete_fields = ['itinerary']
    
    # Single form with all fields - no fieldsets
    fields = ('itinerary', 'type', 'title', 'description', 'order', 'duration', 'hotel_selection', 'activity_selection', 'inclusions')
    
    def get_queryset(self, request):
        """Filter by user's effective partner"""
        logger.info("ItineraryDayItemAdmin.get_queryset called")
        logger.debug(f"ItineraryDayItemAdmin.get_queryset request method: {request.method}")
        
        qs = super().get_queryset(request)
        effective_partner = get_user_effective_partner(request)
        logger.debug(f"ItineraryDayItemAdmin.get_queryset effective_partner: {effective_partner}")
        
        if effective_partner:
            filtered_qs = qs.filter(partner=effective_partner)
            logger.info(f"ItineraryDayItemAdmin.get_queryset returning {filtered_qs.count()} day items")
            return filtered_qs
        
        logger.warning("ItineraryDayItemAdmin.get_queryset no effective partner, returning empty queryset")
        return qs.none()

    def save_model(self, request, obj, form, change):
        """Auto-assign partner based on user"""
        logger.info("ItineraryDayItemAdmin.save_model called")
        logger.debug(f"ItineraryDayItemAdmin.save_model change: {change}, obj pk: {obj.pk if obj else 'None'}")
        
        # Always ensure partner is set if missing
        if not obj.partner:
            effective_partner = get_user_effective_partner(request)
            logger.debug(f"ItineraryDayItemAdmin.save_model setting partner for day item: {effective_partner}")
            if not effective_partner:
                logger.error("ItineraryDayItemAdmin.save_model no effective partner available")
                raise ValueError("No effective partner available for saving itinerary day item")
            obj.partner = effective_partner
        
        super().save_model(request, obj, form, change)
        logger.info(f"ItineraryDayItemAdmin.save_model completed, obj pk: {obj.pk}")

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        """Filter itineraries by user's effective partner"""
        logger.debug(f"ItineraryDayItemAdmin.formfield_for_foreignkey called for field: {db_field.name}")
        
        if db_field.name == "itinerary":
            effective_partner = get_user_effective_partner(request)
            logger.debug(f"ItineraryDayItemAdmin.formfield_for_foreignkey effective_partner: {effective_partner}")
            
            if effective_partner:
                filtered_itineraries = Itinerary.objects.filter(
                    partner=effective_partner,
                    package__type__in=PackageTypeChoices.custom_package_choices()
                )
                logger.debug(f"ItineraryDayItemAdmin.formfield_for_foreignkey filtered to {filtered_itineraries.count()} itineraries")
                kwargs["queryset"] = filtered_itineraries
            else:
                logger.warning("ItineraryDayItemAdmin.formfield_for_foreignkey no effective partner, using empty queryset")
                kwargs["queryset"] = Itinerary.objects.none()
        elif db_field.name == "hotel":
            # Show all ItineraryHotel objects with active hotels
            from packages.models import ItineraryHotel
            kwargs["queryset"] = ItineraryHotel.objects.filter(hotel__is_active=True).select_related('hotel')
        elif db_field.name == "activity":
            # Show all ItineraryActivity objects with active activities
            from packages.models import ItineraryActivity
            kwargs["queryset"] = ItineraryActivity.objects.filter(activity__is_active=True).select_related('activity')
        
        return super().formfield_for_foreignkey(db_field, request, **kwargs)

    def has_module_permission(self, request):
        """Only allow users who can manage packages"""
        return user_can_manage_packages(request)

    def has_view_permission(self, request, obj=None):
        """Only allow users who can manage packages"""
        return user_can_manage_packages(request)

    def has_add_permission(self, request):
        """Only allow users who can manage packages"""
        return user_can_manage_packages(request)

    def has_change_permission(self, request, obj=None):
        """Only allow users who can manage packages"""
        return user_can_manage_packages(request)

    def has_delete_permission(self, request, obj=None):
        """Allow deletion for itinerary day items"""
        return user_can_manage_packages(request)
