{% extends "admin/change_form.html" %}

{% block object-tools-items %}
    {{ block.super }}
    
    {% if original %}
    <!-- Add custom Reframe Highlights With AI button only for existing activities -->
    <li>
        <a href="#" class="btn btn-primary hey-custom-button" 
           onclick="document.getElementById('reframe-highlights-ai-form').submit(); return false;">
            Reframe Highlights With AI
        </a>
    </li>
    {% endif %}
{% endblock %}

{% block extrahead %}
{{ block.super }}
<style>
    /* Force proper spacing and sizing for the Reframe Highlights With AI button */
    .object-tools ul {
        display: flex !important;
        gap: 12px !important;
        align-items: center !important;
    }
    
    .object-tools li {
        margin: 0 !important;
        margin-bottom: 12px !important; /* Add bottom spacing between buttons */
        list-style: none !important;
    }
    
    /* Style the Reframe Highlights With AI button to match other admin buttons exactly */
    .hey-custom-button {
        background-color: #007bff !important;
        border: 1px solid #007bff !important;
        color: #fff !important;
        padding: 8px 16px !important;
        font-size: 14px !important; /* Increased from 13px to 14px to match Close button */
        font-weight: 500 !important; /* Increased font weight for better visibility */
        line-height: 1.5 !important;
        border-radius: 0.25rem !important;
        text-decoration: none !important;
        display: inline-block !important;
        text-align: center !important;
        vertical-align: middle !important;
        cursor: pointer !important;
        white-space: nowrap !important;
        user-select: none !important;
        min-height: 36px !important;
        box-sizing: border-box !important;
        transition: all 0.15s ease-in-out !important;
        width: 100% !important; /* Make button full width like others */
    }
    
    .hey-custom-button:hover,
    .hey-custom-button:focus {
        background-color: #0056b3 !important;
        border-color: #0056b3 !important;
        color: #fff !important;
        text-decoration: none !important;
        outline: 0 !important;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
    }
    
    .hey-custom-button:active {
        background-color: #004085 !important;
        border-color: #004085 !important;
        transform: translateY(1px) !important;
    }
    
    /* Ensure the button matches the height and style of other buttons */
    .object-tools a.btn {
        min-height: 36px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        margin-bottom: 12px !important; /* Add spacing between all buttons */
    }
    
    /* Override any Jazzmin theme conflicts */
    .btn.btn-primary.hey-custom-button {
        background: #007bff !important;
        border-color: #007bff !important;
        color: white !important;
        font-size: 14px !important; /* Ensure font size override */
        font-weight: 500 !important; /* Ensure font weight override */
    }
    
    /* Add spacing between all object-tools buttons */
    .object-tools {
        margin-bottom: 0 !important;
    }
    
    .object-tools li:not(:last-child) {
        margin-bottom: 12px !important;
    }
    
    /* Responsive adjustments */
    @media (max-width: 768px) {
        .object-tools ul {
            flex-direction: column !important;
            gap: 6px !important;
        }
        
        .hey-custom-button {
            width: 100% !important;
        }
    }
</style>
{% endblock %}

{% block content %}
    {{ block.super }}
    
    {% if original %}
    <!-- Hidden form for reframe highlights AI action -->
    <form id="reframe-highlights-ai-form" method="post" style="display: none;">
        {% csrf_token %}
        <input type="hidden" name="_reframe_highlights_ai" value="1">
    </form>
    {% endif %}
{% endblock %} 