{% autoescape off %}
<!DOCTYPE html>
<html>
<head>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .content { background: #f9f9f9; padding: 20px; border-radius: 5px; }
        .button { 
            display: inline-block; 
            padding: 12px 24px; 
            background: #0d6efd !important; 
            color: #ffffff !important; 
            text-decoration: none; 
            border-radius: 5px; 
            font-weight: bold;
            border: 2px solid #0d6efd;
        }
        .button:hover { 
            background: #0b5ed7 !important; 
            border-color: #0b5ed7; 
            color: #ffffff !important;
        }
        .footer { margin-top: 30px; text-align: center; font-size: 0.9em; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>Password Reset Request</h2>
        </div>
        <div class="content">
            <p>Hello,</p>
            <p>You're receiving this email because you requested a password reset for your Zuumm Admin account.</p>
            <p>Please click the button below to reset your password:</p>
            <p style="text-align: center;">
                <a href="{{ protocol }}://{{ domain }}{% url 'password_reset_confirm' uidb64=uid token=token %}" class="button">Reset Password</a>
            </p>
            <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
            <p style="word-break: break-all;">{{ protocol }}://{{ domain }}{% url 'password_reset_confirm' uidb64=uid token=token %}</p>
            <p>Your username: {{ user.get_username }}</p>
            <p>If you didn't request this password reset, you can safely ignore this email.</p>
        </div>
        <div class="footer">
            <p>Thanks for using Zuumm Admin!</p>
            <p>The Zuumm Team</p>
        </div>
    </div>
</body>
</html>
{% endautoescape %} 