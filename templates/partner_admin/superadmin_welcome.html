<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="color-scheme" content="light">
    <meta name="supported-color-schemes" content="light">
    <title>Welcome to Zuumm</title>
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            color-scheme: light;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background-color: #f8f9fa !important;
            line-height: 1.6;
            color: #333333 !important;
            forced-color-adjust: none;
        }

        .email-container {
            max-width: 655px;
            margin: 0 auto;
            background-color: #ffffff !important;
        }

        /* Force light theme for all elements but preserve specific colors and backgrounds */
        p, span, h1, h2, h3, h4, h5, h6 {
            background-color: transparent !important;
        }
        
        /* Specific divs that need transparent backgrounds */
        div:not(.login-container):not(.credential-value) {
            background-color: transparent !important;
        }

        /* Restore specific colors */
        .upload-text {
            font-size: 16px;
            color: #1E3A8A !important;
            font-weight: 500;
        }

        .demo-text {
            font-size: 16px;
            color: #1E3A8A !important;
            font-weight: 500;
        }

        .demo-link {
            color: #FF3951 !important;
            text-decoration: none;
            font-weight: 500;
        }

        .header {
            text-align: center;
            padding: 20px 0 0; /* Remove horizontal padding to make image edge-to-edge */
            background-color: #ffffff;
        }

        .logo img {
            max-width: 130px;
            height: auto;
            margin-bottom: 15px;
        }

        .header-image {
            margin: 0;
            text-align: center;
            width: 100%;
        }

        .header-image img {
            width: 100%;
            max-width: 100%; /* Use full width of container */
            height: auto;
            display: block;
            margin: 0;
        }

        .content {
            background-color: #ffffff;
        }

        .login-container {
            background-color: #FFF9F9 !important;
            padding: 32px 80px;
        }

        .login-section {
            display: flex;
            gap: 20px;
        }

        .resort-image {
            width: 200px;
            flex-shrink: 0;
        }

        .resort-image img {
            width: 100%;
            height: 95%;
            object-fit: cover;
            border-radius: 8px;
        }

        .login-details {
            padding-left: 15px;
        }

        .login-title {
            font-size: 14px;
            color: #333333 !important;
            font-weight: 500;
        }

        .login-link {
            font-size: 14px;
            color: #0083E7 !important;
            text-decoration: none;
            font-weight: 500;
        }

        .credentials {
            margin-top: 16px;
        }

        .credentials-title {
            font-size: 14px;
            color: #333333 !important;
            margin-bottom: 12px;
        }

        .credential-item {
            margin-bottom: 12px;
        }

        .credential-label {
            font-size: 9px;
            color: #333333 !important;
            margin-bottom: 4px;
        }

        .credential-value {
            display: flex;
            align-items: center;
            justify-content: space-between;
            background-color: #ffffff;
            border: 1px solid #eeeeee;
            border-radius: 4px;
            width: 278px;
            padding: 8px 14px;
        }

        .credential-text {
            font-size: 14px;
            color: #333333 !important;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            flex-grow: 1;
            /* Allow the text to take up available space */
        }

        .credential-value {
            position: relative;
        }

        .upload-section {
            text-align: center;
            margin-top: 24px;
            margin-bottom: 24px;
        }

        .upload-text {
            font-size: 16px;
            color: #1E3A8A;
            font-weight: 500;
        }

        .demo-text {
            font-size: 16px;
            color: #1E3A8A;
            font-weight: 500;
        }

        .demo-link {
            color: #FF3951;
            text-decoration: none;
            font-weight: 500;
        }

        .footer {
            text-align: center;
            padding: 0 20px 20px;
        }

        .team-signature {
            font-size: 16px;
            color: #333333 !important;
            margin-bottom: 32px;
            /* display: flex;
            align-items: center;
            justify-content: center; */
        }

        .footer-logo {
            height: 22px;
            margin: 0 5px;
            vertical-align: middle;
        }

        .copyright {
            font-size: 12px;
            color: #706E75 !important;
            line-height: 1.4;
            border-top: 1px solid #eee;
            padding-top: 12px;
        }

        @media (max-width: 480px) {
            .login-container {
                padding: 20px 15px;
            }

            .login-section {
                flex-direction: column;
            }

            .resort-image {
                display: none; /* Hide resort image on mobile */
            }

            .credential-value {
                width: 100%;
            }

            .logo img {
                max-width: 150px;
            }
        }
    </style>
</head>

<body>
    <div class="email-container">
        <!-- Header Section -->
        <div class="header">
            <div class="logo">
                <img src="https://prod-zuumm-public.s3.ap-south-1.amazonaws.com/html_template/zuumm_logo.png" alt="ZUUMM Logo" />
            </div>
            <!-- Using the new header image that includes text -->
            <div class="header-image">
                <img src="https://prod-zuumm-public.s3.ap-south-1.amazonaws.com/html_template/superadmin_welcome_header.png" alt="Welcome to Zuumm" style="width: 100%; max-width: 655px; height: auto;" />
            </div>
        </div>
        <!-- Main Content -->
        <div class="content">
            <div class="login-container">
                <div class="login-section">
                    <div class="resort-image">
                        <img src="https://prod-zuumm-public.s3.ap-south-1.amazonaws.com/html_template/email_image.png" alt="Resort Pool" />
                    </div>
                    <div class="login-details">
                        <div class="login-title">
                            Login to your Portal:
                            <a href="{{ admin_url }}" class="login-link">{{ admin_url_display }}</a>
                        </div>
                        <div class="credentials">
                            <div class="credentials-title">Login Credentials</div>
                            <div class="credential-item">
                                <div class="credential-label">Email</div>
                                <div class="credential-value">
                                    <span class="credential-text">{{ admin_email }}</span>
                                </div>
                            </div>
                            <div class="credential-item">
                                <div class="credential-label">Temporary Password</div>
                                <div class="credential-value">
                                    <span class="credential-text">{{ admin_password }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Upload Section -->
            <div class="upload-section">
                <div class="upload-text">Upload your first packages</div>
                <div class="demo-text">
                    or try our <a href="#" class="demo-link">AI demo</a>
                </div>
            </div>
        </div>
        <!-- Footer -->
        <div class="footer">
            <div class="team-signature">
                — The
                <img src="https://prod-zuumm-public.s3.ap-south-1.amazonaws.com/html_template/zuumm_logo.png" alt="ZUUMM Logo" class="footer-logo">
                Team
            </div>
            <div class="copyright">
                © 2025 Zuumm All Rights Reserved.<br>
            </div>
        </div>
    </div>


</body>

</html>