"""
Utility functions and classes for the bookings app
"""
import os
import tempfile
from io import BytesIO
from django.template.loader import render_to_string
from django.core.files.base import ContentFile
from django.conf import settings
from weasyprint import HTML, CSS
from weasyprint.text.fonts import FontConfiguration
import logging

logger = logging.getLogger(__name__)


class VoucherPDFGenerator:
    """
    Helper class to generate PDF vouchers from HTML templates
    Similar to package PDF generation structure
    """
    
    def __init__(self, voucher):
        """
        Initialize with voucher instance
        
        Args:
            voucher: Voucher model instance
        """
        self.voucher = voucher
        self.template_name = 'vouchers/voucher_pdf.html'
        self.css_file = 'vouchers/voucher_pdf.css'
        
    def get_context_data(self):
        """
        Get context data for PDF template
        
        Returns:
            dict: Context data for template rendering
        """
        logger.debug(f"[PDFGenerator] Getting context data for voucher {self.voucher.booking_id}")
        
        # Get itinerary data
        itinerary_days = self.voucher.daywise_itinerary.all().order_by('day_number')
        logger.debug(f"[PDFGenerator] Found {itinerary_days.count()} itinerary days")
        
        # Format dates for display
        formatted_start_date = self.voucher.package_start_date.strftime('%d %B %Y')
        formatted_end_date = self.voucher.package_end_date.strftime('%d %B %Y')
        formatted_booking_date = self.voucher.booking_date.strftime('%d %B %Y at %I:%M %p')
        
        # Calculate duration
        duration = (self.voucher.package_end_date - self.voucher.package_start_date).days + 1
        logger.debug(f"[PDFGenerator] Calculated duration: {duration} days")
        
        # Get logo URL - always use local file path for PDF generation
        # since PDF generation happens on the server where static files are available locally
        from django.contrib.staticfiles import finders
        from django.conf import settings
        
        logo_path = finders.find('images/admin_logo.png')
        if logo_path:
            logo_url = f"file://{logo_path}"
        else:
            # Fallback to settings.STATIC_URL if local file not found
            logo_url = f"{settings.STATIC_URL}images/admin_logo.png"
        
        context = {
            'voucher': self.voucher,
            'partner': self.voucher.partner,
            'itinerary_days': itinerary_days,
            'formatted_start_date': formatted_start_date,
            'formatted_end_date': formatted_end_date,
            'formatted_booking_date': formatted_booking_date,
            'duration': duration,
            'has_itinerary': itinerary_days.exists(),
            'payment_status': 'Fully Paid' if self.voucher.is_fully_paid else 'Partially Paid',
            'remaining_amount': self.voucher.remaining_amount,
            'inclusions_list': self.voucher.inclusions if self.voucher.inclusions else [],
            'special_notes_list': self.voucher.special_notes if self.voucher.special_notes else [],
            'logo_url': logo_url,
        }
        
        logger.debug(f"[PDFGenerator] Context data prepared for voucher {self.voucher.booking_id}")
        return context
    
    def generate_html(self):
        """
        Generate HTML content for PDF
        
        Returns:
            str: Rendered HTML content
        """
        logger.debug(f"[PDFGenerator] Starting HTML generation for voucher {self.voucher.booking_id}")
        try:
            context = self.get_context_data()
            html_content = render_to_string(self.template_name, context)
            logger.debug(f"[PDFGenerator] HTML content generated for voucher {self.voucher.booking_id}")
            return html_content
        except Exception as e:
            logger.error(f"Error generating HTML for voucher {self.voucher.booking_id}: {str(e)}")
            raise
    
    def generate_pdf(self):
        """
        Generate PDF from HTML template
        
        Returns:
            ContentFile: PDF file as Django ContentFile
        """
        logger.debug(f"[PDFGenerator] Starting PDF generation for voucher {self.voucher.booking_id}")
        try:
            # Generate HTML content
            html_content = self.generate_html()
            logger.debug(f"[PDFGenerator] HTML content generated for PDF generation for voucher {self.voucher.booking_id}")
            
            # Create font configuration
            font_config = FontConfiguration()
            logger.debug(f"[PDFGenerator] Font configuration created for voucher {self.voucher.booking_id}")
            
            # Define CSS styles with orange theme
            css_content = """
            @page {
                size: A4;
                margin: 1cm;
                @bottom-center { content: ""; }
                @bottom-left { content: ""; }
                @bottom-right { content: ""; }
            }
            body {
                font-family: 'Arial', sans-serif;
                font-size: 12px;
                line-height: 1.4;
                color: #333;
            }
            .header {
                text-align: center;
                border-bottom: 2px solid #FF6B35;
                padding-bottom: 20px;
                margin-bottom: 20px;
            }
            .logo {
                margin-bottom: 15px;
            }
            .logo img {
                max-height: 60px;
                max-width: 200px;
            }
            .voucher-title {
                font-size: 18px;
                font-weight: bold;
                color: #333;
            }
            .booking-info {
                background-color: #f8f9fa;
                padding: 15px;
                border-radius: 5px;
                margin: 20px 0;
            }
            .section {
                margin: 20px 0;
            }
            .section-title {
                font-size: 14px;
                font-weight: bold;
                color: #FF6B35;
                border-bottom: 1px solid #ddd;
                padding-bottom: 5px;
                margin-bottom: 10px;
            }
            .info-row {
                display: flex;
                justify-content: space-between;
                margin: 5px 0;
                padding: 5px 0;
                border-bottom: 1px dotted #ddd;
            }
            .info-label {
                font-weight: bold;
                width: 40%;
            }
            .info-value {
                width: 60%;
            }
            .amount-section {
                background-color: #FFF4F0;
                padding: 15px;
                border-radius: 5px;
                border-left: 4px solid #FF6B35;
            }
            .total-amount {
                font-size: 16px;
                font-weight: bold;
                color: #FF6B35;
            }
            .itinerary-day {
                background-color: #f8f9fa;
                padding: 10px;
                margin: 10px 0;
                border-radius: 5px;
                border-left: 3px solid #FF6B35;
            }
            .day-header {
                font-weight: bold;
                color: #FF6B35;
                margin-bottom: 5px;
            }
            .inclusion-item, .note-item {
                margin: 5px 0;
                padding-left: 15px;
                position: relative;
            }
            .inclusion-item:before, .note-item:before {
                content: "•";
                color: #FF6B35;
                position: absolute;
                left: 0;
            }
            .footer {
                text-align: center;
                margin-top: 30px;
                padding-top: 20px;
                border-top: 1px solid #ddd;
                font-size: 10px;
                color: #666;
            }
            .status-paid {
                color: #28a745;
                font-weight: bold;
            }
            .status-pending {
                color: #dc3545;
                font-weight: bold;
            }
            """
            
            # Create CSS object
            css = CSS(string=css_content, font_config=font_config)
            logger.debug(f"[PDFGenerator] CSS styles created for voucher {self.voucher.booking_id}")
            
            # Generate PDF
            html_doc = HTML(string=html_content)
            pdf_bytes = html_doc.write_pdf(stylesheets=[css])
            logger.debug(f"[PDFGenerator] PDF generated for voucher {self.voucher.booking_id}")
            
            # Create ContentFile
            filename = f"voucher_{self.voucher.booking_id}.pdf"
            pdf_file = ContentFile(pdf_bytes, name=filename)
            logger.debug(f"[PDFGenerator] ContentFile created with filename: {filename}")
            
            return pdf_file
            
        except Exception as e:
            logger.error(f"Error generating PDF for voucher {self.voucher.booking_id}: {str(e)}")
            raise
    
    def save_pdf_to_model(self):
        """
        Generate PDF and save it to the voucher model
        
        Returns:
            bool: True if successful, False otherwise
        """
        logger.debug(f"[PDFGenerator] Starting save_pdf_to_model for voucher {self.voucher.booking_id}")
        try:
            pdf_file = self.generate_pdf()
            logger.debug(f"[PDFGenerator] PDF generated and ready for saving for voucher {self.voucher.booking_id}")
            
            # Save PDF to voucher model
            self.voucher.voucher_pdf.save(
                pdf_file.name,
                pdf_file,
                save=True
            )
            logger.info(f"PDF generated and saved for voucher {self.voucher.booking_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving PDF for voucher {self.voucher.booking_id}: {str(e)}")
            return False


class VoucherEmailService:
    """
    Service class to handle voucher email functionality
    Similar to OTP email service structure
    """
    
    @staticmethod
    def send_voucher_email(voucher):
        """
        Send voucher email with PDF attachment
        
        Args:
            voucher: Voucher instance
            
        Returns:
            bool: True if email sent successfully, False otherwise
        """
        logger.debug(f"[EmailService] Starting send_voucher_email for voucher {voucher.booking_id}")
        try:
            from base.email_utils import send_mail_task, EmailContentManager
            
            if not voucher.user_email:
                logger.warning(f"No email address for voucher {voucher.booking_id}")
                return False
                
            logger.debug(f"[EmailService] Preparing email for voucher {voucher.booking_id}")
            
            # Prepare email context
            context = {
                'voucher': voucher,
                'customer_name': voucher.user_name,
                'booking_id': voucher.booking_id,
                'package_name': voucher.package_name,
                'total_amount': voucher.total_amount,
                'travel_dates': f"{voucher.package_start_date.strftime('%d %B %Y')} to {voucher.package_end_date.strftime('%d %B %Y')}",
                'partner_name': voucher.partner.entity_name,
                'booking_date': voucher.booking_date.strftime('%d %B %Y'),
            }
            logger.debug(f"[EmailService] Email context prepared for voucher {voucher.booking_id}")
            
            # Get email content
            subject, body = EmailContentManager.get_email_content('voucher_confirmation', context)
            logger.debug(f"[EmailService] Email content (subject, body) prepared for voucher {voucher.booking_id}")
            
            # Prepare attachments
            attachments = []
            if voucher.voucher_pdf:
                try:
                    logger.debug(f"[EmailService] Preparing PDF attachment for voucher {voucher.booking_id}")
                    # For S3 storage, we need to download the file temporarily
                    temp_file = VoucherEmailService._download_pdf_to_temp(voucher.voucher_pdf)
                    if temp_file:
                        logger.debug(f"[EmailService] PDF attachment: Downloaded to temp - {temp_file}")
                        attachments.append(temp_file)
                    else:
                        logger.warning(f"[EmailService] Could not prepare PDF attachment for voucher {voucher.booking_id}: Unable to download file.")
                except Exception as e:
                    logger.warning(f"[EmailService] Could not prepare PDF attachment for voucher {voucher.booking_id}: {str(e)}")
            
            # Send email
            logger.debug(f"[EmailService] Calling send_mail_task for voucher {voucher.booking_id} with {len(attachments)} attachments")
            success = send_mail_task(
                subject=subject,
                body=body,
                recipient_list=[voucher.user_email],
                attachments=attachments
            )
            
            if success:
                logger.info(f"Voucher email sent successfully for booking {voucher.booking_id}")
            else:
                logger.error(f"Failed to send voucher email for booking {voucher.booking_id}")
                
            return success
            
        except Exception as e:
            logger.error(f"Error sending voucher email for booking {voucher.booking_id}: {str(e)}")
            return False
    
    @staticmethod
    def _download_pdf_to_temp(pdf_file):
        """
        Download PDF from S3 to temporary file for email attachment
        
        Args:
            pdf_file: FileField instance
            
        Returns:
            str: Path to temporary file or None if failed
        """
        logger.debug(f"[EmailService] Starting _download_pdf_to_temp for voucher {pdf_file.name}")
        try:
            import tempfile
            import requests
            
            # Create temporary file
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
            
            # For S3 storage, download from URL
            if hasattr(pdf_file, 'url'):
                logger.debug(f"[EmailService] Downloading PDF from URL: {pdf_file.url}")
                response = requests.get(pdf_file.url, timeout=30)
                response.raise_for_status()
                temp_file.write(response.content)
                logger.debug(f"[EmailService] PDF downloaded successfully - size: {len(response.content)} bytes")
            else:
                # For local storage, read file directly
                logger.debug(f"[EmailService] Reading PDF from local storage")
                pdf_file.open('rb')
                content = pdf_file.read()
                temp_file.write(content)
                pdf_file.close()
                logger.debug(f"[EmailService] PDF read successfully - size: {len(content)} bytes")
            
            temp_file.close()
            logger.debug(f"[EmailService] PDF downloaded to temp file: {temp_file.name}")
            return temp_file.name
            
        except Exception as e:
            logger.error(f"Error downloading PDF to temp file: {str(e)}")
            if 'temp_file' in locals():
                try:
                    temp_file.close()
                except:
                    pass
            return None 