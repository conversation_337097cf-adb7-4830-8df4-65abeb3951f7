import requests
from django.conf import settings


class TripjackBooking<PERSON>elper:
    """
    A helper class to encapsulate all API interactions with the Tripjack service.
    This reflects the official API documentation for flights and hotels.
    """

    def __init__(self):
        self.api_key = settings.TRIPJACK_API_KEY
        self.base_url = settings.TRIPJACK_API_URL
        self.headers = {
            'Content-Type': 'application/json',
            'apikey': self.api_key
        }

    def _make_request(self, method, endpoint, payload=None):
        """
        A private helper to make requests to the Tripjack API with robust error handling.
        """
        url = f"{self.base_url}/{endpoint}"
        try:
            response = requests.request(method, url, json=payload, headers=self.headers, timeout=45)
            response.raise_for_status()  # Raises an HTTPError for bad responses (4xx or 5xx)
            return response.json()
        except requests.exceptions.HTTPError as http_err:
            error_details = http_err.response.text if http_err.response else "No response body"
            raise  # Re-raise to be handled by the service layer
        except requests.exceptions.RequestException as req_err:
            raise

    # --- Flight APIs ---

    def review_flight(self, price_id: str):
        """ Calls Tripjack's Flight Review API: POST /fms/v1/review """
        endpoint = "fms/v1/air/review"
        payload = {"priceIds": [price_id]}
        return self._make_request('POST', endpoint, payload)

    def book_flight(self, flight_payload: dict):
        """ Calls Tripjack's Flight Booking API: POST /oms/v1/air/book """
        endpoint = "oms/v1/air/book"
        return self._make_request('POST', endpoint, flight_payload)

    # --- Hotel APIs ---

    def review_hotel(self, search_id: str, hotel_id: str, option_id: str):
        """ Calls Tripjack's Hotel Revalidate API: POST /hms/v1/revalidate """
        endpoint = "hms/v1/revalidate"
        payload = {
            "searchId": search_id,
            "hotelId": hotel_id,
            "optionId": option_id
        }
        return self._make_request('POST', endpoint, payload)

    def book_hotel(self, hotel_payload: dict):
        """ Calls Tripjack's Hotel Booking API: POST /hms/v1/book """
        endpoint = "hms/v1/book"
        return self._make_request('POST', endpoint, hotel_payload)
