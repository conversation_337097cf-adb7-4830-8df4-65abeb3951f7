from django.contrib import admin
from django.utils.html import format_html
from django.contrib import messages
from django_better_admin_arrayfield.admin.mixins import DynamicArrayMixin
from django.http import HttpResponseRedirect
from django.urls import reverse
import logging

from .models import Voucher, DaywiseItinerary
from .forms import VoucherAdminForm, DaywiseItineraryFormSet, DaywiseItineraryAdminForm

logger = logging.getLogger(__name__)


class DaywiseItineraryInline(admin.StackedInline):
    """Inline admin for DaywiseItinerary"""
    model = DaywiseItinerary
    form = DaywiseItineraryAdminForm
    formset = DaywiseItineraryFormSet
    extra = 1
    fields = (
        'day_number', 'date', 'day_title', 'description', 
        'hotel_name', 'room_type', 'check_in_time', 'check_out_time',
        'activities', 'meals_included', 'transportation'
    )
    ordering = ['day_number']
    
    def get_readonly_fields(self, request, obj=None):
        """Make itinerary readonly for existing vouchers"""
        logger.debug(f"[DaywiseItineraryInline] get_readonly_fields called - obj: {obj}, obj.pk: {obj.pk if obj else 'None'}")
        if obj and obj.pk:  # Existing voucher
            logger.debug(f"[DaywiseItineraryInline] Existing voucher {obj.pk} - making fields readonly")
            return self.fields
        logger.debug(f"[DaywiseItineraryInline] New voucher - fields editable")
        return ()
    
    def has_add_permission(self, request, obj=None):
        """Prevent adding new itinerary items to existing vouchers"""
        logger.debug(f"[DaywiseItineraryInline] has_add_permission called - obj: {obj}, obj.pk: {obj.pk if obj else 'None'}")
        if obj and obj.pk:
            logger.debug(f"[DaywiseItineraryInline] Existing voucher {obj.pk} - preventing add permission")
            return False
        result = super().has_add_permission(request, obj)
        logger.debug(f"[DaywiseItineraryInline] New voucher - allowing add permission: {result}")
        return result
    
    def has_delete_permission(self, request, obj=None):
        """Prevent deleting itinerary items from existing vouchers"""
        logger.debug(f"[DaywiseItineraryInline] has_delete_permission called - obj: {obj}, obj.pk: {obj.pk if obj else 'None'}")
        if obj and obj.pk:
            logger.debug(f"[DaywiseItineraryInline] Existing voucher {obj.pk} - preventing delete permission")
            return False
        result = super().has_delete_permission(request, obj)
        logger.debug(f"[DaywiseItineraryInline] New voucher - allowing delete permission: {result}")
        return result


@admin.register(Voucher)
class VoucherAdmin(DynamicArrayMixin, admin.ModelAdmin):
    form = VoucherAdminForm
    inlines = [DaywiseItineraryInline]
    
    # Add custom template for change form to include the Send Voucher Email button
    change_form_template = 'admin/bookings/voucher_change_form.html'
    
    list_display = (
        'booking_id', 'user_name', 'user_email', 'package_name', 
        'total_amount_display', 'amount_paid_display', 'created_at'
    )
    
    list_filter = (
        'package_type', 'created_at',
    )
    
    search_fields = (
        'booking_id', 'user_name', 'user_email', 'package_name',
        'package__title'
    )
    
    readonly_fields = (
        'external_id', 'created_at', 'updated_at', 'voucher_pdf_link',
        'total_amount_display', 'amount_paid_display'
    )
    
    # Add actions for bulk operations
    actions = ['send_voucher_email_action']
    
    def voucher_pdf_link(self, obj):
        """Display voucher PDF as a downloadable link"""
        if obj.voucher_pdf:
            return format_html(
                '<a href="{}" target="_blank" style="color: #2c5aa0; font-weight: bold;">📄 View PDF</a>',
                obj.voucher_pdf.url
            )
        return format_html('<span style="color: #999;">No PDF available</span>')
    
    voucher_pdf_link.short_description = 'Voucher PDF'
    voucher_pdf_link.allow_tags = True
    
    def total_amount_display(self, obj):
        """Display total amount with rupee sign"""
        try:
            if obj.total_amount is not None:
                # Handle SafeString or other object types by converting to string first, then float
                amount_str = str(obj.total_amount)
                amount = float(amount_str)
                return f'₹{amount:.2f}'
            return '₹0.00'
        except (ValueError, TypeError):
            return '₹0.00'
    total_amount_display.short_description = 'Total Amount'
    
    def amount_paid_display(self, obj):
        """Display amount paid with rupee sign"""
        try:
            if obj.amount_paid is not None:
                # Handle SafeString or other object types by converting to string first, then float
                amount_str = str(obj.amount_paid)
                amount = float(amount_str)
                return f'₹{amount:.2f}'
            return '₹0.00'
        except (ValueError, TypeError):
            return '₹0.00'
    amount_paid_display.short_description = 'Amount Paid'
    
    def send_voucher_email_action(self, request, queryset):
        """Custom admin action to send voucher emails"""
        from .utils import VoucherEmailService
        
        sent_count = 0
        failed_count = 0
        for voucher in queryset:
            if voucher.user_email:
                success = VoucherEmailService.send_voucher_email(voucher)
                if success:
                    sent_count += 1
                else:
                    failed_count += 1
            else:
                failed_count += 1
        
        if sent_count > 0:
            messages.success(request, f'Successfully sent {sent_count} voucher emails.')
        if failed_count > 0:
            messages.warning(request, f'Failed to send {failed_count} emails. Check that vouchers have valid email addresses and PDF files.')
    
    send_voucher_email_action.short_description = "Send voucher email to selected vouchers"
    
    def has_change_permission(self, request, obj=None):
        """
        Override to prevent updates to existing vouchers.
        Allow only creation of new vouchers.
        """
        logger.debug(f"[VoucherAdmin] has_change_permission called - obj: {obj}, obj.pk: {obj.pk if obj else 'None'}")
        # If obj is None, it means we're checking for general change permission
        # (like accessing the changelist page) - allow this
        if obj is None:
            result = super().has_change_permission(request, obj)
            logger.debug(f"[VoucherAdmin] General change permission check: {result}")
            return result
        
        # If obj exists, it means we're checking permission for a specific voucher
        # Don't allow changes to existing vouchers
        logger.debug(f"[VoucherAdmin] Specific voucher {obj.pk} change permission: False (preventing edits)")
        return False
    
    def get_readonly_fields(self, request, obj=None):
        """
        Make all fields readonly when viewing existing vouchers
        """
        logger.debug(f"[VoucherAdmin] get_readonly_fields called - obj: {obj}, obj.pk: {obj.pk if obj else 'None'}")
        if obj:  # Editing existing voucher - make everything readonly
            # Get all field names except id, but include our custom readonly fields
            model_fields = [field.name for field in self.model._meta.fields if field.name != 'id']
            readonly_fields = model_fields + ['voucher_pdf_link']
            logger.debug(f"[VoucherAdmin] Existing voucher {obj.pk} - making all fields readonly: {len(readonly_fields)} fields")
            return readonly_fields
        
        # Creating new voucher - use default readonly fields
        logger.debug(f"[VoucherAdmin] New voucher - using default readonly fields: {self.readonly_fields}")
        return self.readonly_fields
    
    def response_change(self, request, obj):
        """Override to handle custom button actions"""
        logger.debug(f"[VoucherAdmin] response_change called - obj: {obj.pk if obj else 'None'}, POST data: {list(request.POST.keys())}")
        if '_send_voucher_email' in request.POST:
            logger.info(f"[VoucherAdmin] Send voucher email button clicked for voucher {obj.pk}")
            from .utils import VoucherEmailService, VoucherPDFGenerator
            from django.http import HttpResponseRedirect
            from django.urls import reverse
            from django.contrib import messages
            
            # Check if voucher has email
            if not obj.user_email:
                logger.warning(f"[VoucherAdmin] No email address for voucher {obj.booking_id}")
                messages.error(request, f'Cannot send email: No email address found for voucher {obj.booking_id}')
            else:
                # Check if PDF exists, if not create it first
                if not obj.voucher_pdf or not obj.voucher_pdf.name:
                    logger.info(f"[VoucherAdmin] PDF not found for voucher {obj.booking_id}, generating...")
                    try:
                        # Generate PDF first
                        pdf_generator = VoucherPDFGenerator(obj)
                        pdf_success = pdf_generator.save_pdf_to_model()
                        
                        if pdf_success:
                            # Refresh the object to get updated PDF field
                            obj.refresh_from_db()
                            logger.info(f"[VoucherAdmin] PDF generated successfully for voucher {obj.booking_id}")
                        else:
                            logger.error(f"[VoucherAdmin] Failed to generate PDF for voucher {obj.booking_id}")
                            messages.error(request, f'❌ Failed to generate PDF for voucher {obj.booking_id}')
                            return HttpResponseRedirect(
                                reverse('admin:bookings_voucher_change', args=[obj.pk])
                            )
                    except Exception as e:
                        logger.error(f"[VoucherAdmin] Error generating PDF for voucher {obj.booking_id}: {str(e)}")
                        messages.error(request, f'❌ Error generating PDF for voucher {obj.booking_id}: {str(e)}')
                        return HttpResponseRedirect(
                            reverse('admin:bookings_voucher_change', args=[obj.pk])
                        )
                
                # Now send the email (PDF should exist now)
                if obj.voucher_pdf and obj.voucher_pdf.name:
                    try:
                        logger.info(f"[VoucherAdmin] Sending voucher email for booking {obj.booking_id}")
                        success = VoucherEmailService.send_voucher_email(obj)
                        if success:
                            logger.info(f"[VoucherAdmin] Email sent successfully to {obj.user_email} for booking {obj.booking_id}")
                            messages.success(request, f'✅ Voucher email sent successfully to {obj.user_email} for booking {obj.booking_id}')
                        else:
                            logger.error(f"[VoucherAdmin] Failed to send email to {obj.user_email} for booking {obj.booking_id}")
                            messages.error(request, f'❌ Failed to send voucher email to {obj.user_email} for booking {obj.booking_id}')
                    except Exception as e:
                        logger.error(f"[VoucherAdmin] Error sending email for voucher {obj.booking_id}: {str(e)}")
                        messages.error(request, f'❌ Error sending email for voucher {obj.booking_id}: {str(e)}')
                else:
                    logger.error(f"[VoucherAdmin] PDF still not available for voucher {obj.booking_id}")
                    messages.error(request, f'❌ PDF still not available for voucher {obj.booking_id} after generation attempt')
            
            # Redirect back to the same page to show the message
            return HttpResponseRedirect(
                reverse('admin:bookings_voucher_change', args=[obj.pk])
            )
        
        return super().response_change(request, obj)
    
    def change_view(self, request, object_id, form_url='', extra_context=None):
        """Override change_view to handle custom actions for existing vouchers"""
        logger.debug(f"[VoucherAdmin] change_view called - object_id: {object_id}, method: {request.method}")
        if request.method == 'POST' and '_send_voucher_email' in request.POST:
            logger.debug(f"[VoucherAdmin] Handling send voucher email POST for object {object_id}")
            # Get the voucher object
            obj = self.get_object(request, object_id)
            if obj:
                return self.response_change(request, obj)
        
        return super().change_view(request, object_id, form_url, extra_context)
    
    def get_fieldsets(self, request, obj=None):
        """Dynamic fieldsets - hide external_id during creation"""
        logger.debug(f"[VoucherAdmin] get_fieldsets called - obj: {obj}, obj.pk: {obj.pk if obj else 'None'}")
        if obj is None:  # Creating new object
            logger.debug(f"[VoucherAdmin] Creating new voucher - using creation fieldsets")
            return [
                (None, {
                    'fields': (
                        'booking_id', 'booking_date',
                        'user', 'user_name', 'user_email',
                        'package', 'package_name', 'package_type', 'number_of_guests',
                        'package_start_date', 'package_end_date',
                        'inclusions',
                        'total_amount', 'amount_paid',
                        'payment_mode', 'payment_date',
                        'special_notes',
                    )
                })
            ]
        else:  # Editing existing object
            logger.debug(f"[VoucherAdmin] Editing existing voucher {obj.pk} - using view fieldsets")
            return [
                (None, {
                    'fields': (
                        'booking_id', 'booking_date',
                        'user', 'user_name', 'user_email',
                        'package', 'package_name', 'package_type', 'number_of_guests',
                        'package_start_date', 'package_end_date',
                        'inclusions',
                        'total_amount_display', 'amount_paid_display',
                        'payment_mode', 'payment_date',
                        'special_notes',
                        'voucher_pdf_link',
                    )
                })
            ]
    
    def get_form(self, request, obj=None, **kwargs):
        """Pass request to form for partner-based filtering"""
        logger.debug(f"[VoucherAdmin] get_form called - obj: {obj}, obj.pk: {obj.pk if obj else 'None'}")
        form = super().get_form(request, obj, **kwargs)
        
        class RequestAwareForm(form):
            def __init__(self, *args, **kwargs):
                kwargs['request'] = request
                logger.debug(f"[VoucherAdmin] RequestAwareForm.__init__ called for {'existing' if obj else 'new'} voucher")
                super().__init__(*args, **kwargs)
        
        return RequestAwareForm
    
    def get_queryset(self, request):
        """Filter vouchers by partner if not superuser"""
        logger.debug(f"[VoucherAdmin] get_queryset called - user: {request.user}, is_superuser: {request.user.is_superuser}")
        qs = super().get_queryset(request)
        if not request.user.is_superuser:
            from packages.admin import get_user_effective_partner
            effective_partner = get_user_effective_partner(request)
            if effective_partner:
                qs = qs.filter(partner=effective_partner)
                logger.debug(f"[VoucherAdmin] Filtered queryset by partner: {effective_partner} - {qs.count()} vouchers")
            else:
                qs = qs.none()
                logger.debug(f"[VoucherAdmin] No effective partner found - empty queryset")
        else:
            logger.debug(f"[VoucherAdmin] Superuser - showing all vouchers: {qs.count()}")
        return qs
    
    def save_model(self, request, obj, form, change):
        """Auto-set partner if not provided"""
        logger.info(f"[VoucherAdmin] save_model called - change: {change}, voucher ID: {obj.pk}, booking_id: {getattr(obj, 'booking_id', 'None')}")
        
        # Auto-set partner if not provided using the existing helper method
        try:
            partner_exists = obj.partner is not None
            logger.debug(f"[VoucherAdmin] Partner exists check: {partner_exists}")
        except:
            partner_exists = False
            logger.debug(f"[VoucherAdmin] Partner exists check failed - assuming False")
        
        if not partner_exists:
            from packages.admin import get_user_effective_partner
            effective_partner = get_user_effective_partner(request)
            if effective_partner:
                obj.partner = effective_partner
                logger.info(f"[VoucherAdmin] Auto-set partner for voucher {obj.booking_id}: {obj.partner}")
            else:
                logger.warning(f"[VoucherAdmin] No effective partner found for user {request.user}")
        else:
            logger.debug(f"[VoucherAdmin] Partner already set: {obj.partner}")
        
        # Save the voucher
        logger.debug(f"[VoucherAdmin] Calling super().save_model() to save voucher")
        super().save_model(request, obj, form, change)
        
        # Ensure voucher has been saved and has a real ID
        if obj.pk:
            logger.info(f"[VoucherAdmin] Voucher saved successfully - ID: {obj.pk}, Booking ID: {obj.booking_id}")
        else:
            logger.error(f"[VoucherAdmin] Voucher save failed - no ID assigned after save")
    
    def save_related(self, request, form, formsets, change):
        """Override to generate PDF after all inline formsets are saved"""
        voucher = form.instance
        logger.info(f"[VoucherAdmin] save_related called - change: {change}, voucher ID: {voucher.pk}, booking_id: {voucher.booking_id}")
        logger.info(f"[VoucherAdmin] Processing {len(formsets)} formsets for voucher {voucher.booking_id}")
        
        # Ensure voucher instance has been saved and has a real ID
        if not voucher.pk:
            logger.error(f"[VoucherAdmin] Voucher instance has no ID during save_related - this should not happen")
            # Force save the voucher if it doesn't have an ID
            voucher.save()
            logger.info(f"[VoucherAdmin] Force-saved voucher, got ID: {voucher.pk}")
        
        # Log formset details before processing
        for i, formset in enumerate(formsets):
            logger.debug(f"[VoucherAdmin] Formset {i+1}/{len(formsets)} - Type: {type(formset).__name__}, Forms count: {len(formset.forms)}")
            
            # Log each form in the formset
            for j, inline_form in enumerate(formset.forms):
                if hasattr(inline_form, 'cleaned_data') and inline_form.cleaned_data:
                    voucher_ref = inline_form.cleaned_data.get('voucher', 'None')
                    logger.debug(f"[VoucherAdmin] Form {j+1} in formset {i+1} - voucher reference: {voucher_ref}")
                    if hasattr(inline_form, 'instance') and inline_form.instance:
                        logger.debug(f"[VoucherAdmin] Form {j+1} instance voucher_id: {getattr(inline_form.instance, 'voucher_id', 'None')}")
        
        try:
            # First save all related objects (including inline formsets)
            logger.info(f"[VoucherAdmin] Calling super().save_related for voucher {voucher.booking_id}")
            super().save_related(request, form, formsets, change)
            logger.info(f"[VoucherAdmin] super().save_related completed successfully for voucher {voucher.booking_id}")
        except Exception as e:
            logger.error(f"[VoucherAdmin] Error in save_related for voucher {voucher.booking_id}: {str(e)}", exc_info=True)
            raise
        
        # For new vouchers, always generate PDF and send email after inlines are processed
        if not change:  # New voucher creation
            logger.info(f"[VoucherAdmin] New voucher {voucher.booking_id} created - starting PDF generation and email sending")
            
            try:
                from .utils import VoucherPDFGenerator, VoucherEmailService
                
                # Generate PDF now that all itinerary items have been created
                logger.info(f"[VoucherAdmin] Generating PDF for new voucher {voucher.booking_id}")
                pdf_generator = VoucherPDFGenerator(voucher)
                pdf_success = pdf_generator.save_pdf_to_model()
                
                if pdf_success:
                    logger.info(f"[VoucherAdmin] PDF generated successfully for voucher {voucher.booking_id}")
                    
                    # Refresh the voucher instance to get the updated PDF field
                    voucher.refresh_from_db()
                    logger.debug(f"[VoucherAdmin] Voucher {voucher.booking_id} refreshed from DB - PDF field: {voucher.voucher_pdf}")
                    
                    # Send email with PDF attachment
                    if voucher.user_email:
                        try:
                            logger.info(f"[VoucherAdmin] Sending voucher email to {voucher.user_email} for booking {voucher.booking_id}")
                            email_success = VoucherEmailService.send_voucher_email(voucher)
                            if email_success:
                                logger.info(f"[VoucherAdmin] Voucher email sent successfully for booking {voucher.booking_id}")
                            else:
                                logger.warning(f"[VoucherAdmin] Failed to send voucher email for booking {voucher.booking_id}")
                        except Exception as e:
                            logger.error(f"[VoucherAdmin] Error sending voucher email for booking {voucher.booking_id}: {str(e)}", exc_info=True)
                    else:
                        logger.warning(f"[VoucherAdmin] No email address found for voucher {voucher.booking_id} - skipping email")
                
                else:
                    logger.error(f"[VoucherAdmin] Failed to generate PDF for voucher {voucher.booking_id}")
            
            except Exception as e:
                logger.error(f"[VoucherAdmin] Error in PDF generation process for voucher {voucher.booking_id}: {str(e)}", exc_info=True)
        else:
            logger.debug(f"[VoucherAdmin] Existing voucher {voucher.booking_id} updated - skipping PDF generation")


# Custom admin site configurations
admin.site.register_model_admin = True
