from django.db import transaction
from bookings.models import (
    Booking, BookingItinerary, BookingItineraryDayItem, FlightBooking, 
    HotelBooking, ActivityBooking, Passenger, BookedFlightSSR, BookingStatus
)
from bookings.helpers.tripjack_booking_helper import TripjackBookingHelper

class BookingException(Exception):
    """Custom exception for booking failures to return clean error messages."""
    def __init__(self, message, failed_item=None):
        super().__init__(message)
        self.failed_item = failed_item

class PackageBookingService:
    """
    Service to handle the creation of a complete package booking.
    Orchestrates DB operations and third-party API calls.
    """
    def __init__(self, user, validated_data: dict):
        self.user = user
        self.validated_data = validated_data
        self.tripjack_helper = TripjackBookingHelper()

    def _create_passengers_and_ssrs(self, flight_booking_obj, traveller_info_list):
        """
        Creates Passenger and BookedFlightSSR records in the DB.
        Returns a dictionary formatted for the Tripjack API payload.
        """
        ssr_payload = {"ssrBaggageInfos": [], "ssrMealInfos": [], "ssrSeatInfos": []}
        
        for passenger_data in traveller_info_list:
            # Pop SSR data so it's not passed to the Passenger model constructor
            baggage = passenger_data.pop('baggage', [])
            meals = passenger_data.pop('meals', [])
            seats = passenger_data.pop('seats', [])

            passenger = Passenger.objects.create(
                flight_booking=flight_booking_obj,
                **passenger_data, # Unpack the rest of the validated data
                have_ssr_details=bool(baggage or meals or seats)
            )

            for ssr_data in baggage:
                BookedFlightSSR.objects.create(passenger=passenger, ssr_type='Baggage', **ssr_data)
                ssr_payload['ssrBaggageInfos'].append(ssr_data)
            for ssr_data in meals:
                BookedFlightSSR.objects.create(passenger=passenger, ssr_type='Meal', **ssr_data)
                ssr_payload['ssrMealInfos'].append(ssr_data)
            for ssr_data in seats:
                BookedFlightSSR.objects.create(passenger=passenger, ssr_type='Seat', **ssr_data)
                ssr_payload['ssrSeatInfos'].append(ssr_data)

        return ssr_payload

    def _process_flight_booking(self, item_data, itinerary_day_obj):
        flight_details = item_data['flight_details']
        
        flight_booking = FlightBooking.objects.create(
            tripjack_booking_id=item_data['booking_id'],
            amount=item_data['amount'],
            status=BookingStatus.PENDING,
            passengers_bifurcation=item_data.get('passengers_bifurcation', {}),
            is_international=item_data.get('is_international', False),
            gst_info=flight_details.get('gst_info'),
            delivery_info=flight_details.get('delivery_info')
        )

        # Create passengers and get the correctly formatted SSR payload for the API
        ssr_api_payload = self._create_passengers_and_ssrs(flight_booking, flight_details['travellerInfo'])

        # Construct the final FLAT payload for Tripjack's book API
        tripjack_payload = {
            "bookingId": flight_booking.tripjack_booking_id,
            "paymentInfos": [{"amount": str(flight_booking.amount)}],
            "travellerInfo": flight_details['travellerInfo'], # Send original passenger data
            "gstInfo": flight_details.get('gst_info'),
            "deliveryInfo": flight_details.get('delivery_info'),
            **ssr_api_payload
        }
        
        try:
            response = self.tripjack_helper.book_flight(tripjack_payload)
            # Assuming a successful call if no exception is raised
            flight_booking.status = BookingStatus.SUCCESS
            # Path to PNR might vary; check an actual successful response
            pnr_details = response.get('order', {}).get('itemInfos', {}).get('AIR', {}).get('travellerInfos', [{}])[0].get('pnrDetails', {})
            flight_booking.tripjack_pnr = next(iter(pnr_details.values()), None) # Safely get the first PNR
            flight_booking.meta_information = {'provider_response': response}
            flight_booking.save()
        except Exception as e:
            flight_booking.status = BookingStatus.FAILED
            flight_booking.meta_information = {'error': str(e)}
            flight_booking.save()
            raise BookingException("Flight booking with the provider failed.", failed_item=item_data)
            
        BookingItineraryDayItem.objects.create(
            booking_itinerary_day=itinerary_day_obj,
            type='Flight',
            flight_booking=flight_booking,
            order=item_data.get('order', 0)
        )

    def _process_hotel_booking(self, item_data, itinerary_day_obj):
        hotel_details = item_data['hotel_details']
        
        hotel_booking = HotelBooking.objects.create(
            tripjack_booking_id=item_data['booking_id'],
            tripjack_hotel_id=hotel_details['tripjack_hotel_id'],
            hotel_name=hotel_details['hotel_name'],
            check_in_date=hotel_details['check_in_date'],
            check_out_date=hotel_details['check_out_date'],
            room_data=hotel_details['rooms'],
            amount=item_data['amount']
        )

        lead_guest = hotel_details['lead_guest']
        tripjack_payload = {
            "bookingId": hotel_booking.tripjack_booking_id,
            "rooms": hotel_details['rooms'],
            "leadGuest": {
                "ti": lead_guest['title'],
                "fN": lead_guest['first_name'],
                "lN": lead_guest['last_name']
            }
        }
        
        try:
            response = self.tripjack_helper.book_hotel(tripjack_payload)
            hotel_booking.status = BookingStatus.SUCCESS
            hotel_booking.confirmation_number = response.get('order', {}).get('confirmationNo')
            hotel_booking.meta_information = {'provider_response': response}
            hotel_booking.save()
        except Exception as e:
            hotel_booking.status = BookingStatus.FAILED
            hotel_booking.meta_information = {'error': str(e)}
            hotel_booking.save()
            raise BookingException("Hotel booking with the provider failed.", failed_item=item_data)
        
        BookingItineraryDayItem.objects.create(
            booking_itinerary_day=itinerary_day_obj,
            type='Hotel',
            hotel_booking=hotel_booking,
            order=item_data.get('order', 0)
        )

    def _process_activity_booking(self, item_data, itinerary_day_obj):
        activity_details = item_data['activity_details']
        activity_booking = ActivityBooking.objects.create(
            activity_name=activity_details.get('activity_name'),
            amount=item_data.get('amount'),
            provider=activity_details.get('external_provider', 'GetYourGuide'),
            provider_booking_id=activity_details.get('external_booking_reference'),
            date=activity_details.get('activity_date'),
            timing=activity_details.get('activity_time'),
            meta_information={'provider_request': activity_details}
        )
        
        BookingItineraryDayItem.objects.create(
            booking_itinerary_day=itinerary_day_obj,
            type='Activity',
            activity_booking=activity_booking,
            order=item_data.get('order', 0)
        )

    @transaction.atomic
    def create_package(self):
        """
        Main service method to create the entire package.
        Wrapped in a transaction to ensure all-or-nothing data integrity.
        """
        booking = Booking.objects.create(
            user=self.user,
            booker_email=self.validated_data['booker_email'],
            booker_phone_number=self.validated_data['booker_phone_number'],
            status=BookingStatus.PENDING
        )
        total_amount = 0

        for day_data in self.validated_data['itinerary']:
            itinerary_day = BookingItinerary.objects.create(
                booking=booking,
                day_number=day_data['day_number'],
                date=day_data['date']
            )
            for item in sorted(day_data['items'], key=lambda x: x.get('order', 0)):
                total_amount += item['amount']
                item_type = item['type']

                if item_type == 'Flight':
                    self._process_flight_booking(item, itinerary_day)
                elif item_type == 'Hotel':
                    self._process_hotel_booking(item, itinerary_day)
                elif item_type == 'Activity':
                    self._process_activity_booking(item, itinerary_day)
        
        booking.total_amount = total_amount
        booking.status = BookingStatus.SUCCESS
        booking.save()
        return booking
