from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated, AllowAny
from accounts.models import User

from .serializers import ReviewRequestSerializer, PackageBookingRequestSerializer
from bookings.helpers.tripjack_booking_helper import TripjackBooking<PERSON>elper
from bookings.api.v1.services import PackageBookingService, BookingException


class ReviewAPIView(APIView):
    """
    API View to review/revalidate and get a temporary booking_id for a flight or hotel.
    Endpoint: POST /api/review
    """
    permission_classes = [AllowAny]

    def post(self, request, *args, **kwargs):
        serializer = ReviewRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        validated_data = serializer.validated_data
        review_type = validated_data['type']
        details = validated_data['details']
        helper = TripjackBookingHelper()
        
        try:
            if review_type == "Flight":
                response = helper.review_flight(price_id=details['priceId'])
                # Parse unified response from the actual Tripjack response structure
                booking_id = response.get('bookingId')
                amount = response.get('totalPriceInfo', {}).get('totalFareDetail', {}).get('fC', {}).get('TF')
                
            elif review_type == "Hotel":
                response = helper.review_hotel(
                    search_id=details['search_id'],
                    hotel_id=details['hotel_id'],
                    option_id=details['option_id']
                )
                booking_id = response.get('bookingId')
                amount = response.get('option', {}).get('totalPrice')
            
            else:
                # This case is already handled by the serializer, but included for safety
                return Response({"error": "Invalid review type"}, status=status.HTTP_400_BAD_REQUEST)

            if not booking_id or amount is None:
                return Response({
                    "error": "Could not retrieve valid booking details from provider.",
                    "provider_response": response
                }, status=status.HTTP_400_BAD_REQUEST)

            return Response({
                "type": review_type,
                "booking_id": booking_id,
                "amount": amount,
                "provider_response": response # Return full response for frontend to use if needed
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": f"An unexpected error occurred during review: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class PackageBookingAPIView(APIView):
    """
    The main API to create the final booking for the entire package itinerary.
    Endpoint: POST /api/packages/book
    """
    permission_classes = [AllowAny]
    
    def post(self, request, *args, **kwargs):
        serializer = PackageBookingRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        user = User.objects.filter(is_active=True).first()
        service = PackageBookingService(user=user, validated_data=serializer.validated_data)
        
        try:
            booking_obj = service.create_package()
            return Response({
                "status": "SUCCESS",
                "message": "Your package has been successfully booked.",
                "booking_reference_id": booking_obj.id # Return the primary key of the master Booking
            }, status=status.HTTP_201_CREATED)
            
        except BookingException as e:
            return Response({
                "status": "FAILED",
                "message": str(e),
                "failed_item": e.failed_item
            }, status=status.HTTP_400_BAD_REQUEST)
            
        except Exception as e:
            return Response({
                "status": "FAILED",
                "message": f"An unexpected server error occurred: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
