from rest_framework import serializers


class FlightReviewDetailsSerializer(serializers.Serializer):
    priceId = serializers.CharField(required=True)


class HotelReviewDetailsSerializer(serializers.Serializer):
    search_id = serializers.CharField(required=True)
    hotel_id = serializers.CharField(required=True)
    option_id = serializers.CharField(required=True)


class ReviewRequestSerializer(serializers.Serializer):
    type = serializers.ChoiceField(choices=["Flight", "Hotel"])
    details = serializers.JSONField()

    def validate(self, data):
        review_type = data.get('type')
        details = data.get('details')
        
        if review_type == "Flight":
            serializer = FlightReviewDetailsSerializer(data=details)
        elif review_type == "Hotel":
            serializer = HotelReviewDetailsSerializer(data=details)
        else:
            raise serializers.ValidationError({"type": "Invalid review type provided."})
            
        serializer.is_valid(raise_exception=True)
        return data


class PassengerSSRSerializer(serializers.Serializer):
    segment_key = serializers.CharField(max_length=100)
    code = serializers.CharField(max_length=50)


class PassengerSerializer(serializers.Serializer):
    title = serializers.CharField(max_length=10)
    first_name = serializers.CharField(max_length=100)
    last_name = serializers.CharField(max_length=100)
    passenger_type = serializers.ChoiceField(choices=["ADULT", "CHILD", "INFANT"])
    dob = serializers.DateField(required=False, allow_null=True)
    passport_nationality = serializers.CharField(required=False, allow_blank=True, max_length=5)
    passport_number = serializers.CharField(required=False, allow_blank=True, max_length=50)
    passport_expiry_date = serializers.DateField(required=False, allow_null=True)
    
    baggage = PassengerSSRSerializer(many=True, required=False)
    meals = PassengerSSRSerializer(many=True, required=False)
    seats = PassengerSSRSerializer(many=True, required=False)
    

class GSTInfoSerializer(serializers.Serializer):
    gst_number = serializers.CharField()
    email = serializers.EmailField()
    registered_name = serializers.CharField()
    mobile = serializers.CharField()
    address = serializers.CharField()


class DeliveryInfoSerializer(serializers.Serializer):
    emails = serializers.ListField(child=serializers.EmailField())
    contacts = serializers.ListField(child=serializers.CharField())


class FlightDetailsSerializer(serializers.Serializer):
    travellerInfo = PassengerSerializer(many=True)
    gst_info = GSTInfoSerializer(required=False)
    delivery_info = DeliveryInfoSerializer(required=True)


class HotelRoomPaxInfoChildSerializer(serializers.Serializer):
    age = serializers.IntegerField(min_value=0, max_value=17)


class HotelRoomPaxInfoSerializer(serializers.Serializer):
    adults = serializers.IntegerField(min_value=1)
    children = HotelRoomPaxInfoChildSerializer(many=True, required=False)


class HotelRoomSerializer(serializers.Serializer):
    room_type_name = serializers.CharField()
    paxInfo = HotelRoomPaxInfoSerializer()


class LeadGuestSerializer(serializers.Serializer):
    title = serializers.CharField()
    first_name = serializers.CharField()
    last_name = serializers.CharField()


class HotelDetailsSerializer(serializers.Serializer):
    tripjack_hotel_id = serializers.CharField()
    hotel_name = serializers.CharField()
    check_in_date = serializers.DateTimeField()
    check_out_date = serializers.DateTimeField()
    rooms = HotelRoomSerializer(many=True)
    lead_guest = LeadGuestSerializer()


class ActivityDetailsSerializer(serializers.Serializer):
    external_provider = serializers.CharField()
    external_booking_reference = serializers.CharField()
    activity_name = serializers.CharField()
    activity_date = serializers.DateField()
    activity_time = serializers.CharField()
    participants = serializers.JSONField() # Simple validation for now
    location = serializers.JSONField()


class ItineraryItemSerializer(serializers.Serializer):
    type = serializers.ChoiceField(choices=["Flight", "Hotel", "Activity"])
    order = serializers.IntegerField(min_value=0, default=0)
    booking_id = serializers.CharField(required=False, allow_blank=True)
    amount = serializers.DecimalField(max_digits=10, decimal_places=2)
    
    flight_details = FlightDetailsSerializer(required=False)
    hotel_details = HotelDetailsSerializer(required=False)
    activity_details = ActivityDetailsSerializer(required=False)

    def validate(self, data):
        item_type = data.get('type')
        if item_type == "Flight" and not data.get('flight_details'):
            raise serializers.ValidationError({"flight_details": "This field is required for Flight items."})
        if item_type == "Hotel" and not data.get('hotel_details'):
            raise serializers.ValidationError({"hotel_details": "This field is required for Hotel items."})
        if item_type == "Activity" and not data.get('activity_details'):
            raise serializers.ValidationError({"activity_details": "This field is required for Activity items."})
        if item_type in ["Flight", "Hotel"] and not data.get('booking_id'):
            raise serializers.ValidationError({"booking_id": "This field is required for Flight and Hotel items."})
        return data


class ItineraryDaySerializer(serializers.Serializer):
    day_number = serializers.IntegerField(min_value=1)
    date = serializers.DateField()
    items = ItineraryItemSerializer(many=True)


class PackageBookingRequestSerializer(serializers.Serializer):
    booker_email = serializers.EmailField()
    booker_phone_number = serializers.CharField()
    itinerary = ItineraryDaySerializer(many=True, allow_empty=False)
