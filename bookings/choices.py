from django.db import models


class PackageTypeChoices(models.TextChoices):
    """Package type choices for vouchers"""
    STANDARD = "Standard", "Standard"
    PREMIUM = "Premium", "Premium"
    DELUXE = "Deluxe", "Deluxe"
    LUXURY = "Luxury", "Luxury"
    BUDGET = "Budget", "Budget"
    CUSTOMIZED = "Customized", "Customized"


class PaymentModeChoices(models.TextChoices):
    """Payment mode choices"""
    CREDIT_CARD = "Credit Card", "Credit Card"
    DEBIT_CARD = "Debit Card", "Debit Card"
    UPI = "UPI", "UPI"
    NET_BANKING = "Net Banking", "Net Banking"
    BANK_TRANSFER = "Bank Transfer", "Bank Transfer"
    CASH = "Cash", "Cash"
    WALLET = "Digital Wallet", "Digital Wallet"
    EMI = "EMI", "EMI"
    OTHERS = "Others", "Others"


class MealTypeChoices(models.TextChoices):
    """Meal inclusion choices for itinerary"""
    BREAKFAST = "Breakfast", "Breakfast"
    LUNCH = "Lunch", "Lunch"
    DINNER = "Dinner", "Dinner"
    BREAKFAST_LUNCH = "Breakfast & Lunch", "Breakfast & Lunch"
    LUNCH_DINNER = "Lunch & Dinner", "Lunch & Dinner"
    BREAKFAST_DINNER = "Breakfast & Dinner", "Breakfast & Dinner"
    ALL_MEALS = "All Meals", "All Meals (Breakfast, Lunch & Dinner)"
    NO_MEALS = "No Meals", "No Meals Included"


class RoomTypeChoices(models.TextChoices):
    """Room type choices for accommodation"""
    STANDARD = "Standard", 
    DELUXE = "Deluxe", 
    SUITE = "Suite",
    PREMIUM = "Premium",
    LUXURY = "Luxury",
    VILLA = "Villa",
    COTTAGE = "Cottage",
    APARTMENT = "Apartment",


class VoucherStatusChoices(models.TextChoices):
    """Voucher/Booking status choices"""
    PENDING = "Pending", "Pending Confirmation"
    CONFIRMED = "Confirmed", "Confirmed"
    CANCELLED = "Cancelled", "Cancelled"
    COMPLETED = "Completed", "Completed"
    REFUNDED = "Refunded", "Refunded"


# Constants
BOOKING_ID_PREFIX = "ZUUMM"
DEFAULT_CURRENCY = "INR"
MAX_GUESTS_PER_BOOKING = 50 


class BookingStatus(models.TextChoices):
    """Booking status choices"""
    PENDING = "Pending", "Pending"
    CONFIRMED = "Confirmed", "Confirmed"
    FAILED = "Failed", "Failed"
    CANCELLED = "Cancelled", "Cancelled"


class BookingItineraryDayItemType(models.TextChoices):
    """Booking itinerary day item type choices"""
    HOTEL = "Hotel", "Hotel"
    ACTIVITY = "Activity", "Activity"
    FLIGHT = "Flight", "Flight"
    TRANSFER = "Transfer", "Transfer"


class PassengerType(models.TextChoices):
    ADULT = 'Adult', 'Adult'
    CHILD = 'Child', 'Child'
    INFANT = 'Infant', 'Infant'


class SSRType(models.TextChoices):
    BAGGAGE = 'Baggage', 'Baggage'
    MEAL = 'Meal', 'Meal'
    SEAT = 'Seat', 'Seat'
