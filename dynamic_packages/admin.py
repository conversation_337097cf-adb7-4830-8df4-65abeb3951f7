from django.contrib import admin
from django.contrib.gis.admin import OSMGeoAdmin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from django.contrib import messages
from django.http import HttpResponseRedirect
from django.db import models
from django_better_admin_arrayfield.admin.mixins import DynamicArrayMixin

from .models import (
    Hotel, Room, Facility, HotelContact, HotelAddress, 
    HotelMedia, RoomMedia, Country, State, City
)


# ===============================================
# INLINE ADMIN CLASSES
# ===============================================

class HotelMediaInline(admin.TabularInline):
    """Inline admin for hotel media"""
    model = HotelMedia
    extra = 1
    fields = ('media', 'media_preview', 'created_at')
    readonly_fields = ('media_preview', 'created_at')
    
    def media_preview(self, obj):
        if obj.media:
            return format_html(
                '<img src="{}" width="100" height="75" style="object-fit: cover;" />',
                obj.media.url
            )
        return "No Image"
    media_preview.short_description = "Preview"


class RoomMediaInline(admin.TabularInline):
    """Inline admin for room media"""
    model = RoomMedia
    extra = 1
    fields = ('media', 'media_preview', 'created_at')
    readonly_fields = ('media_preview', 'created_at')
    
    def media_preview(self, obj):
        if obj.media:
            return format_html(
                '<img src="{}" width="100" height="75" style="object-fit: cover;" />',
                obj.media.url
            )
        return "No Image"
    media_preview.short_description = "Preview"


class RoomInline(admin.TabularInline):
    """Inline admin for rooms"""
    model = Room
    extra = 0
    fields = ('name', 'type', 'price', 'max_adults', 'max_children', 'meal_plan', 'room_link')
    readonly_fields = ('room_link',)
    show_change_link = True
    
    def room_link(self, obj):
        if obj.pk:
            url = reverse('admin:dynamic_packages_room_change', args=[obj.pk])
            return format_html('<a href="{}" target="_blank">Edit Room Details →</a>', url)
        return "Save hotel first"
    room_link.short_description = "Actions"


class FacilityInline(admin.TabularInline):
    """Inline admin for hotel facilities only"""
    model = Facility
    extra = 1
    fields = ('type', 'amenities', 'facility_link')
    readonly_fields = ('facility_link',)
    
    def get_queryset(self, request):
        """Only show hotel facilities, not room facilities"""
        qs = super().get_queryset(request)
        return qs.filter(room__isnull=True)  # Only hotel facilities
    
    def facility_link(self, obj):
        if obj.pk:
            url = reverse('admin:dynamic_packages_facility_change', args=[obj.pk])
            return format_html('<a href="{}" target="_blank">Edit Facility →</a>', url)
        return "Save first"
    facility_link.short_description = "Actions"


class HotelContactInline(admin.StackedInline):
    """Inline admin for hotel contacts"""
    model = HotelContact
    extra = 0
    fields = ('phone', 'email', 'fax', 'website', 'contact_link')
    readonly_fields = ('contact_link',)
    
    def contact_link(self, obj):
        if obj.pk:
            url = reverse('admin:dynamic_packages_hotelcontact_change', args=[obj.pk])
            return format_html('<a href="{}" target="_blank">Edit Contact Details →</a>', url)
        return "Save first"
    contact_link.short_description = "Actions"


# ===============================================
# MAIN ADMIN CLASSES
# ===============================================

@admin.register(Hotel)
class HotelAdmin(admin.ModelAdmin):
    """Admin for Hotel model"""
    list_display = (
        'name', 'destination_link', 'city_name', 'rating', 'base_price', 
        'rooms_count', 'facilities_count', 'media_count', 'is_active', 'created_at'
    )
    list_filter = (
        'destination', 'rating', 'currency', 'is_active', 'created_at',
        'address__country', 'address__state', 'address__city'
    )
    search_fields = (
        'name', 'description', 'tripjack_static_id', 'tripjack_hotel_id',
        'destination__title', 'address__city__name', 'address__state__name'
    )
    raw_id_fields = ('destination', 'address')
    readonly_fields = (
        'external_id', 'created_at', 'updated_at', 'rooms_count', 
        'facilities_count', 'media_count', 'address_link', 'destination_link'
    )
    ordering = ('-created_at',)
    
    # All fields in single form - no fieldsets
    fields = (
        'name', 'description', 'rating', 'is_active',
        'tripjack_static_id', 'tripjack_hotel_id', 'nationality',
        'destination', 'destination_link', 'address', 'address_link', 
        'base_price', 'currency',
        'rooms_count', 'facilities_count', 'media_count',
        'external_id', 'created_at', 'updated_at', 'meta_info'
    )
    
    inlines = [RoomInline, FacilityInline, HotelContactInline, HotelMediaInline]
    
    actions = ['activate_hotels', 'deactivate_hotels', 'bulk_update_currency']
    
    def destination_link(self, obj):
        if obj.destination:
            url = reverse('admin:packages_destination_change', args=[obj.destination.pk])
            return format_html('<a href="{}" target="_blank">{}</a>', url, obj.destination.title)
        return "No destination"
    destination_link.short_description = "Destination"
    
    def address_link(self, obj):
        if obj.address:
            url = reverse('admin:dynamic_packages_hoteladdress_change', args=[obj.address.pk])
            return format_html('<a href="{}" target="_blank">Edit Address →</a>', url)
        return "No address"
    address_link.short_description = "Address"
    
    def rooms_count(self, obj):
        count = obj.rooms.count()
        if count > 0:
            url = reverse('admin:dynamic_packages_room_changelist') + f'?hotel__id__exact={obj.id}'
            return format_html('<a href="{}">{} rooms</a>', url, count)
        return '0 rooms'
    rooms_count.short_description = "Rooms"
    
    def facilities_count(self, obj):
        count = obj.facilities.count()
        if count > 0:
            url = reverse('admin:dynamic_packages_facility_changelist') + f'?hotel__id__exact={obj.id}'
            return format_html('<a href="{}">{} facilities</a>', url, count)
        return '0 facilities'
    facilities_count.short_description = "Facilities"
    
    def media_count(self, obj):
        count = obj.media.count()
        if count > 0:
            return f'{count} images'
        return '0 images'
    media_count.short_description = "Media"
    
    def city_name(self, obj):
        return obj.city_name or 'N/A'
    city_name.short_description = "City"
    
    # Custom actions
    def activate_hotels(self, request, queryset):
        updated = queryset.update(is_active=True)
        self.message_user(request, f'{updated} hotels were successfully activated.', messages.SUCCESS)
    activate_hotels.short_description = "Activate selected hotels"
    
    def deactivate_hotels(self, request, queryset):
        updated = queryset.update(is_active=False)
        self.message_user(request, f'{updated} hotels were successfully deactivated.', messages.SUCCESS)
    deactivate_hotels.short_description = "Deactivate selected hotels"
    
    def bulk_update_currency(self, request, queryset):
        updated = queryset.update(currency='INR')
        self.message_user(request, f'Currency updated to INR for {updated} hotels.', messages.SUCCESS)
    bulk_update_currency.short_description = "Set currency to INR"

    def has_add_permission(self, request):
        return False


@admin.register(Room)
class RoomAdmin(admin.ModelAdmin):
    """Admin for Room model"""
    list_display = (
        'name', 'hotel_link', 'type', 'price', 'max_adults', 'max_children', 
        'meal_plan', 'media_count', 'facilities_count', 'is_active', 'created_at'
    )
    list_filter = (
        'type', 'meal_plan', 'max_adults', 'max_children', 'is_active', 'created_at',
        'hotel__destination', 'hotel__rating'
    )
    search_fields = (
        'name', 'description', 'type', 'meal_plan', 
        'hotel__name', 'hotel__destination__title'
    )
    readonly_fields = (
        'external_id', 'created_at', 'updated_at', 'media_count', 
        'facilities_count', 'hotel_link'
    )
    ordering = ('-created_at',)
    
    # All fields in single form - no fieldsets
    fields = (
        'name', 'type', 'description', 'is_active',
        'hotel', 'hotel_link', 'price', 'meal_plan',
        'max_adults', 'max_children', 'occupancy',
        'media_count', 'facilities_count',
        'external_id', 'created_at', 'updated_at', 'meta_info'
    )
    
    inlines = [RoomMediaInline]
    
    actions = ['activate_rooms', 'deactivate_rooms', 'update_meal_plan']
    
    def hotel_link(self, obj):
        if obj.hotel:
            url = reverse('admin:dynamic_packages_hotel_change', args=[obj.hotel.pk])
            return format_html('<a href="{}" target="_blank">{}</a>', url, obj.hotel.name)
        return "No hotel"
    hotel_link.short_description = "Hotel"
    
    def media_count(self, obj):
        count = obj.media.count()
        if count > 0:
            return f'{count} images'
        return '0 images'
    media_count.short_description = "Media"
    
    def facilities_count(self, obj):
        count = obj.facilities.count()
        if count > 0:
            url = reverse('admin:dynamic_packages_facility_changelist') + f'?room__id__exact={obj.id}'
            return format_html('<a href="{}">{} facilities</a>', url, count)
        return '0 facilities'
    facilities_count.short_description = "Facilities"
    
    # Custom actions
    def activate_rooms(self, request, queryset):
        updated = queryset.update(is_active=True)
        self.message_user(request, f'{updated} rooms were successfully activated.', messages.SUCCESS)
    activate_rooms.short_description = "Activate selected rooms"
    
    def deactivate_rooms(self, request, queryset):
        updated = queryset.update(is_active=False)
        self.message_user(request, f'{updated} rooms were successfully deactivated.', messages.SUCCESS)
    deactivate_rooms.short_description = "Deactivate selected rooms"
    
    def update_meal_plan(self, request, queryset):
        updated = queryset.update(meal_plan='Breakfast Included')
        self.message_user(request, f'Meal plan updated for {updated} rooms.', messages.SUCCESS)
    update_meal_plan.short_description = "Set meal plan to 'Breakfast Included'"

    def has_add_permission(self, request):
        return False


@admin.register(HotelContact)
class HotelContactAdmin(DynamicArrayMixin, admin.ModelAdmin):
    """Admin for HotelContact model"""
    list_display = (
        'hotel_name', 'phone', 'email_summary', 'website', 
        'has_complete_info', 'is_active', 'created_at'
    )
    list_filter = ('is_active', 'created_at', 'hotel__destination', 'hotel__rating')
    search_fields = ('phone', 'email', 'fax', 'website', 'hotel__name')
    readonly_fields = ('external_id', 'created_at', 'updated_at', 'hotel_link')
    ordering = ('-created_at',)
    
    # All fields in single form - no fieldsets
    fields = (
        'phone', 'email', 'fax', 'website', 'is_active',
        'hotel', 'hotel_link',
        'external_id', 'created_at', 'updated_at'
    )
    
    actions = ['activate_contacts', 'deactivate_contacts']
    
    def hotel_name(self, obj):
        """Display hotel name without hyperlink"""
        if obj.hotel:
            return obj.hotel.name
        return "No hotel"
    hotel_name.short_description = "Hotel"
    
    def hotel_link(self, obj):
        if obj.hotel:
            url = reverse('admin:dynamic_packages_hotel_change', args=[obj.hotel.pk])
            return format_html('<a href="{}" target="_blank">{}</a>', url, obj.hotel.name)
        return "No hotel"
    hotel_link.short_description = "Hotel"
    
    def email_summary(self, obj):
        if obj.email:
            if len(obj.email) == 1:
                return obj.email[0]
            elif len(obj.email) > 1:
                return f"{obj.email[0]} (+{len(obj.email)-1} more)"
        return "No email"
    email_summary.short_description = "Email"
    
    def has_complete_info(self, obj):
        complete = bool(obj.phone and obj.email and obj.website)
        if complete:
            return format_html('<span style="color: green;">✓ Complete</span>')
        return format_html('<span style="color: orange;">⚠ Partial</span>')
    has_complete_info.short_description = "Info Status"
    
    # Custom actions
    def activate_contacts(self, request, queryset):
        updated = queryset.update(is_active=True)
        self.message_user(request, f'{updated} contacts were successfully activated.', messages.SUCCESS)
    activate_contacts.short_description = "Activate selected contacts"
    
    def deactivate_contacts(self, request, queryset):
        updated = queryset.update(is_active=False)
        self.message_user(request, f'{updated} contacts were successfully deactivated.', messages.SUCCESS)
    deactivate_contacts.short_description = "Deactivate selected contacts"

    def has_add_permission(self, request):
        return False


@admin.register(Facility)
class FacilityAdmin(DynamicArrayMixin, admin.ModelAdmin):
    """Admin for Facility model"""
    list_display = (
        'facility_summary', 'type', 'hotel_link', 'room_link', 
        'amenities_count', 'is_active', 'created_at'
    )
    list_filter = ('type', 'is_active', 'created_at', 'hotel__destination')
    search_fields = ('amenities', 'hotel__name', 'room__name', 'type')
    readonly_fields = ('external_id', 'created_at', 'updated_at', 'hotel_link', 'room_link')
    ordering = ('-created_at',)
    
    # All fields in single form - no fieldsets
    fields = (
        'type', 'amenities', 'is_active',
        'hotel', 'hotel_link', 'room', 'room_link',
        'external_id', 'created_at', 'updated_at'
    )
    
    actions = ['activate_facilities', 'deactivate_facilities', 'set_hotel_type', 'set_room_type']
    
    def facility_summary(self, obj):
        if obj.amenities:
            amenities_str = ', '.join(obj.amenities[:3])
            if len(obj.amenities) > 3:
                amenities_str += f" (+{len(obj.amenities)-3} more)"
            return amenities_str
        return "No amenities"
    facility_summary.short_description = "Amenities"
    
    def hotel_link(self, obj):
        if obj.hotel:
            url = reverse('admin:dynamic_packages_hotel_change', args=[obj.hotel.pk])
            return format_html('<a href="{}" target="_blank">{}</a>', url, obj.hotel.name)
        return "No hotel"
    hotel_link.short_description = "Hotel"
    
    def room_link(self, obj):
        if obj.room:
            url = reverse('admin:dynamic_packages_room_change', args=[obj.room.pk])
            return format_html('<a href="{}" target="_blank">{}</a>', url, obj.room.name)
        return "No room"
    room_link.short_description = "Room"
    
    def amenities_count(self, obj):
        return len(obj.amenities) if obj.amenities else 0
    amenities_count.short_description = "Count"
    
    # Custom actions
    def activate_facilities(self, request, queryset):
        updated = queryset.update(is_active=True)
        self.message_user(request, f'{updated} facilities were successfully activated.', messages.SUCCESS)
    activate_facilities.short_description = "Activate selected facilities"
    
    def deactivate_facilities(self, request, queryset):
        updated = queryset.update(is_active=False)
        self.message_user(request, f'{updated} facilities were successfully deactivated.', messages.SUCCESS)
    deactivate_facilities.short_description = "Deactivate selected facilities"
    
    def set_hotel_type(self, request, queryset):
        updated = queryset.update(type='Hotel')
        self.message_user(request, f'{updated} facilities set to Hotel type.', messages.SUCCESS)
    set_hotel_type.short_description = "Set type to Hotel"
    
    def set_room_type(self, request, queryset):
        updated = queryset.update(type='Room')
        self.message_user(request, f'{updated} facilities set to Room type.', messages.SUCCESS)
    set_room_type.short_description = "Set type to Room"

    def has_add_permission(self, request):
        return False


@admin.register(HotelAddress)
class HotelAddressAdmin(OSMGeoAdmin):
    """Admin for HotelAddress model with map support"""
    list_display = ('address_summary', 'city', 'state', 'country', 'hotels_count', 'created_at')
    list_filter = ('country', 'state', 'city', 'created_at')
    search_fields = ('address_line', 'postal_code', 'city__name', 'state__name', 'country__name')
    readonly_fields = ('external_id', 'created_at', 'updated_at', 'hotels_count')
    
    # All fields in single form - no fieldsets
    fields = (
        'address_line', 'postal_code', 'city', 'state', 'country',
        'location',
        'is_active',
        'external_id', 'created_at', 'updated_at', 'hotels_count'
    )
    
    # Map settings for GeoDjango
    default_lon = 77.1025  # Delhi longitude
    default_lat = 28.7041  # Delhi latitude
    default_zoom = 5
    
    def address_summary(self, obj):
        parts = []
        if obj.address_line:
            parts.append(obj.address_line[:50])
        if obj.city:
            parts.append(obj.city.name)
        if obj.state:
            parts.append(obj.state.name)
        return ', '.join(parts) or 'No address'
    address_summary.short_description = "Address"
    
    def hotels_count(self, obj):
        count = obj.hotel_set.count()
        if count > 0:
            url = reverse('admin:dynamic_packages_hotel_changelist') + f'?address__id__exact={obj.id}'
            return format_html('<a href="{}">{} hotels</a>', url, count)
        return '0 hotels'
    hotels_count.short_description = "Hotels"

    def has_add_permission(self, request):
        return False


@admin.register(City)
class CityAdmin(admin.ModelAdmin):
    """Admin for City model"""
    list_display = ('name', 'code', 'hotels_count', 'is_active', 'created_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('name', 'code')
    ordering = ('name',)
    readonly_fields = ('external_id', 'created_at', 'updated_at', 'hotels_count')
    
    # All fields in single form - no fieldsets
    fields = (
        'name', 'code', 'is_active',
        'external_id', 'created_at', 'updated_at', 'hotels_count'
    )
    
    def hotels_count(self, obj):
        count = Hotel.objects.filter(address__city=obj).count()
        if count > 0:
            url = reverse('admin:dynamic_packages_hotel_changelist') + f'?address__city__id__exact={obj.id}'
            return format_html('<a href="{}">{} hotels</a>', url, count)
        return '0 hotels'
    hotels_count.short_description = "Hotels"

    def has_add_permission(self, request):
        return False


@admin.register(State)
class StateAdmin(admin.ModelAdmin):
    """Admin for State model"""
    list_display = ('name', 'code', 'hotels_count', 'is_active', 'created_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('name', 'code')
    ordering = ('name',)
    readonly_fields = ('external_id', 'created_at', 'updated_at', 'hotels_count')
    
    # All fields in single form - no fieldsets
    fields = (
        'name', 'code', 'is_active',
        'external_id', 'created_at', 'updated_at', 'hotels_count'
    )
    
    def hotels_count(self, obj):
        count = Hotel.objects.filter(address__state=obj).count()
        if count > 0:
            url = reverse('admin:dynamic_packages_hotel_changelist') + f'?address__state__id__exact={obj.id}'
            return format_html('<a href="{}">{} hotels</a>', url, count)
        return '0 hotels'
    hotels_count.short_description = "Hotels"

    def has_add_permission(self, request):
        return False


@admin.register(Country)
class CountryAdmin(admin.ModelAdmin):
    """Admin for Country model"""
    list_display = ('name', 'code', 'hotels_count', 'is_active', 'created_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('name', 'code')
    ordering = ('name',)
    readonly_fields = ('external_id', 'created_at', 'updated_at', 'hotels_count')
    
    # All fields in single form - no fieldsets
    fields = (
        'name', 'code', 'is_active',
        'external_id', 'created_at', 'updated_at', 'hotels_count'
    )
    
    def hotels_count(self, obj):
        count = Hotel.objects.filter(address__country=obj).count()
        if count > 0:
            url = reverse('admin:dynamic_packages_hotel_changelist') + f'?address__country__id__exact={obj.id}'
            return format_html('<a href="{}">{} hotels</a>', url, count)
        return '0 hotels'
    hotels_count.short_description = "Hotels"

    def has_add_permission(self, request):
        return False


# Add custom CSS for better styling
admin.site.enable_nav_sidebar = True
