from django.db import models
from django.contrib.gis.db import models as locationmodels
from django_better_admin_arrayfield.models.fields import ArrayField

from base.storage_utils import (
    hotel_image_upload_path,
    room_image_upload_path,
)
from base.models import BaseModel

from dynamic_packages.choices import FacilityTypeChoices


class Country(BaseModel):
    code = models.CharField(max_length=10, unique=True)
    name = models.CharField(max_length=100)

    class Meta:
        verbose_name = "Country"
        verbose_name_plural = "Countries"

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        """Override save to ensure title is lowercase"""
        if self.name:
            self.name = self.name.lower().strip()
        super().save(*args, **kwargs)


class State(BaseModel):
    code = models.CharField(max_length=10, unique=True)
    name = models.CharField(max_length=100)

    class Meta:
        verbose_name = "State"
        verbose_name_plural = "States"

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        """Override save to ensure title is lowercase"""
        if self.name:
            self.name = self.name.lower().strip()
        super().save(*args, **kwargs)


class City(BaseModel):
    code = models.CharField(max_length=20, unique=True)
    name = models.CharField(max_length=100)

    class Meta:
        verbose_name = "City"
        verbose_name_plural = "Cities"

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        """Override save to ensure title is lowercase"""
        if self.name:
            self.name = self.name.lower().strip()
        super().save(*args, **kwargs)


class HotelAddress(BaseModel):
    address_line = models.CharField(max_length=255, null=True, blank=True)
    postal_code = models.CharField(max_length=20, blank=True, null=True)
    city = models.ForeignKey(City, on_delete=models.SET_NULL, null=True, blank=True)
    state = models.ForeignKey(State, on_delete=models.SET_NULL, null=True, blank=True)
    country = models.ForeignKey(Country, on_delete=models.SET_NULL, null=True, blank=True)
    location = locationmodels.PointField(srid=4326, null=True, blank=True)

    def __str__(self):
        parts = []
        if self.address_line:
            parts.append(self.address_line[:50])
        if self.city and self.city.name:
            parts.append(self.city.name.title())
        if self.state and self.state.name:
            parts.append(self.state.name.title())
        if self.country and self.country.name:
            parts.append(self.country.name.title())
        
        # Filter out empty parts and join
        filtered_parts = [part for part in parts if part and part.strip()]
        return ', '.join(filtered_parts) if filtered_parts else 'Address'


class Hotel(BaseModel):
    destination = models.ForeignKey('packages.Destination', on_delete=models.CASCADE, related_name="hotels")

    # Tripjack Static Hotel ID (from static hotels API)
    tripjack_static_id = models.CharField(max_length=50)

    # Tripjack Hotel ID (from detail API response - like hsid8112566514-61886608)
    tripjack_hotel_id = models.CharField(max_length=50)

    # Location Details
    address = models.ForeignKey(HotelAddress, on_delete=models.SET_NULL, null=True, blank=True)

    # Hotel Details
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    rating = models.PositiveIntegerField(blank=True, null=True)

    # Base Price (Least price of all rooms associated with the hotel)
    base_price = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)

    # Currency (Currency of the base price)
    currency = models.CharField(max_length=10, default="INR")
    nationality = models.CharField(max_length=10, default="106")

    meta_info = models.JSONField(default=dict, blank=True, null=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            # For main query: destination_id + rating >= min_rating + is_active
            models.Index(
                fields=['destination_id', 'is_active', 'rating'],
                name='hotel_dest_active_rating_idx'
            ),
            # For external API lookups
            models.Index(
                fields=['tripjack_static_id'],
                name='hotel_tripjack_static_idx'
            ),
            models.Index(
                fields=['tripjack_hotel_id'],
                name='hotel_tripjack_hotel_idx'
            ),
        ]

    def __str__(self):
        return self.name

    @property
    def city_name(self):
        return self.address.city.name if self.address and self.address.city else None

    @property
    def state_name(self):
        return self.address.state.name if self.address and self.address.state else None

    @property
    def country_name(self):
        return self.address.country.name if self.address and self.address.country else None


class Room(BaseModel):
    hotel = models.ForeignKey(Hotel, on_delete=models.CASCADE, related_name="rooms")
    name = models.CharField(max_length=255)
    type = models.CharField(max_length=100, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    price = models.DecimalField(max_digits=10, decimal_places=2)
    max_adults = models.PositiveIntegerField(blank=True, null=True)
    max_children = models.PositiveIntegerField(blank=True, null=True)
    meal_plan = models.CharField(max_length=100, blank=True, null=True)
    occupancy = models.JSONField(default=list, blank=True, null=True)

    meta_info = models.JSONField(default=dict, blank=True, null=True)


class HotelMedia(BaseModel):
    hotel = models.ForeignKey(Hotel, on_delete=models.CASCADE, related_name="media")
    media = models.FileField(upload_to=hotel_image_upload_path)

    def __str__(self):
        return f"Media for Hotel: {self.hotel.name}"


class RoomMedia(BaseModel):
    room = models.ForeignKey(Room, on_delete=models.CASCADE, related_name="media")
    media = models.FileField(upload_to=room_image_upload_path)

    def __str__(self):
        return f"Media for Room: {self.room.name}"


class Facility(BaseModel):
    hotel = models.ForeignKey(Hotel, on_delete=models.CASCADE, related_name="facilities", null=True, blank=True)
    room = models.ForeignKey(Room, on_delete=models.CASCADE, related_name="facilities", null=True, blank=True)
    amenities = ArrayField(models.CharField(max_length=255), default=list, blank=True, null=True)
    type = models.CharField(max_length=50, choices=FacilityTypeChoices.choices)

    class Meta:
        verbose_name = "Facility"
        verbose_name_plural = "Facilities"

    def __str__(self):
        if self.amenities:
            amenities_str = ', '.join(self.amenities[:2])
            if len(self.amenities) > 2:
                amenities_str += f" (+{len(self.amenities)-2} more)"
            return f"{self.type}: {amenities_str}"
        return f"{self.type} Facility"


class HotelContact(BaseModel):
    hotel = models.ForeignKey(Hotel, on_delete=models.CASCADE, related_name="contacts")
    phone = models.CharField(max_length=50, blank=True, null=True)
    email = ArrayField(models.EmailField(), blank=True, null=True)
    fax = models.CharField(max_length=50, blank=True, null=True)
    website = models.URLField(blank=True, null=True)

    def __str__(self):
        return f"Contact for Hotel: {self.hotel.name}"


def hard_delete_hotel_data():
    """
    Hard delete hotel data when the hotel is deleted.
    """
    hotels = Hotel.global_objects.all()
    for hotel in hotels:
        hotel.hard_delete()
    print("🔄 Hard deleted hotel data")

    rooms = Room.global_objects.all()
    for room in rooms:
        room.hard_delete()
    print("🔄 Hard deleted room data")

    facilities = Facility.global_objects.all()
    for facility in facilities:
        facility.hard_delete()
    print("🔄 Hard deleted facility data")

    hotel_contacts = HotelContact.global_objects.all()
    for hotel_contact in hotel_contacts:
        hotel_contact.hard_delete()
    print("🔄 Hard deleted hotel contact data")

    hotel_media = HotelMedia.global_objects.all()
    for hotel_media in hotel_media:
        hotel_media.hard_delete()
    print("🔄 Hard deleted hotel media data")

    room_media = RoomMedia.global_objects.all()
    for room_media in room_media:
        room_media.hard_delete()
    print("🔄 Hard deleted room media data")

    cities = City.global_objects.all()
    for city in cities:
        city.hard_delete()
    print("🔄 Hard deleted city data")

    states = State.global_objects.all()
    for state in states:
        state.hard_delete()
    print("🔄 Hard deleted state data")

    countries = Country.global_objects.all()
    for country in countries:
        country.hard_delete()
    print("🔄 Hard deleted country data")
    hotel_address = HotelAddress.global_objects.all()
    for hotel_address in hotel_address:
        hotel_address.hard_delete()
    print("🔄 Hard deleted hotel address data")
    
    print("🔄 Hard deleted all hotel related data")
